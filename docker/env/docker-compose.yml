version : '3.8'
services:

  #Nacos
  yooa-nacos:
    container_name: yooa-nacos
    image: yooa-nacos
    build:
      context: ./nacos
    environment:
      - MODE=standalone
    volumes:
      - ./nacos/logs:/home/<USER>/logs
      - ./nacos/conf/application.properties:/home/<USER>/conf/application.properties
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"
    # depends_on:
      # - yooa-mysql
      
  #Mysql
  # yooa-mysql:
    # container_name: yooa-mysql
    # image: yooa-mysql
    # build:
      # context: ./mysql
    # ports:
      # - "13306:13306"
    # volumes:
      # - ./mysql/conf:/etc/mysql/conf.d
      # - ./mysql/logs:/logs
      # - ./mysql/data:/var/lib/mysql
    # command: [
          # 'mysqld',
          # '--innodb-buffer-pool-size=80M',
          # '--character-set-server=utf8mb4',
          # '--collation-server=utf8mb4_unicode_ci',
          # '--default-time-zone=+8:00',
          # '--lower-case-table-names=1'
        # ]
    # environment:
      # MYSQL_DATABASE: 'yooa_system'
      # MYSQL_ROOT_PASSWORD: password

  #Redis
  # yooa-redis:
    # container_name: yooa-redis
    # image: yooa-redis
    # build:
      # context: ./redis
    # ports:
      # - "16379:16379"
    # volumes:
      # - ./redis/conf/redis.conf:/home/<USER>/redis/redis.conf
      # - ./redis/data:/data
    # command: redis-server /home/<USER>/redis/redis.conf
    
  #Nginx
  # yooa-nginx:
    # container_name: yooa-nginx
    # image: yooa-nginx
    # build:
      # context: ./nginx
    # ports:
      # - "80:80"
    # volumes:
      # - ./nginx/html/dist:/home/<USER>/projects
      # - ./nginx/conf/nginx.conf:/etc/nginx/nginx.conf
      # - ./nginx/conf.d:/etc/nginx/conf.d
      # - ./nginx/logs:/var/log/nginx
      
  #Zookeeper
  # yooa-zookeeper:
    # container_name: yooa-zookeeper
    # image: yooa-zookeeper
    # build:
      # context: ./zookeeper
    # ports:
      # - '2181:2181'
    # environment:
      # - ALLOW_ANONYMOUS_LOGIN=yes
      
  # #Kafka
  # yooa-kafka:
    # container_name: yooa-kafka
    # image: yooa-kafka
    # build:
      # context: ./kafka
    # ports:
      # - '9092:9092'
    # environment:
      # - KAFKA_BROKER_ID=0
      # - KAFKA_CFG_LISTENERS=PLAINTEXT://:9092
      # - KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://192.168.50.159:9092
      # - KAFKA_CFG_ZOOKEEPER_CONNECT=192.168.50.159:2181
      # - ALLOW_PLAINTEXT_LISTENER=yes
    # depends_on:
      # - yooa-zookeeper
    
