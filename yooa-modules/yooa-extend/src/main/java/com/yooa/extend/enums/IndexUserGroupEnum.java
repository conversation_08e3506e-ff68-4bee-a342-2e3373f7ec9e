package com.yooa.extend.enums;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.yooa.extend.api.domain.vo.IndexUserGroupAllVo;
import com.yooa.extend.api.domain.vo.PeopleRankingVo;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.Comparator;

/**
 * <AUTHOR> xh
 * @Date: 2025/2/28 9:28
 * @Description: 枚举策略类
 */
@Getter
@AllArgsConstructor
public enum IndexUserGroupEnum {

    FRIEND("friend", "好友") {
        @Override
        public BigDecimal getFieldValue(IndexUserGroupAllVo target) {
            return Convert.toBigDecimal(target.getFriend());
        }

        @Override
        public void setUpValue(PeopleRankingVo vo, IndexUserGroupAllVo target) {
            vo.setUpValue(Convert.toBigDecimal(target.getFriend()));
        }

        @Override
        public Comparator<IndexUserGroupAllVo> getComparatorValue(String fieldType) {
            return Comparator.comparingLong(IndexUserGroupAllVo::getFriend);
        }
    },


    REGISTER("register", "注册") {
        @Override
        public BigDecimal getFieldValue(IndexUserGroupAllVo target) {
            return Convert.toBigDecimal(target.getRegister());
        }

        @Override
        public void setUpValue(PeopleRankingVo vo, IndexUserGroupAllVo target) {
            vo.setUpValue(Convert.toBigDecimal(target.getRegister()));
        }

        @Override
        public Comparator<IndexUserGroupAllVo> getComparatorValue(String fieldType) {
            return Comparator.comparingLong(IndexUserGroupAllVo::getRegister);
        }
    },


    QUALITY_USERS("qualityUsers", "优质") {
        @Override
        public BigDecimal getFieldValue(IndexUserGroupAllVo target) {
            return Convert.toBigDecimal(target.getQualityUsers());
        }

        @Override
        public void setUpValue(PeopleRankingVo vo, IndexUserGroupAllVo target) {
            vo.setUpValue(Convert.toBigDecimal(target.getQualityUsers()));
        }

        @Override
        public Comparator<IndexUserGroupAllVo> getComparatorValue(String fieldType) {
            return Comparator.comparingInt(IndexUserGroupAllVo::getQualityUsers);
        }
    },


    RECEIVE("receive", "交接") {
        @Override
        public BigDecimal getFieldValue(IndexUserGroupAllVo target) {
            return Convert.toBigDecimal(target.getReceive());
        }

        @Override
        public void setUpValue(PeopleRankingVo vo, IndexUserGroupAllVo target) {
            vo.setUpValue(Convert.toBigDecimal(target.getReceive()));
        }

        @Override
        public Comparator<IndexUserGroupAllVo> getComparatorValue(String fieldType) {
            return Comparator.comparingLong(IndexUserGroupAllVo::getReceive);
        }
    },


    RECEIVE_VIP("receiveVip", "二交") {
        @Override
        public BigDecimal getFieldValue(IndexUserGroupAllVo target) {
            return Convert.toBigDecimal(target.getReceiveVip());
        }

        @Override
        public void setUpValue(PeopleRankingVo vo, IndexUserGroupAllVo target) {
            vo.setUpValue(Convert.toBigDecimal(target.getReceiveVip()));
        }

        @Override
        public Comparator<IndexUserGroupAllVo> getComparatorValue(String fieldType) {
            return Comparator.comparingInt(IndexUserGroupAllVo::getReceiveVip);
        }
    },


    FIRST_CHARGE("firstCharge", "首充") {
        @Override
        public BigDecimal getFieldValue(IndexUserGroupAllVo target) {
            return Convert.toBigDecimal(target.getFirstCharge());
        }

        @Override
        public void setUpValue(PeopleRankingVo vo, IndexUserGroupAllVo target) {
            vo.setUpValue(Convert.toBigDecimal(target.getFirstCharge()));
        }

        @Override
        public Comparator<IndexUserGroupAllVo> getComparatorValue(String fieldType) {
            return Comparator.comparingLong(IndexUserGroupAllVo::getFirstCharge);
        }
    },


    FORTY_FIVE_DAYS("fortyFiveDays", "新增") {
        @Override
        public BigDecimal getFieldValue(IndexUserGroupAllVo target) {
            return Convert.toBigDecimal(target.getFortyFiveDays());
        }

        @Override
        public void setUpValue(PeopleRankingVo vo, IndexUserGroupAllVo target) {
            vo.setUpValue(Convert.toBigDecimal(target.getFortyFiveDays()));
        }

        @Override
        public Comparator<IndexUserGroupAllVo> getComparatorValue(String fieldType) {
            return Comparator.comparing(IndexUserGroupAllVo::getFortyFiveDays);
        }
    },


    REAL_PROFIT("realProfit", "总业绩") {
        @Override
        public BigDecimal getFieldValue(IndexUserGroupAllVo target) {
            return Convert.toBigDecimal(target.getRealProfit());
        }

        @Override
        public void setUpValue(PeopleRankingVo vo, IndexUserGroupAllVo target) {
            vo.setUpValue(Convert.toBigDecimal(target.getRealProfit()));
        }

        @Override
        public Comparator<IndexUserGroupAllVo> getComparatorValue(String fieldType) {
            return Comparator.comparing(IndexUserGroupAllVo::getRealProfit);
        }
    },


    FIRST_CHARGE_RATE("firstChargeRate", "付费率") {
        @Override
        public BigDecimal getFieldValue(IndexUserGroupAllVo target) {
            return Convert.toBigDecimal(target.getFirstChargeRate());
        }

        @Override
        public void setUpValue(PeopleRankingVo vo, IndexUserGroupAllVo target) {
            vo.setUpValue(Convert.toBigDecimal(target.getFirstChargeRate()));
        }

        @Override
        public Comparator<IndexUserGroupAllVo> getComparatorValue(String fieldType) {
            return Comparator.comparing(IndexUserGroupAllVo::getFirstChargeRate);
        }
    },


    REGISTER_RATE("registerRate", "注册率") {
        @Override
        public BigDecimal getFieldValue(IndexUserGroupAllVo target) {
            return Convert.toBigDecimal(target.getRegisterRate());
        }

        @Override
        public void setUpValue(PeopleRankingVo vo, IndexUserGroupAllVo target) {
            vo.setUpValue(Convert.toBigDecimal(target.getRegisterRate()));
        }

        @Override
        public Comparator<IndexUserGroupAllVo> getComparatorValue(String fieldType) {
            return Comparator.comparing(IndexUserGroupAllVo::getRegisterRate);
        }
    },


    RECEIVE_RATE("receiveRate", "交接率") {
        @Override
        public BigDecimal getFieldValue(IndexUserGroupAllVo target) {
            return Convert.toBigDecimal(target.getReceiveRate());
        }

        @Override
        public void setUpValue(PeopleRankingVo vo, IndexUserGroupAllVo target) {
            vo.setUpValue(Convert.toBigDecimal(target.getReceiveRate()));
        }

        @Override
        public Comparator<IndexUserGroupAllVo> getComparatorValue(String fieldType) {
            return Comparator.comparing(IndexUserGroupAllVo::getReceiveRate);
        }
    },


    RECEIVE_VIP_RATE("receiveVipRate", "二交率") {
        @Override
        public BigDecimal getFieldValue(IndexUserGroupAllVo target) {
            return Convert.toBigDecimal(target.getReceiveVipRate());
        }

        @Override
        public void setUpValue(PeopleRankingVo vo, IndexUserGroupAllVo target) {
            vo.setUpValue(Convert.toBigDecimal(target.getReceiveVipRate()));
        }

        @Override
        public Comparator<IndexUserGroupAllVo> getComparatorValue(String fieldType) {
            return Comparator.comparing(IndexUserGroupAllVo::getReceiveVipRate);
        }
    },


    FANS2H("fans2h", "两百粉") {
        @Override
        public BigDecimal getFieldValue(IndexUserGroupAllVo target) {
            return ObjUtil.isNotNull(target.getFans2h()) ? BigDecimal.valueOf(target.getFans2h()) : BigDecimal.ZERO;
        }

        @Override
        public void setUpValue(PeopleRankingVo vo, IndexUserGroupAllVo target) {
            vo.setUpValue(ObjUtil.isNotNull(target.getFans2h()) ? BigDecimal.valueOf(target.getFans2h()) : BigDecimal.ZERO);
        }

        @Override
        public Comparator<IndexUserGroupAllVo> getComparatorValue(String fieldType) {
            return Comparator.comparingInt(IndexUserGroupAllVo::getFans2h);
        }
    },


    FANS5K("fans5k", "五千粉") {
        @Override
        public BigDecimal getFieldValue(IndexUserGroupAllVo target) {
            return ObjUtil.isNotNull(target.getFans5k()) ? BigDecimal.valueOf(target.getFans5k()) : BigDecimal.ZERO;
        }

        @Override
        public void setUpValue(PeopleRankingVo vo, IndexUserGroupAllVo target) {
            vo.setUpValue(ObjUtil.isNotNull(target.getFans5k()) ? BigDecimal.valueOf(target.getFans5k()) : BigDecimal.ZERO);
        }

        @Override
        public Comparator<IndexUserGroupAllVo> getComparatorValue(String fieldType) {
            return Comparator.comparingInt(IndexUserGroupAllVo::getFans5k);
        }
    },


    FANS5W("fans5w", "五万粉") {
        @Override
        public BigDecimal getFieldValue(IndexUserGroupAllVo target) {
            return ObjUtil.isNotNull(target.getFans5w()) ? BigDecimal.valueOf(target.getFans5w()) : BigDecimal.ZERO;
        }

        @Override
        public void setUpValue(PeopleRankingVo vo, IndexUserGroupAllVo target) {
            vo.setUpValue(ObjUtil.isNotNull(target.getFans5w()) ? BigDecimal.valueOf(target.getFans5w()) : BigDecimal.ZERO);
        }

        @Override
        public Comparator<IndexUserGroupAllVo> getComparatorValue(String fieldType) {
            return Comparator.comparingInt(IndexUserGroupAllVo::getFans5w);
        }
    };



    public abstract BigDecimal getFieldValue(IndexUserGroupAllVo target);

    public abstract void setUpValue(PeopleRankingVo vo, IndexUserGroupAllVo target);

    public abstract Comparator<IndexUserGroupAllVo> getComparatorValue(String fieldType);

    /**
     * 获取中文说明
     */
    public static String getFieldName(String fieldType) {
        IndexUserGroupEnum[] values = IndexUserGroupEnum.values();
        for (IndexUserGroupEnum value : values) {
            if (StrUtil.equals(value.code, fieldType)) {
                return value.msg;
            }
        }
        return "";
    }

    /**
     * 获取参数值
     */
    public static BigDecimal getFieldValue(String fieldType, IndexUserGroupAllVo target) {
        IndexUserGroupEnum[] values = IndexUserGroupEnum.values();
        for (IndexUserGroupEnum value : values) {
            if (StrUtil.equals(value.code, fieldType)) {
                return value.getFieldValue(target);
            }
        }
        return BigDecimal.ZERO;
    }


    /**
     * 设置对比值
     */
    public static void setCompareValue(PeopleRankingVo vo, IndexUserGroupAllVo target){
        IndexUserGroupEnum[] values = IndexUserGroupEnum.values();
        for (IndexUserGroupEnum value : values) {
            if (StrUtil.equals(value.msg, vo.getFieldsName())) {
                value.setUpValue(vo,target);
            }
        }
    }

    /**
     * 获取比较类型
     */
    public static Comparator<IndexUserGroupAllVo> getComparator(String fieldType){
        IndexUserGroupEnum[] values = IndexUserGroupEnum.values();
        for (IndexUserGroupEnum value : values) {
            if (StrUtil.equals(value.code, fieldType)) {
                return value.getComparatorValue(fieldType);
            }
        }
        return null;
    }




    private final String code;
    private final String msg;
}
