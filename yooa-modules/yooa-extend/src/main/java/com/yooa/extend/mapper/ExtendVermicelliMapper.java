package com.yooa.extend.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.crm.api.domain.CrmCustomerJoinServe;
import com.yooa.extend.api.domain.ExtendVermicelli;
import com.yooa.extend.api.domain.dto.CommonalityDto;
import com.yooa.extend.api.domain.dto.ExtendVermicelliDto;
import com.yooa.extend.api.domain.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ExtendVermicelliMapper extends BaseMapper<ExtendVermicelli> {

    /**
     * 以部门结构查询粉丝登记记录
     *
     * @param dto (参数:fields - 9、deptId、beginTime、endTime)
     */
    IndexVo index(@Param("dto") CommonalityDto dto);

    /**
     * 以部门结构查询粉丝登记记录
     *
     * @param dto (参数:deptId、beginTime、endTime)
     */
    IndexAllVo indexAll(@Param("dto") CommonalityDto dto);

    /**
     * 以部门结构查询粉丝登记记录根据时间分组
     *
     * @param dto (参数:expType、fields - 9、deptId、beginTime、endTime)
     */
    List<IndexTimeGroupVo> indexDeptTimeGroup(@Param("dto") CommonalityDto dto);

    /**
     * 以部门结或用户构查询粉丝登记记录根据时间分组
     *
     * @param dto (参数:expType、userIds、deptId、beginTime、endTime)
     */
    List<IndexTimeGroupAllVo> indexUserTimeGroupAll(@Param("dto") CommonalityDto dto);

    /**
     * 以部门结构查询粉丝登记记录根据用户分组
     *
     * @param dto (参数:fields - 9、deptId、beginTime、endTime)
     */
    List<IndexUserGroupVo> indexUserGroup(@Param("dto") CommonalityDto dto);

    /**
     * 以部门结构查询粉丝登记记录
     *
     * @param dto (参数:userId、beginTime、endTime)
     */
    IndexUserGroupAllVo indexUserAll(@Param("dto") CommonalityDto dto);

    /**
     * 以部门结构查询粉丝登记记录列表
     *
     * @param dto (参数:userId、beginTime、endTime)
     */
    List<ExtendVermicelli> indexUserAllVermicelli(@Param("dto") CommonalityDto dto);

    /**
     * 以用户结构查询粉丝登记记录根据用户分组
     *
     * @param dto (参数:beginTime、endTime)
     */
    List<IndexUserGroupAllVo> selPeopleRanking(@Param("dto") CommonalityDto dto);

    /**
     * 以部门结构查询粉丝登记记录根据部门分组
     *
     * @param dto (参数:hierarchy、beginTime、endTime)
     */
    List<IndexDeptGroupAllVo> indexDeptGroupAll(@Param("dto") CommonalityDto dto);

    /**
     * 以部门结构查询粉丝登记记录根据部门分组
     *
     * @param dto (参数:fields - 9、hierarchy、beginTime、endTime)
     */
    List<IndexDeptGroupVo> indexDeptGroup(@Param("dto") CommonalityDto dto);

    List<AnchorOperateVo> selectAnchorOperateVoList();

    /**
     * 查询粉丝登记(权限)
     *
     * @return
     */
    List<ExtendVermicelliVo> selVermicelliListLimits(Page<ExtendVermicelliVo> page,
                                                     @Param("query") ExtendVermicelliDto query,
                                                     @Param("userIds") List<Long> userIds,
                                                     @Param("bl") boolean bl);

    /**
     * 导出粉丝登记(权限)(跟上面的查询sql一直只是映射的map不一样)
     *
     * @return
     */
    List<ExtendVermicelliVo> exportVermicelliListLimits(Page<ExtendVermicelliVo> page,
                                                        @Param("query") ExtendVermicelliDto query,
                                                        @Param("userIds") List<Long> userIds,
                                                        @Param("bl") boolean bl);

    /**
     * 查询客户昵称
     *
     * @param ids
     * @return
     */
    List<ExtendVermicelliVo> selectVermicelliCustomerNickName(@Param("ids") List<Long> ids);

    List<CrmCustomerJoinServe> selCrmCustomerJoinServe();
}




