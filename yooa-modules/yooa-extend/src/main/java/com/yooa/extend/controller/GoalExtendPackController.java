package com.yooa.extend.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.constant.DictConstants;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.extend.api.domain.GoalExtendPack;
import com.yooa.extend.api.domain.dto.GoalExtendPackQueryDto;
import com.yooa.extend.service.GoalExtendPackService;
import com.yooa.extend.service.TargetPlanChangeRecordService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@AllArgsConstructor
@RestController
@RequestMapping("/goalExtendPack")
public class GoalExtendPackController extends BaseController {

    private final GoalExtendPackService goalExtendPackService;
    private final TargetPlanChangeRecordService targetPlanChangeRecordService;

    @GetMapping("/list/{type}")
    public AjaxResult list(@PathVariable String type, Page<GoalExtendPack> page, GoalExtendPackQueryDto query) {
        // 查询我审批的
        if ("1".equals(type)) {
            query.setAuditPeopleId(SecurityUtils.getUserId());
            query.setAuditStatusList(List.of(DictConstants.EXAMINE_STATUS_AWAIT, DictConstants.EXAMINE_STATUS_PASS, DictConstants.EXAMINE_STATUS_NOT_PASS));
        }
        // 查询我申请的
        else if ("2".equals(type)) {
            query.setProducerId(SecurityUtils.getUserId());
            query.setAuditStatus(null);
        }
        return AjaxResult.success(page.setRecords(goalExtendPackService.getGoalExtendPackList(page, query)));
    }

    @GetMapping("/reject-num")
    public AjaxResult rejectNum() {
        // 查询我申请的被驳回数量
        List<GoalExtendPack> goalExtendPackList = goalExtendPackService.list(
                Wrappers.<GoalExtendPack>lambdaQuery()
                        .eq(GoalExtendPack::getProducerId, SecurityUtils.getUserId())
                        .eq(GoalExtendPack::getAuditStatus, DictConstants.EXAMINE_STATUS_NOT_PASS)
        );
        return AjaxResult.success(CollUtil.isEmpty(goalExtendPackList) ? 0 : goalExtendPackList.size());
    }

    @GetMapping("/info")
    public AjaxResult info(GoalExtendPackQueryDto query) {
        GoalExtendPack goalExtendPack = goalExtendPackService.getOne(
                Wrappers.<GoalExtendPack>lambdaQuery()
                        .eq(query.getTableType() == 0, GoalExtendPack::getTableType, query.getTableType())
                        .eq(GoalExtendPack::getProducerId, SecurityUtils.getUserId())
                        .orderByDesc(GoalExtendPack::getCreateTime)
                        .last(" limit 1")
        );
        return success(goalExtendPack);
    }

    @GetMapping("/total/{type}")
    public AjaxResult total(@PathVariable String type) {
        List<GoalExtendPack> packList = new ArrayList<>();
        if ("1".equals(type)) {
            packList = goalExtendPackService.list(
                    Wrappers.<GoalExtendPack>lambdaQuery()
                            .eq(GoalExtendPack::getAuditPeopleId, SecurityUtils.getUserId())
                            .eq(GoalExtendPack::getAuditStatus, DictConstants.EXAMINE_STATUS_AWAIT)
            );
        }
        else if ("2".equals(type)) {
            packList = goalExtendPackService.list(
                    Wrappers.<GoalExtendPack>lambdaQuery()
                            .eq(GoalExtendPack::getProducerId, SecurityUtils.getUserId())
                            .in(GoalExtendPack::getAuditStatus, DictConstants.EXAMINE_STATUS_SAVE, DictConstants.EXAMINE_STATUS_AWAIT)
            );
        }

        Map<String, Long> statisticsMap = new HashMap<>();
        statisticsMap.put("year", packList.stream().filter(p -> p.getTableType().equals(0)).count());
        statisticsMap.put("month", packList.stream().filter(p -> p.getTableType().equals(1)).count());
        return AjaxResult.success(statisticsMap);
    }

    @PutMapping("/revoke/{packId}")
    public AjaxResult revoke(@PathVariable Long packId) {
        GoalExtendPack goalExtendPack = goalExtendPackService.getById(packId);
        if (ObjUtil.isEmpty(goalExtendPack)) {
            return warn("异常数据，追回失败");
        }

        if (!goalExtendPack.getProducerId().equals(SecurityUtils.getUserId())) {
            return warn("不是您的目标计划无权限操作");
        }

        if (!goalExtendPack.getAuditStatus().equals(DictConstants.EXAMINE_STATUS_AWAIT)) {
            return warn("状态异常，追回失败");
        }

        goalExtendPack.setAuditStatus(DictConstants.EXAMINE_STATUS_SAVE);
        if (goalExtendPackService.updateById(goalExtendPack)) {
            targetPlanChangeRecordService.save(packId, String.valueOf(DictConstants.EXAMINE_STATUS_REVOKE), null);
        }
        return success();
    }


    /**
     * 审批
     *
     * @param pack 对象
     */
    @PutMapping("/updPack")
    public AjaxResult updPack(@RequestBody GoalExtendPack pack) {
        if (goalExtendPackService.updateById(pack)) {
            targetPlanChangeRecordService.save(pack.getId().longValue(), String.valueOf(pack.getAuditStatus()), pack.getReason());
        }
        return success();
    }
}
