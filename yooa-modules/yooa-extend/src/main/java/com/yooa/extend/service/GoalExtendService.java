package com.yooa.extend.service;

import com.yooa.extend.api.domain.GoalExtendPack;
import com.yooa.extend.api.domain.GoalFields;
import com.yooa.extend.api.domain.GoalGroupFields;
import com.yooa.extend.api.domain.dto.CommonalityDto;
import com.yooa.extend.api.domain.dto.ExamineGoalDto;
import com.yooa.extend.api.domain.dto.UpdGoalExtendMonthDto;
import com.yooa.extend.api.domain.dto.UpdGoalExtendWeeksDto;
import com.yooa.extend.api.domain.vo.*;
import com.yooa.system.api.domain.SysUser;
import com.yooa.system.api.domain.vo.SysUserVo;

import java.util.List;
import java.util.Map;

/**
 *
 */
public interface GoalExtendService {

    // 制定目标字段
    int setTargetFields(GoalFields goalFields);

    // 删除已制定目标字段(目标字段已被使用不能删除!)
    int delTargetFields(GoalFields goalFields);

    // 查询制定目标字段
    List<GoalFields> inquireTargetFields(GoalFields goalFields);

    // 新增目标字段分组
    int addGroupFields(GoalGroupFields groupFields);

    // 查询目标字段分组
    List<GoalGroupFieldsVo> selGroupFields(GoalGroupFields groupFields);

    // 新增周计划目标
    int updWeeksGoal(SysUserVo sysUser, UpdGoalExtendWeeksDto updGoalExtendWeeksDto);

    // 新增月计划目标
    int updMonthGoal(SysUserVo sysUser, UpdGoalExtendMonthDto updGoalExtendMonthDto);

    // 查询自己的周计划列表
    ResultGoalWeeksVo selWeeksGoal(CommonalityDto commonalityDto);

    // 查询自己的月计划列表
    ResultGoalMonthVo selMonthGoal(CommonalityDto commonalityDto);

    // 查看计划详情
    List<?> selGoalDetails(Integer goalId);

    // 查询年计划进度
    List<ScheduleVo> selYearsSchedule(CommonalityDto commonalityDto);

    // 查询年计划进度(多条)
    Map<Integer, List<ScheduleVo>> selYearsScheduleList(CommonalityDto commonalityDto);

    // 查询计划审核状态
    Map<Integer, String> selPackAuditStatus(Integer tableType, CommonalityDto commonalityDto);

    // 查询月计划进度
    Map<String, List<ScheduleVo>> selMonthScheduleList(CommonalityDto commonalityDto);

    // 查询月计划进度
    List<ScheduleVo> selMonthSchedule(CommonalityDto commonalityDto);

    // 查询自己的审批计划列表
    List<GoalExtendPack> examineGoalList(SysUser sysUser, ExamineGoalDto examineGoalDto);

    // 查询下级的计划数和计划通过数
    String juniorGoalNumber(SysUser sysUser, int type);

    // 提交团队计划
    int submitTeamGoal(SysUserVo userVo, int type);

    // 定时任务每月去下发周计划
    void weeksGoal();

    // 定时任务每年去下发月计划
    void yearsGoal();

    // 查询分组下发字段
    List<GoalGroupFields> selGoalGroupFieldsList(GoalGroupFields groupFields);

    // 查询目标字段
    List<GoalFields> selGoalFields(GoalFields fields);

    //统计月计划年计划数量
    PlanCountVo getPlanCountVo();
}
