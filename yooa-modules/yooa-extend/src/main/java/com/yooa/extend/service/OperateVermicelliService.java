package com.yooa.extend.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.extend.api.domain.OperateVermicelli;
import com.yooa.extend.api.domain.dto.OperateVermicelliDto;
import com.yooa.extend.api.domain.vo.OperateVermicelliVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【operate_vermicelli(运营充值达成_粉丝登记列表)】的数据库操作Service
 * @createDate 2025-03-27 14:03:23
 */
public interface OperateVermicelliService extends IService<OperateVermicelli> {

    // 查询粉丝登记列表(权限(下级))
    List<OperateVermicelliVo> selOperateVermicelliListLimits(Page<OperateVermicelliVo> page, OperateVermicelliDto query, boolean bl);

    // 查询粉丝登记列表(权限(自己))
    List<OperateVermicelliVo> selOperateVermicelliListLimitsOneself(Page<OperateVermicelliVo> page, OperateVermicelliDto query, boolean bl);

    // 查询粉丝登记列表
    List<OperateVermicelli> selOperateVermicelliList(List<Long> userIds, Integer fieldType, Long customerId, List<Long> friendIds, String beginTime, String endTime);

}
