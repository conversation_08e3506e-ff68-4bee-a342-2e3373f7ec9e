package com.yooa.extend.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yooa.common.core.constant.DictConstants;
import com.yooa.common.datascope.annotation.DataScope;
import com.yooa.crm.api.RemoteFriendService;
import com.yooa.extend.api.domain.dto.ExtendDetailedDto;
import com.yooa.extend.api.domain.vo.DetailedVo;
import com.yooa.extend.mapper.ExtendTargetProgressMapper;
import com.yooa.extend.mapper.ExtendVermicelliMapper;
import com.yooa.extend.service.VipService;
import com.yooa.system.api.RemoteDeptService;
import com.yooa.system.api.RemoteUserService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 *
 */
@AllArgsConstructor
@Service
public class VipServiceImpl implements VipService {

    private final ExtendTargetProgressMapper extendTargetProgressMapper;
    private final ExtendVermicelliMapper vermicelliMapper;
    private final RemoteFriendService remoteFriendService;
    private final RemoteDeptService remoteDeptService;
    private final RemoteUserService remoteUserService;

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public IPage<DetailedVo> selectDataVipDetailed(Page<?> page, ExtendDetailedDto detailedDto) {
        IPage<DetailedVo> iPage = null;                                         // 最终返回
        List<DetailedVo> vipDetailedVoList = CollUtil.newArrayList();           // 查询数据汇总
        detailedDto.setSelType(DictConstants.SYS_DEPT_TYPE_VIP_SERVICE);        // 查询VIP数据

        if (detailedDto.getType() == 5) {                                       // 查个人的
            iPage = extendTargetProgressMapper.detailedUserLimits(page, detailedDto);

            if (CollUtil.isEmpty(iPage.getRecords())) {
                return iPage;
            }
            List<Long> pdUserIds = iPage.getRecords().stream()
                    .filter(u -> StrUtil.isNotEmpty(u.getPdUserIds()))
                    .flatMap(u -> Arrays.stream(u.getPdUserIds().split(","))).map(Long::parseLong).collect(Collectors.toList());
            vipDetailedVoList = extendTargetProgressMapper.ExtendDetailedUserData(
                    detailedDto,
                    pdUserIds.stream().map(String::valueOf).collect(Collectors.joining(",")),
                    iPage.getRecords().stream().map(DetailedVo::getId).toList().stream().map(String::valueOf).collect(Collectors.joining(",")));       // 查数据汇总
            ObjectMapper objectMapper = new ObjectMapper();
            vipDetailedVoList.forEach(d -> {
                if (StrUtil.isNotEmpty(d.getOrderInfo())) {
                    try {
                        // 分解订单字符串
                        Map<String, Object> orderMap = objectMapper.readValue(d.getOrderInfo(), Map.class);
                        d.setChargeUser((Integer) orderMap.get("charge_user"));
                        d.setFortyFiveDays(new BigDecimal(orderMap.get("order_new_money").toString()));
                        d.setRealProfit(new BigDecimal(orderMap.get("order_money").toString()));
                    } catch (JsonProcessingException e) {
                        throw new RuntimeException(e);
                    }
                } else {
                    d.setChargeUser(0);
                    d.setFortyFiveDays(BigDecimal.ZERO);
                    d.setRealProfit(BigDecimal.ZERO);
                }
            });
        } else {                                   // 查部门的
            // 设置查询层级(主管看小组这样推)
            Integer hierarchy = detailedDto.getType() == 4 ? 5 : detailedDto.getType() == 3 ? 4 : detailedDto.getType() == 2 ? 3 : 2;
            detailedDto.setHierarchy(hierarchy);
            iPage = extendTargetProgressMapper.ExtendDetailedDeptLimits(page, detailedDto);       // 查数据汇总

            if (CollUtil.isEmpty(iPage.getRecords())) {
                return iPage;
            }

            vipDetailedVoList = extendTargetProgressMapper.ExtendDetailedDeptData(
                    detailedDto,
                    iPage.getRecords().stream().map(DetailedVo::getId).collect(Collectors.toList()).stream().map(String::valueOf).collect(Collectors.joining(",")));       // 查数据汇总
            ObjectMapper objectMapper = new ObjectMapper();
            vipDetailedVoList.forEach(d -> {
                try {
                    // 分解粉丝登记字符串
                    if (StrUtil.isNotEmpty(d.getFansInfo())) {
                        Map<String, Integer> fansMap = objectMapper.readValue(d.getFansInfo(), Map.class);
                        d.setFans2h(fansMap.get("fans2h"));
                        d.setFans5h(fansMap.get("fans5h"));
                        d.setFans5k(fansMap.get("fans5k"));
                        d.setFans5kNew(fansMap.get("fans5kNew"));
                        d.setFans5w(fansMap.get("fans5w"));
                        d.setFirstCharge(fansMap.get("firstCharge").longValue());
                    } else {
                        d.setFans2h(0);
                        d.setFans5h(0);
                        d.setFans5k(0);
                        d.setFans5kNew(0);
                        d.setFans5w(0);
                        d.setFirstCharge(0L);
                    }
                    // 分解订单字符串
                    if (StrUtil.isNotEmpty(d.getOrderInfo())) {
                        Map<String, Object> orderMap = objectMapper.readValue(d.getOrderInfo(), Map.class);
                        d.setChargeUser((Integer) orderMap.get("charge_user"));
                        d.setFortyFiveDays(new BigDecimal(orderMap.get("order_new_money").toString()));
                        d.setRealProfit(new BigDecimal(orderMap.get("order_money").toString()));
                    } else {
                        d.setChargeUser(0);
                        d.setFortyFiveDays(BigDecimal.ZERO);
                        d.setRealProfit(BigDecimal.ZERO);
                    }
                } catch (JsonProcessingException e) {
                    throw new RuntimeException(e);
                }

            });
        }

        for (DetailedVo p : iPage.getRecords()) {
            DetailedVo vo = vipDetailedVoList.stream().filter(e -> e.getId().equals(p.getId())).findFirst().orElse(null);

            if (ObjUtil.isNotNull(vo)) {
                p.setFans2h(vo.getFans2h());
                p.setFans5h(vo.getFans5h());
                p.setFans5k(vo.getFans5k());
                p.setFans5kNew(vo.getFans5kNew());
                p.setFans5w(vo.getFans5w());
                p.setFirstCharge(vo.getFirstCharge());
                p.setChargeUser(vo.getChargeUser());
                p.setRealProfit(vo.getRealProfit());
                p.setFortyFiveDays(vo.getFortyFiveDays());
                p.setReceiveVip(vo.getReceiveVip());
                p.setQualityUsers(vo.getQualityUsers());
            }
        }

        List<Long> userIds = CollUtil.newArrayList();
        for (DetailedVo v : iPage.getRecords()) {
            // 拆分出部门-团队-小组
            List<String> stringList = Arrays.stream(v.getAncestorsNames().split("-")).collect(Collectors.toList());      // 拆分部门名
            int size = stringList.size() - 2;       // 要截取的
            if (size == 1) {
                v.setTeamName1(stringList.get(stringList.size() - 1));
            } else if (size == 2) {
                v.setTeamName1(stringList.get(stringList.size() - 2));
                v.setTeamName2(stringList.get(stringList.size() - 1));
            } else if (size == 3) {
                v.setTeamName1(stringList.get(stringList.size() - 3));
                v.setTeamName2(stringList.get(stringList.size() - 2));
                v.setTeamName3(stringList.get(stringList.size() - 1));
            }

            if (ObjUtil.isNotNull(v.getLeaderId()) && ObjUtil.isNull(v.getLeaderName())) {
                userIds.add(v.getLeaderId());
            }
        }

        return iPage;
    }

}
