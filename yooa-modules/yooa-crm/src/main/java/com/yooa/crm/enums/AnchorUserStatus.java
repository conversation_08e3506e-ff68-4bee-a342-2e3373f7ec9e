package com.yooa.crm.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR> xh
 * @Date: 2025/7/10 16:38
 * @Description:
 */
@Getter
@AllArgsConstructor
public enum AnchorUserStatus {

    NORMAL(0,"正常"),
    NOT_OPEN(1,"未开"),
    OFF_AIR_BROADCAST(2,"休播"),
    SELF_SEPARATION(3,"自离"),
    ELIMINATE(4,"淘汰"),
    WAIT_LIVE(5,"待停播");


    private final Integer code;
    private final String desc;
}
