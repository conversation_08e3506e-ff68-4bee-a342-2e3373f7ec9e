package com.yooa.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yooa.crm.api.domain.CrmAnchorLiveRecord;
import com.yooa.crm.api.domain.dto.AnchorLiveTimeDto;
import com.yooa.crm.api.domain.vo.AnchorVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR> xh
 * @Date: 2025/3/4 17:33
 * @Description:
 */
public interface CrmAnchorLiveRecordMapper extends BaseMapper<CrmAnchorLiveRecord> {

    /**
     * 查询今日时长 当月时长 总时长汇总
     * @param anchorId 主播 id
     * @return
     */
    AnchorLiveTimeDto getAnchorLiveListByAnchorId(@Param("anchorId")Long anchorId);

    Set<Long> getAnchorIdsByStartTimeAndEndTime(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("ids") List<Long> anchorVoList);
}
