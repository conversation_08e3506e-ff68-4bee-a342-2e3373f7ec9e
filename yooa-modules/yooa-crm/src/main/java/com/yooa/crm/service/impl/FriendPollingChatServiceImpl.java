package com.yooa.crm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.crm.api.domain.CrmFriendPollingChat;
import com.yooa.crm.mapper.CrmFriendPollingChatMapper;
import com.yooa.crm.service.FriendChannelService;
import com.yooa.crm.service.FriendPollingChatService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 好友渠道管理 - 服务实现层
 */
@AllArgsConstructor
@Service
public class FriendPollingChatServiceImpl extends ServiceImpl<CrmFriendPollingChatMapper, CrmFriendPollingChat> implements FriendPollingChatService {


}
