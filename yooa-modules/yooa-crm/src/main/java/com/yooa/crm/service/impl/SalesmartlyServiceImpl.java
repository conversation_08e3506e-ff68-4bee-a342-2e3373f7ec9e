package com.yooa.crm.service.impl;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mashape.unirest.http.exceptions.UnirestException;
import com.yooa.common.core.constant.DictConstants;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.domain.R;
import com.yooa.common.core.utils.LocalDateUtil;
import com.yooa.common.core.utils.SaleSmartlyClientUtils;
import com.yooa.crm.api.domain.CrmFriend;
import com.yooa.crm.api.domain.CrmFriendPollingChat;
import com.yooa.crm.api.domain.vo.WebHookChatVo;
import com.yooa.crm.api.domain.vo.WebHookUserVo;
import com.yooa.crm.config.SaleSmartlyConfig;
import com.yooa.crm.mapper.CrmFriendMapper;
import com.yooa.crm.mapper.CrmFriendPollingChatMapper;
import com.yooa.crm.service.FriendChannelService;
import com.yooa.crm.service.FriendPollingChatService;
import com.yooa.crm.service.SalesmartlyService;
import com.yooa.external.api.RemoteSaleSmartlyService;
import com.yooa.system.api.RemoteFileService;
import com.yooa.system.api.RemoteUserService;
import com.yooa.system.api.domain.SysUserPd;
import com.yooa.system.api.model.LoginUser;
import lombok.AllArgsConstructor;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

@AllArgsConstructor
@Service
@Slf4j
public class SalesmartlyServiceImpl implements SalesmartlyService {
    private final RemoteUserService remoteUserService;
    private final CrmFriendMapper friendMapper;
    private final FriendChannelService friendChannelService;
    private final CrmFriendPollingChatMapper friendPollingChatMapper;
    private final FriendPollingChatService friendPollingChatService;
    private final RemoteFileService remoteFileService;
    private final RemoteSaleSmartlyService remoteSalesmartlyService;

    @Override
    @Synchronized
    public void webHook(SaleSmartlyConfig.config configVo, Map<String, Object> payload) throws UnirestException, IOException {
//        log.info("webhookMap:{}", payload);

//            Map<String, Object> paramsMap = JSON.parseObject(dataString);
//            // 验证签名
//            String sign = SaleSmartlyClientUtils.getSign(configVo.getWebhookToken(), paramsMap);
//            if (!sign.equals(signature)) {
//                log.error("webhook签名未通过");
//                throw new ServiceException("webhook签名未通过");
//            }
        // 获取事件类型
        String event = payload.get("event").toString();
        Map<String, Object> data = JSON.parseObject(payload.get("data").toString(), Map.class);

        // 客户消息
        if ("update_user".equals(event) || "add_user".equals(event)) {
            WebHookUserVo wbHookUserVo = new WebHookUserVo();
            wbHookUserVo.setChat_user_id(data.get("chat_user_id").toString());
            wbHookUserVo.setName(data.get("name").toString());
            wbHookUserVo.setRemark_name((data.get("remark_name").toString()));
            wbHookUserVo.setPhone(data.get("phone").toString());
            wbHookUserVo.setEmail(data.get("email").toString());
            wbHookUserVo.setRemark(data.get("remark").toString());
            if (StringUtils.isNotEmpty(data.get("sys_user_id").toString())) {
                wbHookUserVo.setSys_user_id(Integer.valueOf(data.get("sys_user_id").toString()));
            }

            if (Objects.isNull(wbHookUserVo) || wbHookUserVo.getSys_user_id() == null) {
                return;
            }
            else {
                handleWebhook(wbHookUserVo, configVo);
            }
        }
        else if ("message".equals(event)) {

            String msg_type = data.get("msg_type").toString();
            if ("text".equals(msg_type) || "image".equals(msg_type) || "file".equals(msg_type)) {
                WebHookChatVo webHookChatVo = new WebHookChatVo();
                webHookChatVo.setMsg_type(msg_type);
                webHookChatVo.setChat_user_id(data.get("chat_user_id").toString());
                webHookChatVo.setSequence_id(Long.valueOf(data.get("sequence_id").toString()));
                webHookChatVo.setSend_time(data.get("send_time").toString());
                webHookChatVo.setMsg(data.get("msg").toString());
                webHookChatVo.setSender_type(data.get("sender_type").toString());
                webHookChatVo.setSender(data.get("sender").toString());
                handleWebhookChat(webHookChatVo, configVo);
            }
            else {
                return;
            }
        }
    }

    public void handleWebhook(WebHookUserVo wbHookUserVo, SaleSmartlyConfig.config configVo) throws UnirestException {
        log.info("userId:{}", wbHookUserVo.getSys_user_id());

        // 通过团队管理成员获取投手、推广id
        Integer sysUserId = wbHookUserVo.getSys_user_id();
        String sysUserName = getMember(sysUserId, wbHookUserVo, configVo);
//        String sysUserName = "西瓜/123";
        Long pitcherId = null; // 投手id
        Long extendId = null; // 推广id
        if (sysUserName != null && sysUserName.contains("/")) {
            String[] parts = sysUserName.split("/");
            if (parts.length <= 1) {
                log.error("用户备注格式异常：{}", sysUserName);
                return;
            }
            R<LoginUser> userResult = remoteUserService.getUserInfo(parts[0], SecurityConstants.INNER);
            if (Objects.nonNull(userResult.getData())) {
                pitcherId = userResult.getData().getSysUser().getUserId();
            }
            // 判断parts[1]是否为long类型
            if (parts[1].matches("\\d+")) {
                R<SysUserPd> pdUser = remoteUserService.getUserByPdId(Long.valueOf(parts[1]), SecurityConstants.INNER);
                if (Objects.nonNull(pdUser.getData())) {
                    extendId = pdUser.getData().getUserId();
                }
            }
        }
        if (pitcherId == null || extendId == null) {
            log.error("用户无投手或推广");
            return;
        }
        // 获取客户基本信息
        JSONObject userData = userData(wbHookUserVo, configVo);
        if (Objects.nonNull(userData)) {
            String chatUserId = userData.getString("chat_user_id");
//            CrmFriend friend = friendMapper.selectOne(new QueryWrapper<CrmFriend>().eq("polling_api_user_id", chatUserId));
            boolean isAdd = true;
            List<CrmFriend> friendList = friendMapper.selectList(new QueryWrapper<CrmFriend>()
                    .eq("polling_api_user_id", chatUserId)
                    .eq("pitcher_id", pitcherId)
                    .eq("extend_id", extendId)
                    .isNull("pitcher_id")
                    .isNull("extend_id")
            );
            CrmFriend friend = new CrmFriend();

            for (CrmFriend crmFriend : friendList) {
                Long crmPitcherId = crmFriend.getPitcherId();
                Long crmExtendId = crmFriend.getExtendId();
                if (Objects.equals(crmPitcherId, pitcherId) && Objects.equals(crmExtendId, extendId)) {
                    friend = crmFriend;
                    isAdd = false;
                    break;
                }
            }
            // 新增
            if (isAdd) {
                friend.setStatus(DictConstants.CUSTOMER_STATUS_RECEIVED);
                // 渠道
                String channel = configVo.getChannel();
                // 轮询的主渠道和子渠道是固定的
                if ("FB".equals(channel)) {
                    friend.setMainChannelId(1L);
                    friend.setSubChannelId(17L);
                }
                if ("TK".equals(channel)) {
                    friend.setMainChannelId(2L);
                    friend.setSubChannelId(18L);
                }

                friend.setPitcherId(pitcherId);
                friend.setExtendId(extendId);
                friend.setPollingType(1L);
                friend.setType(2L);
                friend.setReceiveNumber(1);
                friend.setFriendCode(UUID.randomUUID().toString());                // 好友编码,确认唯一性,用于确认领取的好友分支
                if (StringUtils.isNotEmpty(userData.get("created_time").toString())) {
                    Integer createdTime = Convert.toInt(userData.get("created_time"));
                    // 时间戳格式单位为秒，转为yyyy-mm-dd的LocalDate格式
                    LocalDate localDate = LocalDateUtil.epochSecondToLocalDate(createdTime);
                    friend.setRecordDate(localDate);
                }
            }
            friend.setPollingApiUserId(chatUserId);
            friend.setFriendName(StringUtils.isNotEmpty(userData.get("name").toString()) ? userData.get("name").toString() : friend.getFriendName());
            friend.setPollingApiChannel(StringUtils.isNotEmpty(userData.get("channel").toString()) ? userData.getInteger("channel") : friend.getPollingApiChannel());
            friend.setPollingApiPhone(StringUtils.isNotEmpty(userData.get("phone").toString()) ? userData.get("phone").toString() : friend.getPollingApiPhone());
            if (StringUtils.isNotEmpty(userData.get("language").toString())) {
                if ((userData.get("language").toString().contains("cn"))) {
                    friend.setLanguage(1);
                }
                else if ((userData.get("language").toString().contains("us"))) {
                    friend.setLanguage(2);
                }
            }
            friend.setPollingRemark(StringUtils.isNotEmpty(userData.get("remark").toString()) ? userData.get("remark").toString() : friend.getPollingRemark());


            if (isAdd) {
                friendMapper.insert(friend);
            }
            else {
                friendMapper.updateById(friend);
            }
        }
    }

    public String getMember(Integer sysUserId, WebHookUserVo wbHookUserVo, SaleSmartlyConfig.config configVo) throws UnirestException {
        // 初始页码
        int page = 1;
        // 每页大小
        int pageSize = 100;
        // 标记是否找到匹配用户
        boolean userFound = false;
        // 存储找到的用户数据
        JSONObject matchedUser = null;
        String memberName = null;

        // 循环直到找到用户或没有更多数据
        while (!userFound) {
            // 构建请求参数
            Map<String, Object> memberParams = new HashMap<>();
            memberParams.put("project_id", configVo.getProjectId());
            memberParams.put("page", page);
            memberParams.put("page_size", pageSize);
            memberParams.put("updated_time", SaleSmartlyClientUtils.updatedTime());
            String signature = SaleSmartlyClientUtils.getSign(configVo.getApiToken(), memberParams);

            R<JSONObject> getMemberList = remoteSalesmartlyService.getMemberList(configVo.getProjectId(), page, pageSize, SaleSmartlyClientUtils.updatedTime(), signature);
            JSONObject memberJson = getMemberList.getData();
            // 获取会员列表
            JSONArray memberArrayList = memberJson.getJSONArray("list");

            // 如果列表为空，说明没有更多数据了
            if (memberArrayList == null || memberArrayList.isEmpty()) {
                break;
            }
            // 遍历当前页的会员数据
            for (int i = 0; i < memberArrayList.size(); i++) {
                JSONObject member = memberArrayList.getJSONObject(i);
                Integer memberUserId = member.getInteger("sys_user_id");
                // 检查sys_user_id是否匹配
                if (sysUserId.equals(memberUserId)) {
                    matchedUser = member;
                    userFound = true;
                    break;
                }
            }
            // 如果没找到，继续下一页
            if (!userFound) {
                page++;
            }
        }
        if (matchedUser != null) {
            memberName = matchedUser.getString("nickname");
        }
        return memberName;
    }

    public JSONObject userData(WebHookUserVo wbHookUserVo, SaleSmartlyConfig.config configVo) throws UnirestException {
        Map<String, Object> memberParams = new HashMap<>();
        memberParams.put("project_id", configVo.getProjectId());
        memberParams.put("page_size", 100);
        memberParams.put("ids", wbHookUserVo.getChat_user_id());
        memberParams.put("updated_time", SaleSmartlyClientUtils.updatedTime());
        String signature = SaleSmartlyClientUtils.getSign(configVo.getApiToken(), memberParams);
        R<JSONObject> getContactList = remoteSalesmartlyService.getContactList(configVo.getProjectId(), 100, wbHookUserVo.getChat_user_id(), SaleSmartlyClientUtils.updatedTime(), signature);
        JSONObject memberJson = getContactList.getData();
        // 获取会员列表
        JSONArray userList = memberJson.getJSONArray("list");
        if (userList == null || userList.isEmpty()) {
            return new JSONObject();
        }
        else {
            return userList.getJSONObject(0);
        }
    }

    /**
     * 消息数据
     */
    public void handleWebhookChat(WebHookChatVo webHookChatVo, SaleSmartlyConfig.config configVo) throws UnirestException, IOException {
        CrmFriendPollingChat addCrmFriendPollingChat = new CrmFriendPollingChat();
        addCrmFriendPollingChat.setPollingApiUserId(webHookChatVo.getChat_user_id());
        addCrmFriendPollingChat.setSequenceId(webHookChatVo.getSequence_id());
        addCrmFriendPollingChat.setText(webHookChatVo.getMsg());
//        addCrmFriendPollingChat.setContent(webHookChatVo.getMsg());
        String msgType = null;
        if ("text".equals(webHookChatVo.getMsg_type())) {
            msgType = "1";
        }
        else if ("image".equals(webHookChatVo.getMsg_type())) {
            msgType = "2";
        }
        else if ("file".equals(webHookChatVo.getMsg_type())) {
            msgType = "4";
        }
        addCrmFriendPollingChat.setMsgType(msgType);
        addCrmFriendPollingChat.setSenderType(webHookChatVo.getSender_type());
        addCrmFriendPollingChat.setSenderId(webHookChatVo.getSender());
        long sendTimeSLong = Long.parseLong(webHookChatVo.getSend_time());
        Instant instant = Instant.ofEpochMilli(sendTimeSLong);
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        addCrmFriendPollingChat.setSendTime(dateTime);
        friendPollingChatService.save(addCrmFriendPollingChat);
    }

}
