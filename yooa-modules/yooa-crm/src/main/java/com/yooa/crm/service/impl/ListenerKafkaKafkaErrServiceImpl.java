package com.yooa.crm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.crm.api.domain.CrmListenerKafkaErr;
import com.yooa.crm.mapper.CrmListenerKafkaErrMapper;
import com.yooa.crm.service.ListenerKafkaErrService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> xh
 * @Date: 2025/3/12 10:46
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class ListenerKafkaKafkaErrServiceImpl extends ServiceImpl<CrmListenerKafkaErrMapper, CrmListenerKafkaErr>
        implements ListenerKafkaErrService {
}
