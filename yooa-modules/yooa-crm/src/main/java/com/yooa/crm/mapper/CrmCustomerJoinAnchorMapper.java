package com.yooa.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.crm.api.domain.CrmCustomerJoinAnchor;
import com.yooa.crm.api.domain.query.CustomerJoinAnchorQuery;
import com.yooa.crm.api.domain.query.CustomerOperateJoinAnchorQuery;
import com.yooa.crm.api.domain.vo.CustomerJoinAnchorVo;
import com.yooa.crm.api.domain.vo.CustomerOperateJoinAnchorVo;
import com.yooa.extend.api.domain.OperateVermicelli;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户主播交接 - 数据层
 */
public interface CrmCustomerJoinAnchorMapper extends BaseMapper<CrmCustomerJoinAnchor> {

    List<CrmCustomerJoinAnchor> getCustomerJoinAnchorByCustomerId(@Param("customerId") Long customerId, @Param("addTime") LocalDateTime addTime);

    List<CustomerJoinAnchorVo> getList(Page page, @Param("query") CustomerJoinAnchorQuery customerJoinAnchorQuery);

    List<Long> getIds(Page page, @Param("query") CustomerJoinAnchorQuery customerJoinAnchorQuery);

    List<CustomerJoinAnchorVo> queryDetailById(@Param("ids") List<Long> ids);

    void updateCustomerJoinAnchorAmt(@Param("anchorId") Long anchorId, @Param("customerId") Long customerId, @Param("operateId") Long operateId, @Param("totalAmount") BigDecimal totalAmount);

    List<CustomerOperateJoinAnchorVo> getOperatorJoinAnchorList(Page page, @Param("query") CustomerOperateJoinAnchorQuery query);

    List<Long> getOperatorJoinIds(Page page, @Param("query") CustomerOperateJoinAnchorQuery query);

    List<CustomerOperateJoinAnchorVo> queryOperateJoinAnchorById(@Param("ids") List<Long> ids);

    List<CustomerJoinAnchorVo> queryDetail(@Param("query") CustomerJoinAnchorQuery customerJoinAnchorQuery);

    List<CustomerOperateJoinAnchorVo> getOperatorJoinAnchorListExport(@Param("query") CustomerOperateJoinAnchorQuery query);

    /**
     * 根据时间查询一交(根据一交查询关联的好友绑定记录)返回运营的粉丝登记
     *
     * @param customerId
     * @param beginTime
     * @param endTime
     * @return
     */
    List<OperateVermicelli> joinAnchorOperateVermicelli(@Param("customerId") Long customerId,
                                                        @Param("beginTime") LocalDateTime beginTime,
                                                        @Param("endTime") LocalDateTime endTime);



    List<Long> getPitcherIds(Page page, @Param("query") CustomerJoinAnchorQuery customerJoinAnchorQuery);

    List<CustomerJoinAnchorVo> queryPitcherDetailById(@Param("ids") List<Long> ids);

    /**
     * 查询投手一交列表
     */
    List<CustomerJoinAnchorVo> selectPitcherJoinAnchorList(@Param("page") Page<CustomerJoinAnchorVo> page,
                                                           @Param("query") CustomerJoinAnchorQuery query);

    /**
     * 导出投手一交列表
     */
    List<CustomerJoinAnchorVo> exportPitcherJoinAnchorList(@Param("query") CustomerJoinAnchorQuery query);

}




