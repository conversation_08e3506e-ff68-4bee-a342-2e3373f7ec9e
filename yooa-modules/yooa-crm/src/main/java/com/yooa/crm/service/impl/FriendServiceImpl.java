package com.yooa.crm.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.cmf.api.RemoteCmfUserService;
import com.yooa.common.core.constant.DictConstants;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.common.core.utils.DateUtil;
import com.yooa.common.core.utils.LocalDateUtil;
import com.yooa.common.core.utils.SpringUtils;
import com.yooa.common.core.utils.bean.BeanValidators;
import com.yooa.common.datascope.annotation.DataScope;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.crm.api.domain.*;
import com.yooa.crm.api.domain.dto.CustomerStatisticsDto;
import com.yooa.crm.api.domain.dto.FriendEmployeeDto;
import com.yooa.crm.api.domain.dto.FriendRegisterChargeDto;
import com.yooa.crm.api.domain.dto.PollingFriendDto;
import com.yooa.crm.api.domain.query.*;
import com.yooa.crm.api.domain.vo.*;
import com.yooa.crm.mapper.*;
import com.yooa.crm.service.*;
import com.yooa.extend.api.RemoteVermicelliService;
import com.yooa.extend.api.domain.ExtendVermicelli;
import com.yooa.extend.api.domain.OperateVermicelli;
import com.yooa.extend.api.domain.VipVermicelli;
import com.yooa.extend.api.domain.dto.CommonalityDto;
import com.yooa.extend.api.domain.vo.IndexUserGroupAllVo;
import com.yooa.system.api.RemoteDictDataService;
import com.yooa.system.api.RemoteUserService;
import com.yooa.system.api.domain.SysDictData;
import com.yooa.system.api.domain.SysUser;
import com.yooa.system.api.domain.query.DictDataQuery;
import com.yooa.system.api.domain.query.UserQuery;
import com.yooa.system.api.domain.vo.SysUserVo;
import lombok.AllArgsConstructor;
import lombok.Synchronized;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Validator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.Period;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 好友 - 服务实现层
 */
@AllArgsConstructor
@Service
public class FriendServiceImpl extends ServiceImpl<CrmFriendMapper, CrmFriend> implements FriendService {

    protected final Validator validator;
    protected final FriendChannelService friendChannelService;
    private final CrmCustomerFriendMapper customerFriendMapper;
    private final CrmCustomerOrderMapper customerOrderMapper;
    private final CrmCustomerMapper customerMapper;
    private final CrmFriendBrowseRecordMapper friendBrowseRecordMapper;
    private final CrmCustomerJoinAnchorMapper customerJoinAnchorMapper;
    private final CrmCustomerJoinServeMapper customerJoinServeMapper;
    private final RemoteUserService remoteUserService;
    private final RemoteVermicelliService remoteVermicelliService;
    private final RemoteCmfUserService remoteCmfUserService;
    private final CrmCustomerJoinAnchorMapper anchorMapper;
    private final FriendPollingReviewService friendPollingReviewService;
    private final CrmFriendPollingChatMapper friendPollingChatMapper;
    private final CustomerFriendService customerFriendService;
    private final CustomerOrderService customerOrderService;
    private final PitcherInvalidOrderService pitcherInvalidOrderService;
    private final CrmFriendMapper friendMapper;

    @Override
    public List<CrmCommonFriendVo> list(Page<CrmCommonFriendVo> page, FriendQuery query) {
        return baseMapper.selectListCustom(page, query);
    }

    @Override
    @Transactional (rollbackFor = Exception.class)
    public Integer updateFriend(CrmFriend friend,String customerId) {
        List<CrmCustomerFriend> customerFriendList = customerFriendMapper.selectList(new LambdaQueryWrapper<CrmCustomerFriend>()
                .eq(CrmCustomerFriend::getFriendId, friend.getFriendId())
                .eq(CrmCustomerFriend::getStatus, 0));

        if (CollUtil.isNotEmpty(customerFriendList)) {
            if (customerFriendList.get(0).getBeginTime().toLocalDate().compareTo(friend.getRecordDate()) < 0) {
                throw new ServiceException("加好友时间不能小于客户更新时间!");
            }
        }

        // 投放订单逻辑
            CrmFriend friendOld = baseMapper.selectById(friend.getFriendId());
            if (!Objects.equals(friend.getPitcherId(), friendOld.getPitcherId()) && StringUtils.isNotBlank(customerId) && !"null".equals(customerId)) {
                rechargeRecordPitcher(friend,friendOld,customerId);

        }

        return baseMapper.updateById(friend);
    }

    @Override
    public List<FriendCustomerVo> listByFriendCustomer(Page<FriendCustomerVo> page, CustomerFriendQuery query, boolean bl) {
        List<Long> userIds = CollUtil.newArrayList();
        if (query.getDataScope() == 1 && SecurityUtils.getLoginUser().getSysUser().getUserRole().equals(DictConstants.SYS_USER_ROLE_ADMIN)) {
            // 管理员看下级的查所有
        } else {
            if (query.getDataScope() == 1) {
                userIds.addAll(remoteUserService.getList(UserQuery.builder().build(), SecurityConstants.INNER)
                        .getData().stream().map(SysUser::getUserId).collect(Collectors.toList()));
            }
        }
        query.setSizeBl(bl);

        List<FriendCustomerVo> list = new ArrayList<>();

        if (query.isSizeBl()) {
            list = baseMapper.selectFriendCustomerList(page, query, userIds);       // 查询
        } else {
            list = baseMapper.exportFriendCustomerList(query, userIds);             // 导出(不查主播信息)
        }

        /**优化查询结构(导出时不去查)*/
        if (bl) {
            // 不带粉丝登记条件时去查粉丝登记
            if (ObjUtil.isNull(query.getType()) && CollUtil.isNotEmpty(list)) {
                query.setFriendIds(list.stream().map(FriendCustomerVo::getFriendId).collect(Collectors.toList()));
                query.setReceiveIds(list.stream().map(FriendCustomerVo::getExtend).map(ExtendVo::getUserId).distinct().toList());
                List<FriendFansVo> friendFansVoList = baseMapper.selectFansList(query);
                list.forEach(f -> {
                    FriendFansVo fansVo = friendFansVoList.stream().filter(e ->
                            ObjUtil.isNotNull(e.getFriendId()) &&
                                    e.getFriendId().equals(f.getFriendId()) &&
                                    e.getUserId().equals(f.getExtend().getUserId())
                    ).findFirst().orElse(null);
                    if (ObjUtil.isNotEmpty(fansVo)) {
                        f.setFansUp(fansVo.getFansUp());
                        f.setFans2h(fansVo.getFans2h());
                        f.setFans5h(fansVo.getFans5h());
                        f.setFans5k(fansVo.getFans5k());
                        f.setFans5w(fansVo.getFans5w());
                    }
                });
            }
            // 不带查询金额范围去查询实际和总充值金额
            if ((ObjUtil.isNull(query.getBeginMoney()) && ObjUtil.isNull(query.getEndMoney()))
                    && CollUtil.isNotEmpty(list) && list.stream().map(FriendCustomerVo::getCustomerId).toList().size() > 0) {
                List<FriendCustomerVo> orderList = baseMapper.selectRecharge(query, list);
                list.forEach(f -> {
                    FriendCustomerVo order = orderList.stream().filter(o ->
                            o.getCustomerId().equals(f.getCustomerId())
                                    && o.getFriendId().equals(f.getFriendId())).findFirst().orElse(null);
                    if (ObjUtil.isNotEmpty(order)) {
                        f.setActualRecharge(order.getActualRecharge());
                        f.setTotalRecharge(order.getTotalRecharge());
                    }
                });
            }
        }

        /**转换状态为好友\注册\一交\二交*/
        list.forEach(l -> {
            if (ObjUtil.isNotNull(l.getServe()) && ObjUtil.isNotNull(l.getServe().getUserId()) && l.getServe().getUserId() != 0) {
                l.setStatus("二交");
            } else if (CollUtil.isNotEmpty(l.getAnchorOperate()) && l.getAnchorOperate().size() > 0) {
                l.setStatus("一交");
            } else if (ObjUtil.isNotNull(l.getCustomerId())) {
                l.setStatus("注册");
            } else if (ObjUtil.isEmpty(l.getCustomerId())) {
                l.setStatus("好友");
            }
        });
        return list;
    }

    public List<FriendCustomerVo> listNewByFriendCustomer(Page<FriendCustomerVo> page, CustomerFriendQuery query, boolean bl) {
        List<Long> userIds = CollUtil.newArrayList();
        if (query.getDataScope() == 1 && SecurityUtils.getLoginUser().getSysUser().getUserRole().equals(DictConstants.SYS_USER_ROLE_ADMIN)) {
            // 管理员看下级的查所有
        } else {
            if (query.getDataScope() == 1) {
                userIds.addAll(remoteUserService.getList(UserQuery.builder().build(), SecurityConstants.INNER)
                        .getData().stream().map(SysUser::getUserId).collect(Collectors.toList()));
            }
        }
        query.setSizeBl(bl);

        List<FriendCustomerVo> list = new ArrayList<>();

        if (query.isSizeBl()) {
            list = baseMapper.selectFriendCustomerNewList(page, query, userIds);       // 查询
        } else {
            list = baseMapper.exportFriendCustomerNewList(query, userIds);             // 导出(不查主播信息)
        }

        /**优化查询结构(导出时不去查)*/
        if (bl) {
            // 不带粉丝登记条件时去查粉丝登记
            if (ObjUtil.isNull(query.getType()) && CollUtil.isNotEmpty(list)) {
                query.setFriendIds(list.stream().map(FriendCustomerVo::getFriendId).collect(Collectors.toList()));
                query.setReceiveIds(list.stream().map(FriendCustomerVo::getExtend).map(ExtendVo::getUserId).distinct().toList());
                List<FriendFansVo> friendFansVoList = baseMapper.selectFansList(query);
                list.forEach(f -> {
                    FriendFansVo fansVo = friendFansVoList.stream().filter(e ->
                            ObjUtil.isNotNull(e.getFriendId()) &&
                                    e.getFriendId().equals(f.getFriendId()) &&
                                    e.getUserId().equals(f.getExtend().getUserId())
                    ).findFirst().orElse(null);
                    if (ObjUtil.isNotEmpty(fansVo)) {
                        f.setFansUp(fansVo.getFansUp());
                        f.setFans2h(fansVo.getFans2h());
                        f.setFans5h(fansVo.getFans5h());
                        f.setFans5k(fansVo.getFans5k());
                        f.setFans5w(fansVo.getFans5w());
                    }
                });
            }
            // 不带查询金额范围去查询实际和总充值金额
            if ((ObjUtil.isNull(query.getBeginMoney()) && ObjUtil.isNull(query.getEndMoney()))
                    && CollUtil.isNotEmpty(list) && list.stream().map(FriendCustomerVo::getCustomerId).toList().size() > 0) {
                List<FriendCustomerVo> orderList = baseMapper.selectRecharge(query, list);
                list.forEach(f -> {
                    FriendCustomerVo order = orderList.stream().filter(o ->
                            o.getCustomerId().equals(f.getCustomerId())
                                    && o.getFriendId().equals(f.getFriendId())).findFirst().orElse(null);
                    if (ObjUtil.isNotEmpty(order)) {
                        f.setActualRecharge(order.getActualRecharge());
                        f.setTotalRecharge(order.getTotalRecharge());
                    }
                });
            }
        }

        /**转换状态为好友\注册\一交\二交*/
        list.forEach(l -> {
            if (ObjUtil.isNotNull(l.getServe()) && ObjUtil.isNotNull(l.getServe().getUserId()) && l.getServe().getUserId() != 0) {
                l.setStatus("二交");
            } else if (CollUtil.isNotEmpty(l.getAnchorOperate()) && l.getAnchorOperate().size() > 0) {
                l.setStatus("一交");
            } else if (ObjUtil.isNotNull(l.getCustomerId())) {
                l.setStatus("注册");
            } else if (ObjUtil.isEmpty(l.getCustomerId())) {
                l.setStatus("好友");
            }
        });
        return list;
    }

    @Override
    public FriendMaintenanceVo joinList(Long friendId) {

        List<ExtendVo> extendVoList = baseMapper.extendJoinList(friendId);      // 推广维护信息集
        List<ServeVo> vipVoList = baseMapper.vipJoinList(friendId);            // VIP维护信息集
        List<OperatorVo> operateVoList = baseMapper.operateJoinList(friendId);    // 运营维护信息集

        return FriendMaintenanceVo.builder()
                .extendVoList(extendVoList)
                .serveVoList(vipVoList)
                .operatorVoList(operateVoList)
                .build();
    }

    @Override
    public List<OperateFriendCustomerVo> listByFriendOperateCustomer(Page<OperateFriendCustomerVo> page, OperateCustomerFriendQuery query, boolean bl) {
        List<Long> userIds = CollUtil.newArrayList();
        if (query.getDataScope() == 1 && SecurityUtils.getLoginUser().getSysUser().getUserRole().equals(DictConstants.SYS_USER_ROLE_ADMIN)) {
            // 管理员看下级的查所有
        } else {
            if (query.getDataScope() == 1) {
                userIds.addAll(remoteUserService.getList(UserQuery.builder().build(), SecurityConstants.INNER)
                        .getData().stream().map(SysUser::getUserId).collect(Collectors.toList()));
            }
        }

        List<OperateFriendCustomerVo> list = CollUtil.newArrayList();


        if (bl) {   // 查询
            list = baseMapper.selectOperateFriendCustomerList(page, query, userIds);

            // 不带粉丝登记条件时去查粉丝登记
            if (ObjUtil.isNull(query.getType()) && CollUtil.isNotEmpty(list)) {
                query.setFriendIds(list.stream().map(OperateFriendCustomerVo::getFriendId).collect(Collectors.toList()));
                query.setOperateIds(userIds);
                List<OperateFriendCustomerVo> friendFansVoList = baseMapper.selectOperateFansList(query);
                list.forEach(f -> {
                    OperateFriendCustomerVo fansVo = friendFansVoList.stream().filter(o ->
                            ObjUtil.isNotNull(o.getFriendId()) &&
                                    o.getFriendId().equals(f.getFriendId()) &&
                                    o.getAnchorOperate().getUserId().equals(f.getAnchorOperate().getUserId())
                    ).findFirst().orElse(null);
                    if (ObjUtil.isNotEmpty(fansVo)) {
                        f.setFansUpReward(fansVo.getFansUpReward());
                        f.setFansUpRecharge(fansVo.getFansUpRecharge());
                        f.setFans1h(fansVo.getFans1h());
                        f.setFans2h(fansVo.getFans2h());
                        f.setFans5h(fansVo.getFans5h());
                        f.setFans5k(fansVo.getFans5k());
                        f.setFans5w(fansVo.getFans5w());
                        f.setFans10w(fansVo.getFans10w());
                    }
                });
            }
            // 不带查询金额范围去查询实际和总打赏金额
            if (CollUtil.isNotEmpty(list) && list.stream().map(OperateFriendCustomerVo::getCustomerId).toList().size() > 0
                    && ObjUtil.isEmpty(query.getBeginTotalMoney()) && ObjUtil.isEmpty(query.getEndTotalMoney())) {

                List<Long> customerIds = list.stream().map(OperateFriendCustomerVo::getCustomerId).collect(Collectors.toList());
                List<Long> pyOperateIds = list.stream()
                        .map(OperateFriendCustomerVo::getAnchorOperate)
                        .collect(Collectors.toList())
                        .stream().map(AnchorOperateVo::getPyOperateId).toList();
                List<Long> anchorIds = list.stream()
                        .map(OperateFriendCustomerVo::getAnchorOperate)
                        .collect(Collectors.toList())
                        .stream().map(AnchorOperateVo::getAnchorId).toList();

                List<OperateFriendCustomerVo> rewardList = baseMapper.selectOperateRecharge(customerIds, pyOperateIds, anchorIds, query);
                list.forEach(f -> {
                    OperateFriendCustomerVo reward = rewardList.stream().filter(r ->
                            r.getCustomerId().equals(f.getCustomerId())
                                    && f.getAnchorOperate().getPyOperateId().equals(r.getAnchorOperate().getPyOperateId())
                                    && f.getAnchorOperate().getAnchorId().equals(r.getAnchorOperate().getAnchorId())).findFirst().orElse(null);
                    if (ObjUtil.isNotEmpty(reward)) {
                        f.setActualRecharge(reward.getActualRecharge());
                        f.setTotalRecharge(reward.getTotalRecharge());
                    } else {
                        f.setActualRecharge(BigDecimal.ZERO);
                        f.setTotalRecharge(BigDecimal.ZERO);
                    }
                });
            }
        } else {
            list = baseMapper.exportOperateFriendCustomerList(query, userIds);
        }

        /**转换状态为好友\注册\一交\二交*/
        list.forEach(l -> {
            if (StrUtil.isNotEmpty(l.getStatus())) {
                if (l.getStatus().equals("1")) {
                    l.setStatus("首次");
                } else if (l.getStatus().equals("2")) {
                    l.setStatus("多次");
                }
            }
        });
        return list;
    }

    @Override
    public List<OperateFriendCustomerVo> listNewByFriendOperateCustomer(Page<OperateFriendCustomerVo> page, OperateCustomerFriendQuery query, boolean bl) {
        List<Long> userIds = CollUtil.newArrayList();
        if (query.getDataScope() == 1 && SecurityUtils.getLoginUser().getSysUser().getUserRole().equals(DictConstants.SYS_USER_ROLE_ADMIN)) {
            // 管理员看下级的查所有
        } else {
            if (query.getDataScope() == 1) {
                userIds.addAll(remoteUserService.getList(UserQuery.builder().build(), SecurityConstants.INNER)
                        .getData().stream().map(SysUser::getUserId).collect(Collectors.toList()));
            }
        }

        List<OperateFriendCustomerVo> list = CollUtil.newArrayList();


        if (bl) {   // 查询
            list = baseMapper.selectOperateFriendCustomerNewList(page, query, userIds);

            // 不带粉丝登记条件时去查粉丝登记
            if (ObjUtil.isNull(query.getType()) && CollUtil.isNotEmpty(list)) {
                query.setFriendIds(list.stream().map(OperateFriendCustomerVo::getFriendId).collect(Collectors.toList()));
                query.setOperateIds(userIds);
                List<OperateFriendCustomerVo> friendFansVoList = baseMapper.selectOperateFansList(query);
                list.forEach(f -> {
                    OperateFriendCustomerVo fansVo = friendFansVoList.stream().filter(o ->
                            ObjUtil.isNotNull(o.getFriendId()) &&
                                    o.getFriendId().equals(f.getFriendId()) &&
                                    o.getAnchorOperate().getUserId().equals(f.getAnchorOperate().getUserId())
                    ).findFirst().orElse(null);
                    if (ObjUtil.isNotEmpty(fansVo)) {
                        f.setFansUpReward(fansVo.getFansUpReward());
                        f.setFansUpRecharge(fansVo.getFansUpRecharge());
                        f.setFans1h(fansVo.getFans1h());
                        f.setFans2h(fansVo.getFans2h());
                        f.setFans5h(fansVo.getFans5h());
                        f.setFans5k(fansVo.getFans5k());
                        f.setFans5w(fansVo.getFans5w());
                        f.setFans10w(fansVo.getFans10w());
                    }
                });
            }
            // 不带查询金额范围去查询实际和总打赏金额
            if (CollUtil.isNotEmpty(list) && list.stream().map(OperateFriendCustomerVo::getCustomerId).toList().size() > 0
                    && ObjUtil.isEmpty(query.getBeginTotalMoney()) && ObjUtil.isEmpty(query.getEndTotalMoney())) {

                List<Long> customerIds = list.stream().map(OperateFriendCustomerVo::getCustomerId).collect(Collectors.toList());
                List<Long> pyOperateIds = list.stream()
                        .map(OperateFriendCustomerVo::getAnchorOperate)
                        .collect(Collectors.toList())
                        .stream().map(AnchorOperateVo::getPyOperateId).toList();
                List<Long> anchorIds = list.stream()
                        .map(OperateFriendCustomerVo::getAnchorOperate)
                        .collect(Collectors.toList())
                        .stream().map(AnchorOperateVo::getAnchorId).toList();

                List<OperateFriendCustomerVo> rewardList = baseMapper.selectOperateRecharge(customerIds, pyOperateIds, anchorIds, query);
                list.forEach(f -> {
                    OperateFriendCustomerVo reward = rewardList.stream().filter(r ->
                            r.getCustomerId().equals(f.getCustomerId())
                                    && f.getAnchorOperate().getPyOperateId().equals(r.getAnchorOperate().getPyOperateId())
                                    && f.getAnchorOperate().getAnchorId().equals(r.getAnchorOperate().getAnchorId())).findFirst().orElse(null);
                    if (ObjUtil.isNotEmpty(reward)) {
                        f.setActualRecharge(reward.getActualRecharge());
                        f.setTotalRecharge(reward.getTotalRecharge());
                    } else {
                        f.setActualRecharge(BigDecimal.ZERO);
                        f.setTotalRecharge(BigDecimal.ZERO);
                    }
                });
            }
        } else {
            list = baseMapper.exportOperateFriendCustomerNewList(query, userIds);
        }

        /**转换状态为好友\注册\一交\二交*/
        list.forEach(l -> {
            if (StrUtil.isNotEmpty(l.getStatus())) {
                if (l.getStatus().equals("1")) {
                    l.setStatus("首次");
                } else if (l.getStatus().equals("2")) {
                    l.setStatus("多次");
                }
            }
        });
        return list;
    }

    @Override
    public List<FreeExportVo> listFreeExport(CustomerFriendQuery query) {
        List<Long> userIds = CollUtil.newArrayList();
        if (SecurityUtils.getLoginUser().getSysUser().getUserRole().equals(DictConstants.SYS_USER_ROLE_ADMIN)) {
            // 管理员看下级的查所有
        } else {
            userIds.addAll(remoteUserService.getList(UserQuery.builder().build(), SecurityConstants.INNER)
                    .getData().stream().map(SysUser::getUserId).collect(Collectors.toList()));
        }

        List<FreeExportVo> list = baseMapper.listFreeExport(query, userIds);

        return list;
    }

    @Override
    public List<RegisterVo> listByRegister(Page<RegisterVo> page, CustomerFriendQuery query, boolean bl) {
        List<Long> userIds = CollUtil.newArrayList();
        if (query.getDataScope() == 1 && SecurityUtils.getLoginUser().getSysUser().getUserRole().equals(DictConstants.SYS_USER_ROLE_ADMIN)) {
            // 管理员看下级的查所有
        } else {
            if (query.getDataScope() == 1) {
                userIds.addAll(remoteUserService.getList(UserQuery.builder().build(), SecurityConstants.INNER)
                        .getData().stream().map(SysUser::getUserId).collect(Collectors.toList()));
            }
        }

        List<RegisterVo> list = new ArrayList<>();

        if (bl) {
            // 分页查询
            if (CollUtil.isNotEmpty(query.getDataScopeType()) &&
                    (query.getDataScopeType().contains(2) || query.getDataScopeType().contains(3) || query.getDataScopeType().contains(0))) {
                list = baseMapper.selectRegisterAllList(page, query, userIds);       // 查询全部
            } else {
                list = baseMapper.selectRegisterExtendList(page, query, userIds);    // 查询推广或者渠道(默认为查询推广)
            }
        } else {
            // 不分页导出
            list = baseMapper.exportRegisterAllList(query, userIds);
        }


        return list;
    }

    @Override
    public List<RegisterVo> listByQuality(Page<RegisterVo> page, CustomerFriendQuery query, boolean bl) {
        List<Long> userIds = CollUtil.newArrayList();
        if (query.getDataScope() == 1 && SecurityUtils.getLoginUser().getSysUser().getUserRole().equals(DictConstants.SYS_USER_ROLE_ADMIN)) {
            // 管理员看下级的查所有
        } else {
            if (query.getDataScope() == 1) {
                userIds.addAll(remoteUserService.getList(UserQuery.builder().build(), SecurityConstants.INNER)
                        .getData().stream().map(SysUser::getUserId).collect(Collectors.toList()));
            }
        }

        List<RegisterVo> list = new ArrayList<>();

        if (bl) {
            // 分页查询
            if (CollUtil.isNotEmpty(query.getDataScopeType()) &&
                    (query.getDataScopeType().contains(2) || query.getDataScopeType().contains(3) || query.getDataScopeType().contains(0))) {
                list = baseMapper.selectRegisterAllList(page, query, userIds);       // 查询全部
            } else {
                list = baseMapper.selectRegisterExtendList(page, query, userIds);    // 查询推广或者渠道(默认为查询推广)
            }
        } else {
            // 不分页导出
            list = baseMapper.exportRegisterAllList(query, userIds);
        }


        return list;
    }

    @Override
    public List<FriendDetailsVo> listByFriend(Page<FriendDetailsVo> page, CustomerFriendQuery query) {
        List<Long> userIds = CollUtil.newArrayList();
        if (query.getDataScope() == 1 && SecurityUtils.getLoginUser().getSysUser().getUserRole().equals(DictConstants.SYS_USER_ROLE_ADMIN)) {
            // 管理员看下级的查所有
        } else {
            if (query.getDataScope() == 1) {
                userIds.addAll(remoteUserService.getList(UserQuery.builder().build(), SecurityConstants.INNER)
                        .getData().stream().map(SysUser::getUserId).collect(Collectors.toList()));
            }
        }

        List<FriendDetailsVo> list = baseMapper.selectFriendList(page, query, userIds);

        for (FriendDetailsVo v : list) {
            // 拆分出部门-团队-小组
            String deptAncestorsNames = v.getReceiveAncestorsNames() + "-" + v.getReceiveDeptName();
            List<String> stringList = Arrays.stream(deptAncestorsNames.split("-")).collect(Collectors.toList());      // 拆分部门名
            int size = stringList.size() - 2;       // 要截取的
            if (size == 1) {
                v.setTeamName1(stringList.get(stringList.size() - 1));
            } else if (size == 2) {
                v.setTeamName1(stringList.get(stringList.size() - 2));
                v.setTeamName2(stringList.get(stringList.size() - 1));
            } else if (size == 3) {
                v.setTeamName1(stringList.get(stringList.size() - 3));
                v.setTeamName2(stringList.get(stringList.size() - 2));
                v.setTeamName3(stringList.get(stringList.size() - 1));
            }
        }

        return list;
    }

    @Override
    public IPage<FriendCustomerVo> customerList(Page page, CommonalityDto dto) {
        if (ObjUtil.isNull(dto.getBeginTime()) || ObjUtil.isNull(dto.getEndTime())) {
            throw new ServiceException("请选择时间范围!");
        } else if (ObjUtil.isNull(dto.getFields())) {
            throw new ServiceException("参数有误!");
        }

        List<Long> userPdIds = CollUtil.newArrayList();
        // 管理员不进(管理员看所有)
        if (!SecurityUtils.getLoginUser().getSysUser().getUserRole().equals(DictConstants.SYS_USER_ROLE_ADMIN)) {
            dto.setUserIds(CollUtil.newArrayList(SecurityUtils.getUserId()));
            userPdIds = SecurityUtils.getLoginUser().getSysUser().getPdUserId();
        }

        Integer deptType = SecurityUtils.getLoginUser().getSysUser().getDept().getDeptType();

        List<FriendCustomerVo> customerList = CollUtil.newArrayList();

        IPage<FriendCustomerVo> baseIPage = null;

        Integer fields = dto.getFields();
        if (fields == 6) {
            throw new ServiceException("暂无数据!");          // 优质(除了优质查不了都可以查)
        } else if (fields == 4) {         // 好友
            baseIPage = baseMapper.customerFriend(page, dto);
        } else if (fields == 0) {         // 注册
            baseIPage = baseMapper.customerRegister(page, dto);
        } else if (fields == 1) {         // 总充值
            List<CrmCustomerOrder> orderList = customerOrderMapper.selectList(page, new LambdaQueryWrapper<CrmCustomerOrder>()
                    .ge(ObjUtil.isNotNull(dto.getBeginTime()), CrmCustomerOrder::getOrderTime, dto.getBeginTime())
                    .le(ObjUtil.isNotNull(dto.getEndTime()), CrmCustomerOrder::getOrderTime, dto.getEndTime())
                    .in(ObjUtil.isNotNull(deptType) && CollUtil.isNotEmpty(userPdIds)
                                    && deptType.equals(DictConstants.SYS_DEPT_TYPE_EXTEND),
                            CrmCustomerOrder::getPyExtendId, userPdIds)
                    .in(ObjUtil.isNotNull(deptType) && CollUtil.isNotEmpty(userPdIds)
                                    && deptType.equals(DictConstants.SYS_DEPT_TYPE_VIP_SERVICE),
                            CrmCustomerOrder::getPyServeId, userPdIds));
            if (CollUtil.isNotEmpty(orderList)) {
                orderList.stream().map(CrmCustomerOrder::getCustomerId).distinct().collect(Collectors.toList()).forEach(id -> {
                    customerList.add(FriendCustomerVo.builder()
                            .customerId(id)
                            .build());
                });
            }
        } else if (fields == 2) {         // 新增业绩
            List<CrmCustomerOrder> orderList = customerOrderMapper.selectList(page, new LambdaQueryWrapper<CrmCustomerOrder>()
                    .ge(ObjUtil.isNotNull(dto.getBeginTime()), CrmCustomerOrder::getOrderTime, dto.getBeginTime())
                    .le(ObjUtil.isNotNull(dto.getEndTime()), CrmCustomerOrder::getOrderTime, dto.getEndTime())
                    .in(ObjUtil.isNotNull(deptType) && CollUtil.isNotEmpty(userPdIds)
                                    && deptType.equals(DictConstants.SYS_DEPT_TYPE_EXTEND),
                            CrmCustomerOrder::getPyExtendId, userPdIds)
                    .in(ObjUtil.isNotNull(deptType) && CollUtil.isNotEmpty(userPdIds)
                                    && deptType.equals(DictConstants.SYS_DEPT_TYPE_VIP_SERVICE),
                            CrmCustomerOrder::getPyServeId, userPdIds));
            if (CollUtil.isNotEmpty(orderList)) {
                List<Long> customerIds = orderList.stream().map(CrmCustomerOrder::getCustomerId).distinct().collect(Collectors.toList());
                List<CrmCustomer> crmCustomers = customerMapper.selectList(new LambdaQueryWrapper<CrmCustomer>()
                        .in(CrmCustomer::getCustomerId, customerIds)
                        .ge(CrmCustomer::getUpdateTime, LocalDateTime.now().minus(Period.ofDays(45))));
                // 当前时间往前推45天的更新时间内有充值的客户
                crmCustomers.stream().map(CrmCustomer::getCustomerId).distinct().collect(Collectors.toList()).forEach(id -> {
                    customerList.add(FriendCustomerVo.builder()
                            .customerId(id)
                            .build());
                });
            }
        } else if (fields == 7) {         // 一交
            List<CrmCustomerJoinAnchor> joinAnchorList = customerJoinAnchorMapper.selectList(new LambdaQueryWrapper<CrmCustomerJoinAnchor>()
                    .in(CollUtil.isNotEmpty(SecurityUtils.getLoginUser().getSysUser().getPdUserId()), CrmCustomerJoinAnchor::getExtendId, SecurityUtils.getLoginUser().getSysUser().getPdUserId())
                    .eq(CrmCustomerJoinAnchor::getStatus, 1)     // 查询已交接的
                    .ge(CrmCustomerJoinAnchor::getReceiveTime, dto.getBeginTime())
                    .le(CrmCustomerJoinAnchor::getReceiveTime, dto.getEndTime()));
            if (CollUtil.isNotEmpty(joinAnchorList)) {
                joinAnchorList.forEach(j -> {
                    customerList.add(FriendCustomerVo.builder()
                            .customerId(j.getCustomerId())
                            .build());
                });
            }
        } else if (fields == 8) {         // 二交
            List<CrmCustomerJoinServe> joinServeList = customerJoinServeMapper.selectList(new LambdaQueryWrapper<CrmCustomerJoinServe>()
                    .in(CollUtil.isNotEmpty(SecurityUtils.getLoginUser().getSysUser().getPdUserId()), CrmCustomerJoinServe::getExtendId, SecurityUtils.getLoginUser().getSysUser().getPdUserId())
                    .eq(CrmCustomerJoinServe::getStatus, 1)      // 查询已交接的
                    .ge(CrmCustomerJoinServe::getJoinTime, dto.getBeginTime())
                    .le(CrmCustomerJoinServe::getJoinTime, dto.getEndTime()));
            if (CollUtil.isNotEmpty(joinServeList)) {
                joinServeList.forEach(v -> {
                    customerList.add(FriendCustomerVo.builder()
                            .customerId(v.getCustomerId())
                            .build());
                });
            }
        } else if (fields == 5 || fields == 9 || fields == 10 || fields == 11 || fields == 12) {         // 粉丝登记
            List<ExtendVermicelli> vermicelliList = remoteVermicelliService.selExtendVermicelliList(
                    dto.getUserIds(),
                    dto.getFields(),
                    CollUtil.newArrayList(),
                    dto.getBeginTime().toString(),
                    dto.getEndTime().toString(),
                    SecurityConstants.INNER).getData();
            if (CollUtil.isNotEmpty(vermicelliList)) {
                vermicelliList.forEach(v -> {
                    customerList.add(FriendCustomerVo.builder()
                            .customerId(Stream.of(v.getCustomerIds().split(",")).filter(s -> !StrUtil.isEmpty(s)).map(Long::parseLong).toList().get(0))
                            .friendId(v.getFriendId())
                            .build());
                });
            }
        }

        if (ObjUtil.isNotNull(baseIPage)) {
            /**转换状态为好友\注册\一交\二交*/
            baseIPage.getRecords().forEach(l -> {
                if (ObjUtil.isNotNull(l.getCustomerId())) {
                    if (ObjUtil.isNotNull(l.getStatus())) {
                        l.setStatus(l.getStatus().equals("1") ? "一交" : l.getStatus().equals("2") ? "二交" : "注册");
                    }
                } else {
                    l.setStatus("好友");
                }
            });
            return baseIPage;
        }

        /**分页操作**/
        IPage<FriendCustomerVo> iPage = new Page<>();

        Long l = 0L;            // 起始页
        Long pages = 0L;        // 总页数
        Long tol = 0L;          // 总条数
        if (CollUtil.isNotEmpty(customerList)) {
            tol = customerList.stream().count();
            pages = (long) Math.ceil((double) customerList.size() / page.getSize());
            if (ObjUtil.isNotNull(page)) {
                l = page.getCurrent() - 1;
                if (l > pages) {
                    l = pages;
                }
            }
            iPage.setRecords(customerList.stream()
                    .skip(l * page.getSize())        // 跳过前面的元素
                    .limit(page.getSize())            // 限制返回的元素数量
                    .toList());
        }
        iPage.setPages(pages);
        iPage.setTotal(tol);
        iPage.setSize(page.getSize());
        iPage.setCurrent(page.getCurrent());

        // 补充数据
        List<Long> friendIds = iPage.getRecords().stream().filter(f -> ObjUtil.isNotNull(f.getFriendId()))
                .map(FriendCustomerVo::getFriendId).toList();               // 拿到好友id

        List<Long> customerIds = iPage.getRecords().stream().filter(f -> ObjUtil.isNotNull(f.getCustomerId()))
                .map(FriendCustomerVo::getCustomerId).toList();          // 拿到客户id

        if (CollUtil.isNotEmpty(friendIds)) {       // 补充好友数据
            List<CrmFriend> friendList = baseMapper.selectList(new LambdaQueryWrapper<CrmFriend>()
                    .in(CrmFriend::getFriendId, friendIds));
            iPage.getRecords().forEach(f -> {
                if (ObjUtil.isNotNull(f.getFriendId())) {
                    CrmFriend crmFriend = friendList.stream().filter(c -> c.getFriendId().equals(f.getFriendId())).findFirst().orElse(null);
                    if (ObjUtil.isNotNull(crmFriend)) {
                        f.setFriendName(crmFriend.getFriendName());                       // 好友名
                        f.setStatus(crmFriend.getStatus().toString());              // 状态
                        f.setCreateTime(crmFriend.getCreateTime());                 // 创建时间
                        f.setRemark(crmFriend.getRemark());                         // 备注
                    }
                }
            });
        }

        if (CollUtil.isNotEmpty(customerIds)) {     // 补充客户信息
//            List<CrmCustomer> crmCustomers = customerMapper.selectList(new LambdaQueryWrapper<CrmCustomer>()
//                    .in(CrmCustomer::getCustomerId, customerIds));
//            iPage.getRecords().forEach(f -> {
//                if (ObjUtil.isNotNull(f.getCustomerId())) {
//                    CrmCustomer crmCustomer = crmCustomers.stream().filter(c -> c.getCustomerId().equals(f.getCustomerId())).findFirst().orElse(null);
//                    if (ObjUtil.isNotNull(crmCustomer)) {
//                        f.setCreateTime(crmCustomer.getCreateTime());    // 创建时间
//                        f.setUpdateTime(crmCustomer.getUpdateTime());    // 修改时间
//                        f.setFriendName(crmCustomer.getCustomerName());  // 客户名
//                    }
//                }
//            });
            if (CollUtil.isEmpty(friendIds)) {       // 补充好友数据
                List<CrmCustomerFriend> list = customerFriendMapper.selectList(new LambdaQueryWrapper<CrmCustomerFriend>()
                        .in(CrmCustomerFriend::getCustomerId, customerIds)
                        .ge(CrmCustomerFriend::getCreateTime, dto.getBeginTime())
                        .le(CrmCustomerFriend::getCreateTime, dto.getEndTime()));

                if (CollUtil.isNotEmpty(list)) {
                    List<CrmFriend> friendList = baseMapper.selectList(new LambdaQueryWrapper<CrmFriend>()
                            .in(CrmFriend::getFriendId, list.stream().map(CrmCustomerFriend::getFriendId).toList()));

                    iPage.getRecords().forEach(f -> {
                        CrmCustomerFriend crmCustomerFriend = list.stream().filter(c -> c.getCustomerId().equals(f.getCustomerId())).findFirst().orElse(null);
                        if (ObjUtil.isNotNull(crmCustomerFriend)) {
                            CrmFriend friend = friendList.stream().filter(c -> c.getFriendId().equals(crmCustomerFriend.getFriendId())).findFirst().orElse(null);
                            f.setFriendId(friend.getFriendId());
                            f.setFriendName(friend.getFriendName());
                            f.setStatus(friend.getStatus().toString());
                            f.setCreateTime(friend.getCreateTime());
                            f.setRemark(friend.getRemark());
                        }
                    });
                }
            }
        }

        return iPage;
    }

    @Override
    public FriendCustomerDetailsVo getFriendCustomerInfo(Long friendId) {
        FriendCustomerDetailsVo vo = customerMapper.selFriendCustomerVo(friendId);
        List<CustomerVo> customerVoList = customerMapper.selCustomerVoList(friendId);
        customerVoList.forEach(c -> {
            if (CollUtil.isEmpty(c.getAnchorOperate()) || ObjUtil.isNull(c.getAnchorOperate().get(0))) {
                c.setAnchorOperate(CollUtil.newArrayList());
            }
        });

        if (CollUtil.isNotEmpty(customerVoList)) {
            vo.setCustomerVoList(customerVoList);
        }
        return vo;
    }

    @Override
    public List<GroupPeopleNumberVo> districtPeopleNumber(CustomerStatisticsDto dto) {
        return SpringUtils.getAopProxy(this).groupPeopleNumber(dto, "crm_friend_area_type", "area");       // 字典类型地区
    }

    @Override
    public List<GroupPeopleNumberVo> districtAddPeople(CustomerStatisticsDto dto) {
        if (ObjUtil.isNull(dto.getGroupType())) {
            throw new ServiceException("参数有误!");
        }
        List<GroupPeopleNumberVo> list = baseMapper.districtGroupPeople(dto);
        if (CollUtil.isEmpty(list)) {
            return list;
        }
        if (dto.getGroupType() == 0) {         // 默认以日分组,这是以月分组
            list.forEach(l -> {       // 转换日期格式
                if (StrUtil.isNotEmpty(l.getName())) {
                    Date date = DateUtil.stringConvertDate(l.getName(), DateUtil.y_M_d);
                    l.setName(DateUtil.dateConvertString(date, DateUtil.y_M));
                }
            });

            list = list.stream().filter(l -> StrUtil.isNotEmpty(l.getName()))       // 分组并求和
                    .collect(Collectors.groupingBy(GroupPeopleNumberVo::getName,
                            Collectors.summingInt(GroupPeopleNumberVo::getNumber)))
                    .entrySet().stream()
                    .map(entry -> new GroupPeopleNumberVo(entry.getKey(), entry.getValue(), 0.0))
                    .collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public List<GroupPeopleNumberVo> sexPeopleNumber(CustomerStatisticsDto dto) {
        return SpringUtils.getAopProxy(this).groupPeopleNumber(dto, "sys_user_sex", "sex");       // 字典类型性别
    }

    @Override
    public List<GroupPeopleNumberVo> rankPeopleNumber(CustomerStatisticsDto dto) {
        return SpringUtils.getAopProxy(this).groupPeopleNumber(dto, "customer_level", "rank");     // 字典类型级别
    }

    // 各字段分组和算占比
    public List<GroupPeopleNumberVo> groupPeopleNumber(CustomerStatisticsDto dto, String groupStr, String groupStr1) {
        List<GroupPeopleNumberVo> list = baseMapper.GroupPeopleNumber(dto, groupStr, groupStr1);
        GroupPeopleNumberVo vo = new GroupPeopleNumberVo();       // 汇总
        vo.setNumber(list.stream().mapToInt(GroupPeopleNumberVo::getNumber).sum());
        vo.setName("总客户量");
        vo.setRatio(0.0);
        list.add(vo);

        if (ObjUtil.isNotNull(vo.getNumber())) {       // 有值才去算
            BigDecimal bg = new BigDecimal(100);
            BigDecimal bg1 = new BigDecimal(vo.getNumber());
            list.forEach(l -> {
                // 算占比
                if (l.getNumber() > 0) {
                    BigDecimal bg2 = new BigDecimal(l.getNumber());
                    l.setRatio(bg2.divide(bg1, 2, BigDecimal.ROUND_HALF_UP).multiply(bg).doubleValue());
                } else {
                    l.setRatio(0.0);
                }
            });
        }

        return list;
    }

    @Override
    public List<SexAgePeopleNumberVo> agePeopleNumber(CustomerStatisticsDto dto) {
        List<int[]> integers = new ArrayList<>();       // 分组规则
        integers.add(new int[]{18, 25});
        integers.add(new int[]{26, 35});
        integers.add(new int[]{36, 45});
        integers.add(new int[]{46, 56});

        List<SexAgePeopleNumberVo> result = new ArrayList<>();
        List<SexAgePeopleNumberVo> list = baseMapper.SexAgePeopleNumber(dto).stream().filter(v -> ObjUtil.isNotNull(v.getAge())).toList();
        integers.forEach(i -> {
            // 初始化
            SexAgePeopleNumberVo vo = new SexAgePeopleNumberVo();
            SexAgePeopleNumberVo vo1 = new SexAgePeopleNumberVo();
            vo.setSex("男");
            vo1.setSex("女");
            vo.setAge(i[0] + "~" + i[1]);
            vo1.setAge(i[0] + "~" + i[1]);
            result.add(vo);
            result.add(vo1);

            // 筛选
            Long l = list.stream().filter(v -> Integer.parseInt(v.getAge()) >= i[0] && Integer.parseInt(v.getAge()) <= i[1] && v.getSex().equals("男"))
                    .mapToLong(SexAgePeopleNumberVo::getNumber).sum();
            Long l1 = list.stream().filter(v -> Integer.parseInt(v.getAge()) >= i[0] && Integer.parseInt(v.getAge()) <= i[1] && v.getSex().equals("女"))
                    .mapToLong(SexAgePeopleNumberVo::getNumber).sum();
            vo.setNumber(l);
            vo1.setNumber(l1);
        });

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int bindCustomer(List<Long> pdUserId, Long friendId, CrmCustomer customer) {
        // 收集这个好友下的所有客户ID集
        List<Long> customerIds = CollUtil.newArrayList(customer.getCustomerId());

        // 好友下的已绑定的客户ID集
        customerIds.addAll(customerFriendMapper.selectList(
                        new LambdaQueryWrapper<CrmCustomerFriend>()
                                .eq(CrmCustomerFriend::getFriendId, friendId)
                                .eq(CrmCustomerFriend::getStatus, 0))
                .stream()
                .map(CrmCustomerFriend::getCustomerId)
                .toList());

        // 判断这个好友下的所有客户的客服是不是同一个,不是联系管理员
        SysUserVo serveUserVo = new SysUserVo();
        List<SysUserVo> list = remoteUserService.getList(UserQuery.builder()
                .pdUserId(CollUtil.newArrayList(customer.getServeId())).build(), SecurityConstants.INNER).getData();
        SysUserVo serveUser = new SysUserVo();
        if (CollUtil.isNotEmpty(list)) {
            serveUser = list.get(0);
        }

        // 绑定时去补以前的推广粉丝登记记录
        // 好友下所有客户的充值订单(在此好友领取人下的订单)

        List<CrmCustomer> customerList = customerMapper.selectList(new LambdaQueryWrapper<CrmCustomer>()
                .in(CrmCustomer::getCustomerId, customerIds));
        LambdaQueryWrapper<CrmCustomerOrder> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(CrmCustomerOrder::getPyExtendId, pdUserId);       // 好友领取人的PD推广ID
        String condition = customerList.stream()                              // 好友下所有客户
                .map(c -> "(customer_id = " + c.getCustomerId() + " and order_time >= '" + c.getUpdateTime() + "')")
                .collect(Collectors.joining(" OR "));
        lambdaQueryWrapper.apply("(" + condition + ")");
        List<CrmCustomerOrder> extendOrderList = customerOrderMapper.selectList(lambdaQueryWrapper);

        // 情况1:(好友下所有客户未有充值,不用处理)
        if (CollUtil.isNotEmpty(extendOrderList)) {
            /**推广的粉丝登记**/

            // 新绑定的客户充值订单
            List<CrmCustomerOrder> orderList1 = extendOrderList.stream().filter(o -> o.getCustomerId().equals(customer.getCustomerId())).collect(Collectors.toList());
            // 已绑定的客户充值订单
            List<CrmCustomerOrder> orderList2 = extendOrderList.stream().filter(o -> !o.getCustomerId().equals(customer.getCustomerId())).collect(Collectors.toList());

            Integer bl = 0;
            // 粉丝登记模版
            ExtendVermicelli vermicelli = new ExtendVermicelli();
            vermicelli.setFriendId(friendId);
            vermicelli.setExtendId(SecurityUtils.getUserId());
            vermicelli.setExtendDeptId(SecurityUtils.getLoginUser().getSysUser().getDeptId());
            vermicelli.setServeId(serveUserVo.getUserId());
            vermicelli.setServeDeptId(serveUserVo.getDeptId());
            vermicelli.setCreateTime(LocalDateTime.now());
            vermicelli.setRemark("绑定时补录的粉丝登记");
            vermicelli.setCreateBy(1L);

            if (ObjUtil.isNotEmpty(orderList1) && ObjUtil.isNotEmpty(orderList2)) {
                // 情况2:(新绑定和已绑定客户都有充值订单)(可能没有200、500粉,200、500从本月月初算)
                bl = 1;
            } else if (ObjUtil.isNotEmpty(orderList1) && ObjUtil.isEmpty(orderList2)) {
                bl = 1;
                // 情况3:(新绑定客户有充值订单,已绑定客户没有充值订单)(说明没有首充、200、500粉,200、500从本月月初算)
                CrmCustomerOrder minOrder = orderList1.stream()
                        .min(Comparator.comparing(CrmCustomerOrder::getOrderTime))
                        .orElse(null);
                if (ObjUtil.isNotEmpty(minOrder)) {
                    // 新增首充记录
                    vermicelli.setFansType(5);
                    vermicelli.setCustomerIds(customer.getCustomerId().toString());
                    vermicelli.setRecordDate(minOrder.getOrderTime().toLocalDate());
                    remoteVermicelliService.addExtendVermicelli(vermicelli, SecurityConstants.INNER);
                }
            }

            if (bl == 1) {
                List<ExtendVermicelli> extendVermicelliList = remoteVermicelliService.selExtendVermicelliList(
                        CollUtil.newArrayList(SecurityUtils.getUserId()),
                        null,
                        CollUtil.newArrayList(friendId),
                        null,
                        null,
                        SecurityConstants.INNER
                ).getData();        // 查询好友的粉丝登记历史记录

                List<String> yearMonthList = extendOrderList.stream()
                        .map(CrmCustomerOrder::getOrderTime)
                        .map(d -> d.getYear() + "-" + String.format("%02d", d.getMonthValue()))
                        .collect(Collectors.toCollection(TreeSet::new)) // 使用 TreeSet 去重并排序
                        .stream()
                        .collect(Collectors.toList());
                Long num5k = extendVermicelliList.stream().filter(v -> v.getFansType() == 2
                        && yearMonthList.contains(v.getRecordDate().getYear() + "-" + String.format("%02d", v.getRecordDate().getMonthValue()))).count();
                Long num5w = extendVermicelliList.stream().filter(v -> v.getFansType() == 3
                        && yearMonthList.contains(v.getRecordDate().getYear() + "-" + String.format("%02d", v.getRecordDate().getMonthValue()))).count();
                boolean bl200 = extendVermicelliList.stream().filter(v -> v.getFansType() == 0).count() == 0;
                boolean bl500 = extendVermicelliList.stream().filter(v -> v.getFansType() == 1).count() == 0;
                boolean bl5k = num5k.intValue() != yearMonthList.size();
                boolean bl5w = num5w.intValue() != yearMonthList.size();

                vermicelli.setCustomerIds(customerIds.stream().map(String::valueOf).collect(Collectors.joining(",")));

                // 判断生成200粉、500粉
                if (bl200 || bl500) {
                    for (CrmCustomerOrder o : extendOrderList) {
                        if (bl200 || bl500) {
                            List<CrmCustomerOrder> computeOrder = extendOrderList.stream().filter(order ->
                                            o.getOrderTime().plusDays(1).compareTo(order.getOrderTime()) >= 0
                                                    && order.getOrderTime().compareTo(o.getOrderTime()) >= 0)
                                    .collect(Collectors.toList());          // 每笔订单往后推24小时

                            BigDecimal dayUp = computeOrder.stream().map(CrmCustomerOrder::getOrderMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
                            if (dayUp.compareTo(BigDecimal.valueOf(200)) >= 0 && bl200) {
                                bl200 = false;
                                // 新增200记录
                                vermicelli.setFansType(0);
                                vermicelli.setRecordDate(computeOrder.stream()
                                        .max(Comparator.comparing(CrmCustomerOrder::getOrderTime))
                                        .orElse(null).getOrderTime().toLocalDate());
                                remoteVermicelliService.addExtendVermicelli(vermicelli, SecurityConstants.INNER);
                            }
                            if (dayUp.compareTo(BigDecimal.valueOf(500)) >= 0 && bl500) {
                                bl500 = false;
                                // 新增500记录
                                vermicelli.setFansType(1);
                                vermicelli.setRecordDate(computeOrder.stream()
                                        .max(Comparator.comparing(CrmCustomerOrder::getOrderTime))
                                        .orElse(null).getOrderTime().toLocalDate());
                                remoteVermicelliService.addExtendVermicelli(vermicelli, SecurityConstants.INNER);
                            }
                        }
                    }
                }

                // 判断生成5k、5w粉
                if (bl5k || bl5w) {
                    boolean bl5kOlb = extendVermicelliList.stream().filter(v -> v.getFansType() == 2).count() == 0;
                    boolean bl5wOlb = extendVermicelliList.stream().filter(v -> v.getFansType() == 3).count() == 0;
                    for (String ym : yearMonthList) {
                        boolean monthBl5k = true;
                        boolean monthBl5w = true;
                        if (CollUtil.isNotEmpty(extendVermicelliList)) {
                            monthBl5k = extendVermicelliList.stream().filter(v -> v.getFansType() == 2
                                    && ym.equals(v.getRecordDate().getYear() + "-" + String.format("%02d", v.getRecordDate().getMonthValue()))).count() == 0;
                            monthBl5w = extendVermicelliList.stream().filter(v -> v.getFansType() == 3
                                    && ym.equals(v.getRecordDate().getYear() + "-" + String.format("%02d", v.getRecordDate().getMonthValue()))).count() == 0;
                        }

                        if (monthBl5k || monthBl5w) {
                            BigDecimal monthUp = extendOrderList.stream().filter(o -> ym.equals(o.getOrderTime().getYear() + "-" + String.format("%02d", o.getOrderTime().getMonthValue())))
                                    .map(CrmCustomerOrder::getOrderMoney).reduce(BigDecimal.ZERO, BigDecimal::add);      // 一个月的充值数

                            if (monthUp.compareTo(BigDecimal.valueOf(5000)) >= 0 && monthBl5k) {
                                LocalDateTime dateTime5k = extendOrderList.stream()
                                        .filter(o -> ym.equals(o.getOrderTime().getYear() + "-" + String.format("%02d", o.getOrderTime().getMonthValue())))
                                        .reduce(
                                                new AbstractMap.SimpleEntry<>(BigDecimal.ZERO, (LocalDateTime) null), // 初始化累计金额和订单时间
                                                (acc, order) -> {
                                                    BigDecimal newAccumulated = acc.getKey().add(order.getOrderMoney());
                                                    if (newAccumulated.compareTo(BigDecimal.valueOf(5000)) >= 0) {   // 统计第一次累加到5千时的订单时间
                                                        return new AbstractMap.SimpleEntry<>(newAccumulated, order.getOrderTime());
                                                    } else {
                                                        return new AbstractMap.SimpleEntry<>(newAccumulated, acc.getValue());
                                                    }
                                                },
                                                (acc1, acc2) -> acc2 // 合并函数，对于顺序流可以简单返回第二个累加器
                                        ).getValue();
                                vermicelli.setRecordDate(dateTime5k.toLocalDate());

                                // 新增5k记录
                                vermicelli.setFansType(2);
                                vermicelli.setStatus(bl5kOlb ? "0" : "1");
                                remoteVermicelliService.addExtendVermicelli(vermicelli, SecurityConstants.INNER);
                                bl5kOlb = false;
                            }
                            if (monthUp.compareTo(BigDecimal.valueOf(50000)) >= 0 && monthBl5w) {
                                LocalDateTime dateTime5w = extendOrderList.stream()
                                        .filter(o -> ym.equals(o.getOrderTime().getYear() + "-" + String.format("%02d", o.getOrderTime().getMonthValue())))
                                        .reduce(
                                                new AbstractMap.SimpleEntry<>(BigDecimal.ZERO, (LocalDateTime) null), // 初始化累计金额和订单时间
                                                (acc, order) -> {
                                                    BigDecimal newAccumulated = acc.getKey().add(order.getOrderMoney());
                                                    if (newAccumulated.compareTo(BigDecimal.valueOf(50000)) >= 0) {  // 统计第一次累加到5万时的订单时间
                                                        return new AbstractMap.SimpleEntry<>(newAccumulated, order.getOrderTime());
                                                    } else {
                                                        return new AbstractMap.SimpleEntry<>(newAccumulated, acc.getValue());
                                                    }
                                                },
                                                (acc1, acc2) -> acc2 // 合并函数，对于顺序流可以简单返回第二个累加器
                                        ).getValue();
                                vermicelli.setRecordDate(dateTime5w.toLocalDate());

                                // 新增5w记录
                                vermicelli.setFansType(3);
                                vermicelli.setStatus(bl5wOlb ? "0" : "1");
                                remoteVermicelliService.addExtendVermicelli(vermicelli, SecurityConstants.INNER);
                                bl5wOlb = false;
                            }
                        }
                    }
                }
            }

            // 情况4:(已绑定客户有充值订单,新绑定客户没有充值订单,不用处理)

            /**运营的首充粉丝登记**/
            List<OperateVermicelli> operateVermicellis = remoteVermicelliService.selOperateVermicelliList(null, 7, null, CollUtil.newArrayList(friendId), null, null, SecurityConstants.INNER).getData();
            if (CollUtil.isEmpty(operateVermicellis)) {
                // 查出最早的一条订单时间
                LocalDateTime minOrderTime = extendOrderList.stream().map(CrmCustomerOrder::getOrderTime).min(LocalDateTime::compareTo).orElse(null);

                // 开始时间：如果订单时间减三个月 小于 客户更新时间那取客户更新时间反而取订单时间减三个月
                LocalDateTime beginTime = minOrderTime.minusMonths(3).isAfter(customer.getUpdateTime()) ? minOrderTime.minusMonths(3) : customer.getUpdateTime();
                // 结束时间：订单时间
                LocalDateTime endTime = minOrderTime;

                // 查询是否有大号或小号已经达成过了粉丝登记
                List<OperateVermicelli> vermicelliList = remoteVermicelliService.selOperateVermicelliList(null, 7, customer.getCustomerId(), null, null, null, SecurityConstants.INNER).getData();

                // 没有达成过粉丝登记
                if (CollUtil.isEmpty(vermicelliList)) {
                    List<OperateVermicelli> operateVermicelliList = customerJoinAnchorMapper.joinAnchorOperateVermicelli(customer.getCustomerId(), beginTime, endTime);

                    operateVermicelliList.stream().forEach(o -> {
                        remoteVermicelliService.addOperateVermicelli(OperateVermicelli.builder()
                                .fansType(7)        // 首充
                                .recordDate(minOrderTime.toLocalDate())
                                .createTime(LocalDateTime.now())
                                .createBy(1L)
                                .remark("充值表产生粉丝登记")
                                .build(), SecurityConstants.INNER);         // 新增运营的首充粉丝登记
                    });
                }

            } else if (operateVermicellis.stream().filter(o -> Arrays.stream(o.getCustomerIds().split(",")).toList().contains(customer.getCustomerId())).toList().size() == 0) {
                // 有首充粉但此条粉丝登记的客户ID集中没有此客户
                operateVermicellis.forEach(o -> {
                    // 粉丝登记插入客户ID
                    o.setCustomerIds(o.getCustomerIds() + "," + customer.getCustomerId());
                });
                // 批量修改登记
                remoteVermicelliService.updOperateVermicelliList(operateVermicellis, SecurityConstants.INNER);
            }
        }

        // 绑定时去补以前的VIP粉丝登记记录
        List<CrmCustomerOrder> serveOrderList = customerOrderMapper.selectList(new LambdaQueryWrapper<CrmCustomerOrder>()
                .in(CrmCustomerOrder::getCustomerId, customerIds)
                .in(CrmCustomerOrder::getPyServeId, customer.getServeId())    // 好友领取人绑定的PYID集
                .eq(CrmCustomerOrder::getOrderStatus, 1)     // 已完成
                .orderByAsc(CrmCustomerOrder::getOrderTime));    // 订单排序

        if (CollUtil.isNotEmpty(serveOrderList)) {

            // 好友已录入的粉丝登记
            List<VipVermicelli> vipVermicelliList = remoteVermicelliService.selVipVermicelliList(
                    CollUtil.newArrayList(serveUser.getUserId()),
                    null,
                    CollUtil.newArrayList(friendId),
                    null,
                    null,
                    SecurityConstants.INNER).getData();

            // VIP粉丝登记模版
            VipVermicelli vermicelli = new VipVermicelli();
            vermicelli.setFriendId(friendId);
            vermicelli.setCustomerIds(serveOrderList.stream().map(CrmCustomerOrder::getCustomerId).distinct().toList()
                    .stream().map(String::valueOf).collect(Collectors.joining(",")));
            vermicelli.setExtendId(SecurityUtils.getUserId());
            vermicelli.setExtendDeptId(SecurityUtils.getLoginUser().getSysUser().getDeptId());
            vermicelli.setServeId(serveUser.getUserId());
            vermicelli.setServeDeptId(serveUser.getDeptId());
            vermicelli.setCreateTime(LocalDateTime.now());
            vermicelli.setRemark("充值订单产生粉丝登记");
            vermicelli.setCreateBy(1L);

            boolean blsc = vipVermicelliList.stream().count() == 0;
            boolean bl200 = vipVermicelliList.stream().filter(e -> e.getFansType() == 0).count() == 0;
            boolean bl500 = vipVermicelliList.stream().filter(e -> e.getFansType() == 1).count() == 0;

            // 查询客户的二交时间(早于七月,并且以前符合可以达成200粉的条件)
            CrmCustomerJoinServe joinServe = customerJoinServeMapper.selectList(new LambdaQueryWrapper<CrmCustomerJoinServe>()
                    .eq(CrmCustomerJoinServe::getCustomerId, customer.getCustomerId())
                    .eq(CrmCustomerJoinServe::getStatus, 1)         // 已交接
                    .last("limit 1")).stream().limit(1).findFirst().orElse(null);

            boolean joinServeStatus = false;  // true:早于七月 false:不用管
            LocalDate julyFirstThisYear = LocalDate.of(2025, Month.JULY, 1);
            if (ObjUtil.isNotNull(joinServe) &&                                         // 二交记录不能为空
                    joinServe.getJoinTime().toLocalDate().isBefore(julyFirstThisYear)) {// 交接时间不能早于七月
                joinServeStatus = true;
            }

            if (blsc) {
                vermicelli.setFansType(5);
                vermicelli.setRecordDate(serveOrderList.stream().map(CrmCustomerOrder::getOrderTime).min(LocalDateTime::compareTo).get().toLocalDate());

                if (joinServeStatus || vermicelli.getRecordDate().isBefore(LocalDate.of(2025, 7, 1))) {
                    // 首次充值在25年7月之前改到2000年
                    vermicelli.setRecordDate(LocalDate.of(2000, 1, 1));
                }
                remoteVermicelliService.addVipVermicelli(vermicelli, SecurityConstants.INNER);
            }

            if (bl200 || bl500) {
                // 按天分组并累加 orderMoney
                Map<LocalDate, BigDecimal> dailyOrderMoneyMap = serveOrderList.stream()
                        .collect(Collectors.groupingBy(
                                order -> order.getOrderTime().toLocalDate(), // 按天分组
                                Collectors.reducing(BigDecimal.ZERO, order -> order.getOrderMoney(), BigDecimal::add) // 累加 orderMoney
                        ));
                if (bl200) {
                    LocalDate firstGreaterThan200 = dailyOrderMoneyMap.entrySet().stream()
                            .sorted(Map.Entry.comparingByKey())         // 排序
                            .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(200)) >= 0)
                            .map(Map.Entry::getKey)
                            .findFirst()
                            .orElse(null);

                    if (ObjUtil.isNotNull(firstGreaterThan200)) {
                        vermicelli.setFansType(0);
                        vermicelli.setRecordDate(firstGreaterThan200);

                        if (joinServeStatus || vermicelli.getRecordDate().isBefore(LocalDate.of(2025, 7, 1))) {
                            // 首次达成200粉在25年7月之前改到2000年
                            vermicelli.setRecordDate(LocalDate.of(2000, 1, 1));
                        }
                        remoteVermicelliService.addVipVermicelli(vermicelli, SecurityConstants.INNER);
                    }

                }
                if (bl500) {
                    LocalDate firstGreaterThan500 = dailyOrderMoneyMap.entrySet().stream()
                            .sorted(Map.Entry.comparingByKey())         // 排序
                            .filter(entry -> entry.getValue().compareTo(BigDecimal.valueOf(500)) >= 0)
                            .map(Map.Entry::getKey)
                            .findFirst()
                            .orElse(null);

                    if (ObjUtil.isNotNull(firstGreaterThan500)) {
                        vermicelli.setFansType(1);
                        vermicelli.setRecordDate(firstGreaterThan500);

                        if (joinServeStatus || vermicelli.getRecordDate().isBefore(LocalDate.of(2025, 7, 1))) {
                            // 首次达成500粉在25年7月之前改到2000年
                            vermicelli.setRecordDate(LocalDate.of(2000, 1, 1));
                        }
                        remoteVermicelliService.addVipVermicelli(vermicelli, SecurityConstants.INNER);
                    }
                }

            }

            // 5k,5w粉只计算25年7月之后的
            serveOrderList = serveOrderList.stream().filter(o -> o.getOrderTime().toLocalDate().isAfter(LocalDate.of(2025, 7, 1))).collect(Collectors.toList());
            List<String> yearMonthList = serveOrderList.stream()
                    .map(CrmCustomerOrder::getOrderTime)
                    .map(d -> d.getYear() + "-" + String.format("%02d", d.getMonthValue()))
                    .collect(Collectors.toCollection(TreeSet::new)) // 使用 TreeSet 去重并排序
                    .stream()
                    .collect(Collectors.toList());

            Long num5k = vipVermicelliList.stream().filter(v -> v.getFansType() == 2
                    && yearMonthList.contains(v.getRecordDate().getYear() + "-" + String.format("%02d", v.getRecordDate().getMonthValue()))).count();
            Long num5w = vipVermicelliList.stream().filter(v -> v.getFansType() == 3
                    && yearMonthList.contains(v.getRecordDate().getYear() + "-" + String.format("%02d", v.getRecordDate().getMonthValue()))).count();
            boolean bl5k = num5k.intValue() != yearMonthList.size();
            boolean bl5w = num5w.intValue() != yearMonthList.size();

            if (bl5k || bl5w) {
                boolean bl5kOlb = vipVermicelliList.stream().filter(v -> v.getFansType() == 2).count() == 0;
                boolean bl5wOlb = vipVermicelliList.stream().filter(v -> v.getFansType() == 3).count() == 0;

                for (String ym : yearMonthList) {
                    boolean monthBl5k = true;
                    boolean monthBl5w = true;
                    if (CollUtil.isNotEmpty(vipVermicelliList)) {
                        monthBl5k = vipVermicelliList.stream().filter(v -> v.getFansType() == 2
                                && ym.equals(v.getRecordDate().getYear() + "-" + String.format("%02d", v.getRecordDate().getMonthValue()))).count() == 0;
                        monthBl5w = vipVermicelliList.stream().filter(v -> v.getFansType() == 3
                                && ym.equals(v.getRecordDate().getYear() + "-" + String.format("%02d", v.getRecordDate().getMonthValue()))).count() == 0;
                    }

                    if (monthBl5k || monthBl5w) {
                        BigDecimal monthUp = serveOrderList.stream().filter(o -> ym.equals(o.getOrderTime().getYear() + "-" + String.format("%02d", o.getCompleteTime().getMonthValue())))
                                .map(CrmCustomerOrder::getOrderMoney).reduce(BigDecimal.ZERO, BigDecimal::add);      // 一个月的充值数

                        if (monthUp.compareTo(BigDecimal.valueOf(5000)) >= 0 && monthBl5k) {
                            vermicelli.setFansType(2);
                            vermicelli.setStatus(bl5kOlb ? "0" : "1");
                            vermicelli.setRecordDate(serveOrderList.stream()
                                    .filter(o -> ym.equals(o.getOrderTime().getYear() + "-" + String.format("%02d", o.getOrderTime().getMonthValue())))
                                    .reduce(
                                            new AbstractMap.SimpleEntry<>(BigDecimal.ZERO, (LocalDateTime) null), // 初始化累计金额和订单时间
                                            (acc, order) -> {
                                                BigDecimal newAccumulated = acc.getKey().add(order.getOrderMoney());
                                                if (newAccumulated.compareTo(BigDecimal.valueOf(5000)) >= 0) {    // 统计第一次累加到5千时的订单时间
                                                    return new AbstractMap.SimpleEntry<>(newAccumulated, order.getOrderTime());
                                                } else {
                                                    return new AbstractMap.SimpleEntry<>(newAccumulated, acc.getValue());
                                                }
                                            },
                                            (acc1, acc2) -> acc2 // 合并函数，对于顺序流可以简单返回第二个累加器
                                    ).getValue().toLocalDate());
                            remoteVermicelliService.addVipVermicelli(vermicelli, SecurityConstants.INNER);
                            bl5kOlb = false;
                        }
                        if (monthUp.compareTo(BigDecimal.valueOf(50000)) >= 0 && monthBl5w) {
                            vermicelli.setFansType(3);
                            vermicelli.setStatus(bl5wOlb ? "0" : "1");
                            vermicelli.setRecordDate(serveOrderList.stream()
                                    .filter(o -> ym.equals(o.getOrderTime().getYear() + "-" + String.format("%02d", o.getOrderTime().getMonthValue())))
                                    .reduce(
                                            new AbstractMap.SimpleEntry<>(BigDecimal.ZERO, (LocalDateTime) null), // 初始化累计金额和订单时间
                                            (acc, order) -> {
                                                BigDecimal newAccumulated = acc.getKey().add(order.getOrderMoney());
                                                if (newAccumulated.compareTo(BigDecimal.valueOf(50000)) >= 0) {   // 统计第一次累加到5万时的订单时间
                                                    return new AbstractMap.SimpleEntry<>(newAccumulated, order.getOrderTime());
                                                } else {
                                                    return new AbstractMap.SimpleEntry<>(newAccumulated, acc.getValue());
                                                }
                                            },
                                            (acc1, acc2) -> acc2 // 合并函数，对于顺序流可以简单返回第二个累加器
                                    ).getValue().toLocalDate());
                            remoteVermicelliService.addVipVermicelli(vermicelli, SecurityConstants.INNER);
                            bl5wOlb = false;
                        }

                    }
                }
            }

            BigDecimal money = customerOrderMapper.calculationVipTotalMoney(customerIds, CollUtil.newArrayList(customer.getServeId()));

            boolean bl10w = vipVermicelliList.stream().filter(e -> e.getFansType() == 6).count() == 0;
            boolean bl40w = vipVermicelliList.stream().filter(e -> e.getFansType() == 7).count() == 0;
            boolean bl100w = vipVermicelliList.stream().filter(e -> e.getFansType() == 8).count() == 0;

            if (bl10w && money.compareTo(BigDecimal.valueOf(100000)) >= 0) {
                vermicelli.setFansType(6);
                vermicelli.setRecordDate(LocalDate.now());
                remoteVermicelliService.addVipVermicelli(vermicelli, SecurityConstants.INNER);
            } else if (bl40w && money.compareTo(BigDecimal.valueOf(400000)) >= 0) {
                vermicelli.setFansType(7);
                vermicelli.setRecordDate(LocalDate.now());
                remoteVermicelliService.addVipVermicelli(vermicelli, SecurityConstants.INNER);
            } else if (bl100w && money.compareTo(BigDecimal.valueOf(1000000)) >= 0) {
                vermicelli.setFansType(8);
                vermicelli.setRecordDate(LocalDate.now());
                remoteVermicelliService.addVipVermicelli(vermicelli, SecurityConstants.INNER);
            }

        }

        /** 维护一交的好友id*/
        List<CrmCustomerJoinAnchor> anchorList = anchorMapper.selectList(new LambdaQueryWrapper<CrmCustomerJoinAnchor>()
                .eq(CrmCustomerJoinAnchor::getCustomerId, customer.getCustomerId())
                .eq(CrmCustomerJoinAnchor::getExtendId, customer.getExtendId())
                .ge(CrmCustomerJoinAnchor::getReceiveTime, customer.getUpdateTime())
                .eq(CrmCustomerJoinAnchor::getStatus, 1));

        if (CollUtil.isNotEmpty(anchorList)) {
            anchorList.forEach(anchor -> {
                anchor.setFriendId(friendId);
                anchorMapper.updateById(anchor);
            });
        }

        CrmFriend crmFriend = baseMapper.selectById(friendId);
        crmFriend.setStatus(DictConstants.CUSTOMER_STATUS_ACTIVATE);
        // 修改好友状态为激活
        baseMapper.updateById(crmFriend);

        /** 新增好友和客户关联记录(如果这个客户被别人绑定,就要跟以前的好友脱绑) */
        CrmCustomerFriend customerFriend = new CrmCustomerFriend();
        customerFriend.setFriendId(friendId);
        customerFriend.setCustomerId(customer.getCustomerId());
        customerFriend.setCreateTime(LocalDateTime.now());      // 创建时间
        customerFriend.setPyExtendId(customer.getExtendId());   // PY推广用户
        customerFriend.setBeginTime(customer.getUpdateTime());  // 客户修改时间
        customerFriend.setStatus(0);

        return customerFriendMapper.insert(customerFriend);     // 新增
    }

    @Override
    public Map<String, Integer> selMycustomerRank(LocalDateTime beginTime, LocalDateTime endTime) {      // 查看实时的
        Long id = null;
        if (!SecurityUtils.getLoginUser().getSysUser().getUserRole().equals(DictConstants.SYS_USER_ROLE_ADMIN)) {
            id = SecurityUtils.getUserId();
        }

        Map<String, Integer> returnMap = new HashMap<>();           // 最总返回
        // 好友和注册
        List<FriendRegisterChargeVo> friendRegisterChargeVoList = SpringUtils.getAopProxy(this)
                .getFriendRegister(
                        ObjUtil.isNotNull(id) ? CollUtil.newArrayList(id) : CollUtil.newArrayList(),
                        ObjUtil.isNotNull(beginTime) ? beginTime.toString() : null,
                        ObjUtil.isNotNull(endTime) ? endTime.toString() : null);

        FriendRegisterChargeVo dto = new FriendRegisterChargeVo();
        dto.setId(SecurityUtils.getUserId());
        for (FriendRegisterChargeVo f : friendRegisterChargeVoList) {
            dto.setFriend(f.getFriend());
            dto.setRegister(f.getRegister());
        }

        returnMap.put("好友", ObjUtil.isNotNull(dto.getFriend()) ? dto.getFriend().intValue() : 0);
        returnMap.put("注册", ObjUtil.isNotNull(dto.getRegister()) ? dto.getRegister().intValue() : 0);

        List<Long> ids = CollUtil.newArrayList();
        if (ObjUtil.isNotNull(id)) {
            ids.add(id);
        }

        IndexUserGroupAllVo vermicelliList = remoteVermicelliService.selExtendVermicelliNumber(
                ids,
                ObjUtil.isNotNull(beginTime) ? beginTime.toString() : null,
                ObjUtil.isNotNull(endTime) ? endTime.toString() : null,
                SecurityConstants.INNER).getData();

        if (ObjUtil.isNotNull(vermicelliList)) {
            returnMap.put("200粉", vermicelliList.getFans2h());
            returnMap.put("500粉", vermicelliList.getFans5h());
            returnMap.put("5k粉", vermicelliList.getFans5k());
            returnMap.put("5w粉", vermicelliList.getFans5w());
            returnMap.put("首充", vermicelliList.getFirstCharge());
        } else {
            returnMap.put("200粉", 0);
            returnMap.put("500粉", 0);
            returnMap.put("5k粉", 0);
            returnMap.put("5w粉", 0);
            returnMap.put("首充", 0);
        }

        return returnMap;
    }

    @Override
    public List<FriendEmployeeVo> selFriendEmployeeData(FriendEmployeeDto dto) {
        List<FriendEmployeeVo> employeeVoList = CollUtil.newArrayList();        // 最终返回

        List<Long> customerIds = baseMapper.selFriendEmployeeCustomerIds(dto);          // 查询客户ID集
        SysUserVo sysUserVo = remoteUserService.getUserById(dto.getUserId(), SecurityConstants.INNER).getData();

        if (CollUtil.isNotEmpty(customerIds)) {
            /**
             * 传入二次条件
             */
            dto.setPdUserId(sysUserVo.getPdUserId());
            dto.setCustomerIds(customerIds.stream().filter(c -> ObjUtil.isNotNull(c)).toList());

            List<CrmCustomerOrder> orderList = baseMapper.selFriendEmployeeMoney(dto);      // 总业绩
            BigDecimal bigMoney = BigDecimal.ZERO;
            BigDecimal smallMoney = BigDecimal.ZERO;
            BigDecimal totalMoney = BigDecimal.ZERO;
            if (CollUtil.isNotEmpty(orderList)) {
                // 计算总充值
                bigMoney = orderList.stream().filter(o -> ObjUtil.isNotNull(o.getOrderMoney()))
                        .map(CrmCustomerOrder::getOrderMoney).max(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
                smallMoney = orderList.stream().filter(o -> ObjUtil.isNotNull(o.getOrderMoney()))
                        .map(CrmCustomerOrder::getOrderMoney).min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
                totalMoney = orderList.stream().filter(o -> ObjUtil.isNotNull(o.getOrderMoney()))
                        .map(CrmCustomerOrder::getOrderMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            employeeVoList.add(FriendEmployeeVo.builder()
                    .bigData(bigMoney)
                    .smallData(smallMoney)
                    .totalData(totalMoney)
                    .name("总业绩")
                    .build());

            List<FriendRegisterChargeVo> friendVoList = baseMapper.selFriendEmployeeFriend(dto);        // 好友数
            Long bigFriend = 0L;
            Long smallFriend = 0L;
            Long totalFriend = 0L;
            if (CollUtil.isNotEmpty(friendVoList)) {
                bigFriend = friendVoList.stream().filter(f -> ObjUtil.isNotNull(f.getFriend()))
                        .map(FriendRegisterChargeVo::getFriend).max(Long::compareTo).orElse(0L);
                smallFriend = friendVoList.stream().filter(f -> ObjUtil.isNotNull(f.getFriend()))
                        .map(FriendRegisterChargeVo::getFriend).min(Long::compareTo).orElse(0L);
                totalFriend = friendVoList.stream().filter(f -> ObjUtil.isNotNull(f.getFriend()))
                        .map(FriendRegisterChargeVo::getFriend).reduce(0L, Long::sum);
            }
            employeeVoList.add(FriendEmployeeVo.builder()
                    .bigData(BigDecimal.valueOf(bigFriend))
                    .smallData(BigDecimal.valueOf(smallFriend))
                    .totalData(BigDecimal.valueOf(totalFriend))
                    .name("好友")
                    .build());

            List<FriendRegisterChargeVo> registerVoList = baseMapper.selFriendEmployeeRegister(dto);    // 注册数
            Long bigRegister = 0L;
            Long smallRegister = 0L;
            Long totalRegister = 0L;
            if (CollUtil.isNotEmpty(registerVoList)) {
                bigRegister = registerVoList.stream().filter(f -> ObjUtil.isNotNull(f.getRegister()))
                        .map(FriendRegisterChargeVo::getRegister).max(Long::compareTo).orElse(0L);
                smallRegister = registerVoList.stream().filter(f -> ObjUtil.isNotNull(f.getRegister()))
                        .map(FriendRegisterChargeVo::getRegister).min(Long::compareTo).orElse(0L);
                totalRegister = registerVoList.stream().filter(f -> ObjUtil.isNotNull(f.getRegister()))
                        .map(FriendRegisterChargeVo::getRegister).reduce(0L, Long::sum);
            }
            employeeVoList.add(FriendEmployeeVo.builder()
                    .bigData(BigDecimal.valueOf(bigRegister))
                    .smallData(BigDecimal.valueOf(smallRegister))
                    .totalData(BigDecimal.valueOf(totalRegister))
                    .name("注册")
                    .build());

            List<CrmCustomerJoinAnchor> joinAnchorList = customerJoinAnchorMapper.selectList(new LambdaQueryWrapper<CrmCustomerJoinAnchor>()
                    .in(CollUtil.isNotEmpty(SecurityUtils.getLoginUser().getSysUser().getPdUserId()), CrmCustomerJoinAnchor::getExtendId, SecurityUtils.getLoginUser().getSysUser().getPdUserId())
                    .in(CrmCustomerJoinAnchor::getCustomerId, dto.getCustomerIds())
                    .eq(CrmCustomerJoinAnchor::getStatus, 1)     // 查询已交接的
                    .ge(CrmCustomerJoinAnchor::getReceiveTime, dto.getBeginTime())
                    .le(CrmCustomerJoinAnchor::getReceiveTime, dto.getEndTime()));
            Long bigOneJoin = 0L;
            Long smallOneJoin = 0L;
            Long totalOneJoin = 0L;
            if (CollUtil.isNotEmpty(joinAnchorList)) {
                Map<LocalDate, Long> map = joinAnchorList.stream()            // 按天分组并统计
                        .collect(Collectors.groupingBy(
                                cmfUsersJoinLive -> cmfUsersJoinLive.getReceiveTime().toLocalDate(),
                                Collectors.counting()));
                bigOneJoin = map.get(Collections.max(map.keySet()));
                smallOneJoin = map.get(Collections.min(map.keySet()));
                totalOneJoin = map.values().stream().mapToLong(Long::longValue).sum();
            }
            employeeVoList.add(FriendEmployeeVo.builder()
                    .bigData(BigDecimal.valueOf(bigOneJoin))
                    .smallData(BigDecimal.valueOf(smallOneJoin))
                    .totalData(BigDecimal.valueOf(totalOneJoin))
                    .name("一交")
                    .build());

            List<CrmCustomerJoinServe> joinServeList = customerJoinServeMapper.selectList(new LambdaQueryWrapper<CrmCustomerJoinServe>()
                    .in(CollUtil.isNotEmpty(SecurityUtils.getLoginUser().getSysUser().getPdUserId()), CrmCustomerJoinServe::getExtendId, SecurityUtils.getLoginUser().getSysUser().getPdUserId())
                    .in(CrmCustomerJoinServe::getCustomerId, dto.getCustomerIds())
                    .eq(CrmCustomerJoinServe::getStatus, 1)      // 查询已交接的
                    .ge(CrmCustomerJoinServe::getJoinTime, dto.getBeginTime())
                    .le(CrmCustomerJoinServe::getJoinTime, dto.getEndTime()));
            Long bigTwoJoin = 0L;
            Long smallTwoJoin = 0L;
            Long totalTwoJoin = 0L;
            if (CollUtil.isNotEmpty(joinServeList)) {
                Map<LocalDate, Long> map = joinServeList.stream()            // 按天分组并统计
                        .collect(Collectors.groupingBy(
                                cmfUsersJoinLive -> cmfUsersJoinLive.getJoinTime().toLocalDate(),
                                Collectors.counting()));
                bigTwoJoin = map.get(Collections.max(map.keySet()));
                smallTwoJoin = map.get(Collections.min(map.keySet()));
                totalTwoJoin = map.values().stream().mapToLong(Long::longValue).sum();
            }
            employeeVoList.add(FriendEmployeeVo.builder()
                    .bigData(BigDecimal.valueOf(bigTwoJoin))
                    .smallData(BigDecimal.valueOf(smallTwoJoin))
                    .totalData(BigDecimal.valueOf(totalTwoJoin))
                    .name("二交")
                    .build());

            List<Long> friendIds = baseMapper.selFriendEmployeeFriendIds(dto);          // 查询好友ID集

            // TODO (这里要改,区分推广和VIP粉丝达成列表)
            List<ExtendVermicelli> vermicelliList = remoteVermicelliService.selExtendVermicelliList(
                    null,
                    null,
                    friendIds,
                    dto.getBeginTime().toString(),
                    dto.getEndTime().toString(),
                    SecurityConstants.INNER).getData();

            Long bigFirstCharge = 0L;
            Long smallFirstCharge = 0L;
            Long totalFirstCharge = 0L;
            Long bigTwoHundred = 0L;
            Long smallTwoHundred = 0L;
            Long totalTwoHundred = 0L;
            Long bigFiveThousand = 0L;
            Long smallFiveThousand = 0L;
            Long totalFiveThousand = 0L;
            Long bigFiftyThousand = 0L;
            Long smallFiftyThousand = 0L;
            Long totalFiftyThousand = 0L;
            if (CollUtil.isNotEmpty(vermicelliList)) {
                // 首充数
                List<ExtendVermicelli> firstCharge = vermicelliList.stream().filter(v -> v.getFansType() == 5).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(firstCharge)) {
                    Map<LocalDate, Long> map = firstCharge.stream()            // 按天分组并统计
                            .collect(Collectors.groupingBy(
                                    extendVermicelli -> extendVermicelli.getRecordDate(),
                                    Collectors.counting()));
                    bigFirstCharge = map.get(Collections.max(map.keySet()));
                    smallFirstCharge = map.get(Collections.min(map.keySet()));
                    totalFirstCharge = map.values().stream().mapToLong(Long::longValue).sum();
                }

                // 两百粉
                List<ExtendVermicelli> twoHundred = vermicelliList.stream().filter(v -> v.getFansType() == 0).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(twoHundred)) {
                    Map<LocalDate, Long> map = twoHundred.stream()            // 按天分组并统计
                            .collect(Collectors.groupingBy(
                                    extendVermicelli -> extendVermicelli.getRecordDate(),
                                    Collectors.counting()));
                    bigTwoHundred = map.get(Collections.max(map.keySet()));
                    smallTwoHundred = map.get(Collections.min(map.keySet()));
                    totalTwoHundred = map.values().stream().mapToLong(Long::longValue).sum();
                }

                // 五千粉
                List<ExtendVermicelli> fiveThousand = vermicelliList.stream().filter(v -> v.getFansType() == 2).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(fiveThousand)) {
                    Map<LocalDate, Long> map = fiveThousand.stream()            // 按天分组并统计
                            .collect(Collectors.groupingBy(
                                    extendVermicelli -> extendVermicelli.getRecordDate(),
                                    Collectors.counting()));
                    bigFiveThousand = map.get(Collections.max(map.keySet()));
                    smallFiveThousand = map.get(Collections.min(map.keySet()));
                    totalFiveThousand = map.values().stream().mapToLong(Long::longValue).sum();
                }

                // 五万粉
                List<ExtendVermicelli> fiftyThousand = vermicelliList.stream().filter(v -> v.getFansType() == 3).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(fiftyThousand)) {
                    Map<LocalDate, Long> map = fiftyThousand.stream()            // 按天分组并统计
                            .collect(Collectors.groupingBy(
                                    extendVermicelli -> extendVermicelli.getRecordDate(),
                                    Collectors.counting()));
                    bigFiftyThousand = map.get(Collections.max(map.keySet()));
                    smallFiftyThousand = map.get(Collections.min(map.keySet()));
                    totalFiftyThousand = map.values().stream().mapToLong(Long::longValue).sum();
                }
            }
            employeeVoList.add(FriendEmployeeVo.builder()
                    .bigData(BigDecimal.valueOf(bigFirstCharge))
                    .smallData(BigDecimal.valueOf(smallFirstCharge))
                    .totalData(BigDecimal.valueOf(totalFirstCharge))
                    .name("首充粉")
                    .build());

            employeeVoList.add(FriendEmployeeVo.builder()
                    .bigData(BigDecimal.valueOf(bigTwoHundred))
                    .smallData(BigDecimal.valueOf(smallTwoHundred))
                    .totalData(BigDecimal.valueOf(totalTwoHundred))
                    .name("两百粉")
                    .build());

            employeeVoList.add(FriendEmployeeVo.builder()
                    .bigData(BigDecimal.valueOf(bigFiveThousand))
                    .smallData(BigDecimal.valueOf(smallFiveThousand))
                    .totalData(BigDecimal.valueOf(totalFiveThousand))
                    .name("五千粉")
                    .build());

            employeeVoList.add(FriendEmployeeVo.builder()
                    .bigData(BigDecimal.valueOf(bigFiftyThousand))
                    .smallData(BigDecimal.valueOf(smallFiftyThousand))
                    .totalData(BigDecimal.valueOf(totalFiftyThousand))
                    .name("五万粉")
                    .build());
        }
        return employeeVoList;
    }

    /*------------------------------------------------------内部调用方法------------------------------------------------------------*/

    @Override
    public int ProbationPeriod() {
        Date date1 = DateUtil.getAfterDate(new Date(), 0, 0, -15, 0, 0, 0);
        Date date2 = DateUtil.getAfterDate(new Date(), 0, 0, -30, 0, 0, 0);

        List<Long> ids = baseMapper.friendSchedulesSelect(date1, date2);        // 返回要修改的好友id集
        if (CollUtil.isNotEmpty(ids)) {
            // 批量修改交接记录
            // SpringUtils.getAopProxy(this).updateReceiveRecord(ids, 0);

            // 更改好友领取表状态为流失
            // TODO

            baseMapper.friendSchedulesUpdate(ids);       // 批量修改好友状态和滞空领取人

            //   customerFriendMapper.updateAllLose(ids);     // 好友和客户关系解绑
            return 0;
        } else {
            return 0;
        }
    }

    @Override
    public List<CrmCustomerFriend> selCustomerFriendList(List<Long> ids) {
        // 收集好友ID集
        List<Long> friendIds = customerFriendMapper.selectList(new LambdaQueryWrapper<CrmCustomerFriend>()
                .in(CrmCustomerFriend::getCustomerId, ids)
                .eq(CrmCustomerFriend::getStatus, 0)
        ).stream().map(CrmCustomerFriend::getFriendId).distinct().toList();

        List<CrmCustomerFriend> customerFriendList = customerFriendMapper.selectList(new LambdaQueryWrapper<CrmCustomerFriend>()
                .in(CrmCustomerFriend::getFriendId, friendIds)
                .eq(CrmCustomerFriend::getStatus, 0));


        return customerFriendList;
    }

    @Override
    @Synchronized
    public List<FriendRegisterChargeVo> getFriendRegister(List<Long> ids, String beginTime, String endTime) {
        LocalDateTime begin = null;
        LocalDateTime end = null;

        if (ObjUtil.isNotNull(beginTime)) {
            begin = LocalDateTime.parse(beginTime);
        }
        if (ObjUtil.isNotNull(endTime)) {
            end = LocalDateTime.parse(endTime);
        }

        /**
         * 创建入参条件
         */
        FriendRegisterChargeDto dto = new FriendRegisterChargeDto();
        dto.setBeginTime(begin);
        dto.setEndTime(end);
        dto.setUserIds(ids);

        List<FriendRegisterChargeVo> returnList = new ArrayList<>();               // 返回的列表
        List<FriendRegisterChargeVo> friendList = baseMapper.getFriend(dto);       // 好友数
        List<FriendRegisterChargeVo> registerList = baseMapper.getRegister(dto);   // 注册数

        if (CollUtil.isNotEmpty(ids)) {
            ids.forEach(i -> {
                FriendRegisterChargeVo vo = new FriendRegisterChargeVo();
                vo.setId(i);
                FriendRegisterChargeVo friendVo = friendList.stream().filter(f -> f.getId().equals(i)).findFirst().orElse(null);
                if (ObjUtil.isNotNull(friendVo)) {
                    vo.setFriend(friendVo.getFriend());
                    vo.setName(friendVo.getName());
                }
                FriendRegisterChargeVo registerVo = registerList.stream().filter(r -> r.getId().equals(i)).findFirst().orElse(null);
                if (ObjUtil.isNotNull(registerVo)) {
                    vo.setRegister(registerVo.getRegister());
                }
                returnList.add(vo);
            });
        } else {
            friendList.forEach(f -> {
                FriendRegisterChargeVo registerVo = registerList.stream().filter(r -> r.getId().equals(f.getId())).findFirst().orElse(null);
                f.setRegister(registerVo.getRegister());
                returnList.add(f);
            });
        }
        return returnList;
    }

    @Override
    @Synchronized
    public List<FriendRegisterChargeVo> getFriendRegisterDept(Long deptId, Long parentId, Integer hierarchy, String beginTime, String endTime) {
        LocalDateTime begin = LocalDateTime.parse(beginTime);
        LocalDateTime end = LocalDateTime.parse(endTime);

        /**
         * 创建入参条件
         */
        FriendRegisterChargeDto dto = new FriendRegisterChargeDto();
        dto.setBeginTime(begin);
        dto.setEndTime(end);
        dto.setFindInSetDeptId(deptId);
        dto.setParentId(parentId);
        dto.setHierarchy(hierarchy);

        List<FriendRegisterChargeVo> friendList = baseMapper.getFriendDept(dto);            // 好友数
        List<FriendRegisterChargeVo> registerList = baseMapper.getRegisterDept(dto);         // 注册数
//        List<FriendRegisterChargeVo> firstChargeList = baseMapper.getFirstChargeDept(dto);   // 首充数
        friendList.stream().forEach(f -> {
            FriendRegisterChargeVo register = registerList.stream().filter(r -> r.getId().equals(f.getId())).findFirst().orElse(null);
            f.setRegister(register.getRegister());
//            FriendRegisterChargeVo firstCharge = firstChargeList.stream().filter(r -> r.getId().equals(f.getId())).findFirst().orElse(null);
//            f.setFirstCharge(firstCharge.getFirstCharge());
        });
        return friendList;
    }

    @Override
    @Synchronized
    public List<FriendRegisterChargeVo> getFriendRegisterDeptTimeGroup(Long deptId, String beginTime, String endTime, Integer expType) {
        LocalDateTime begin = LocalDateTime.parse(beginTime);
        LocalDateTime end = LocalDateTime.parse(endTime);

        /**
         * 创建入参条件
         */
        FriendRegisterChargeDto dto = new FriendRegisterChargeDto();
        dto.setBeginTime(begin);
        dto.setEndTime(end);
        dto.setExpType(expType);
        dto.setDeptId(deptId);

        List<FriendRegisterChargeVo> friendRanking = baseMapper.getFriendDeptTimeGroup(dto);
        List<FriendRegisterChargeVo> registerRanking = baseMapper.getRegisterDeptTimeGroup(dto);
//        List<FriendRegisterChargeVo> firstChargeRanking = baseMapper.getFirstChargeDeptTimeGroup(dto);

        List<LocalDate> dateList = CollUtil.newArrayList();     // 时间分割
        if (expType == 1 || expType == 2) {      // 按天分组
            dateList = LocalDateUtil.getDatesBetween(begin.toLocalDate(), end.toLocalDate());
        } else if (expType == 0) {           // 按月分组
            dateList = LocalDateUtil.getMonthsBetween(begin.toLocalDate(), end.toLocalDate());
        }

        List<FriendRegisterChargeVo> dtoList = CollUtil.newArrayList();        // 返回
        for (LocalDate date : dateList) {
            FriendRegisterChargeVo vo = new FriendRegisterChargeVo();
            vo.setDate(date);          // 时间
            FriendRegisterChargeVo friend = friendRanking.stream().filter(f -> ObjUtil.isNotNull(f.getDate()) && f.getDate().compareTo(date) == 0).findFirst().orElse(null);
            if (ObjUtil.isNotNull(friend)) {
                vo.setFriend(friend.getFriend());
            } else {
                vo.setFriend(0L);      // 好友
            }
            FriendRegisterChargeVo register = registerRanking.stream().filter(r -> ObjUtil.isNotNull(r.getDate()) && r.getDate().compareTo(date) == 0).findFirst().orElse(null);
            if (ObjUtil.isNotNull(register)) {
                vo.setRegister(register.getRegister());
            } else {
                vo.setRegister(0L);    // 注册
            }
//            FriendRegisterChargeVo charge = firstChargeRanking.stream().filter(c -> ObjUtil.isNotNull(c.getDate()) && c.getDate().compareTo(date) == 0).findFirst().orElse(null);
//            if (ObjUtil.isNotNull(charge)) {
//                vo.setFirstCharge(charge.getFirstCharge());
//            } else {
//                vo.setFirstCharge(0L); // 首充
//            }
            dtoList.add(vo);
        }

        return dtoList;
    }

    @Override
    @Synchronized
    public List<FriendRegisterChargeVo> getFriendRegisterUserTimeGroup(List<Long> ids, String beginTime, String endTime, Integer expType) {
        LocalDateTime begin = LocalDateTime.parse(beginTime);
        LocalDateTime end = LocalDateTime.parse(endTime);

        /**
         * 创建入参条件
         */
        FriendRegisterChargeDto dto = new FriendRegisterChargeDto();
        dto.setBeginTime(begin);
        dto.setEndTime(end);
        dto.setExpType(expType);
        dto.setUserIds(ids);

        List<FriendRegisterChargeVo> friendRanking = baseMapper.getFriendUserTimeGroup(dto);
        List<FriendRegisterChargeVo> registerRanking = baseMapper.getRegisterUserTimeGroup(dto);
//        List<FriendRegisterChargeVo> firstChargeRanking = baseMapper.getFirstChargeUserTimeGroup(dto);

        List<LocalDate> dateList = CollUtil.newArrayList();     // 时间分割
        if (expType == 1 || expType == 2) {      // 按天分组
            dateList = LocalDateUtil.getDatesBetween(begin.toLocalDate(), end.toLocalDate());
        } else if (expType == 0) {           // 按月分组
            dateList = LocalDateUtil.getMonthsBetween(begin.toLocalDate(), end.toLocalDate());
        }

        List<FriendRegisterChargeVo> dtoList = CollUtil.newArrayList();        // 返回
        for (LocalDate date : dateList) {
            FriendRegisterChargeVo vo = new FriendRegisterChargeVo();
            vo.setDate(date);          // 时间
            FriendRegisterChargeVo friend = friendRanking.stream().filter(f -> ObjUtil.isNotNull(f.getDate()) && f.getDate().compareTo(date) == 0).findFirst().orElse(null);
            if (ObjUtil.isNotNull(friend)) {
                vo.setFriend(friend.getFriend());
            } else {
                vo.setFriend(0L);      // 好友
            }
            FriendRegisterChargeVo register = registerRanking.stream().filter(r -> ObjUtil.isNotNull(r.getDate()) && r.getDate().compareTo(date) == 0).findFirst().orElse(null);
            if (ObjUtil.isNotNull(register)) {
                vo.setRegister(register.getRegister());
            } else {
                vo.setRegister(0L);    // 注册
            }
//            FriendRegisterChargeVo charge = firstChargeRanking.stream().filter(c -> ObjUtil.isNotNull(c.getDate()) && c.getDate().compareTo(date) == 0).findFirst().orElse(null);
//            if (ObjUtil.isNotNull(charge)) {
//                vo.setFirstCharge(charge.getFirstCharge());
//            } else {
//                vo.setFirstCharge(0L); // 首充
//            }
            dtoList.add(vo);
        }

        return dtoList;
    }

    @Override
    @Synchronized
    public Map<String, List<FriendRegisterChargeVo>> getFriendRegisterRanking(List<Long> ids, String beginTime, String endTime) {
        LocalDateTime begin = LocalDateTime.parse(beginTime);
        LocalDateTime end = LocalDateTime.parse(endTime);

        /**
         * 创建入参条件
         */
        FriendRegisterChargeDto dto = new FriendRegisterChargeDto();
        dto.setBeginTime(begin);
        dto.setEndTime(end);
        dto.setDeptType(DictConstants.SYS_DEPT_TYPE_EXTEND);
        dto.setFindInSetDeptId(88L);        // 推广部

        List<FriendRegisterChargeVo> friendRanking = baseMapper.getFriend(dto).stream()
                .sorted(Comparator.comparing(FriendRegisterChargeVo::getFriend).reversed())        // 倒序排序
                .collect(Collectors.toList());                 // 好友排名
        for (int i = 0; i < friendRanking.size(); i++) {
            friendRanking.get(i).setRanking(i + 1);
        }
        List<FriendRegisterChargeVo> registerRanking = baseMapper.getRegister(dto).stream()
                .sorted(Comparator.comparing(FriendRegisterChargeVo::getRegister).reversed())      // 倒序排序
                .collect(Collectors.toList());                 // 注册排名
        for (int i = 0; i < registerRanking.size(); i++) {
            registerRanking.get(i).setRanking(i + 1);
        }
//        List<FriendRegisterChargeVo> firstChargeRanking = baseMapper.getFirstCharge(dto).stream()
//                .sorted(Comparator.comparing(FriendRegisterChargeVo::getFirstCharge).reversed())   // 倒序排序
//                .collect(Collectors.toList());                 // 首充排名
//        for (int i = 0; i < firstChargeRanking.size(); i++) {
//            firstChargeRanking.get(i).setRanking(i + 1);
//        }

        Map<String, List<FriendRegisterChargeVo>> returnMap = new HashMap<>();
        if (CollUtil.isNotEmpty(ids)) {
            returnMap.put("好友排名", friendRanking.stream().filter(f -> ids.contains(f.getId())).collect(Collectors.toList()));
            returnMap.put("注册排名", registerRanking.stream().filter(r -> ids.contains(r.getId())).collect(Collectors.toList()));
//            returnMap.put("首充排名", firstChargeRanking.stream().filter(c -> ids.contains(c.getId())).collect(Collectors.toList()));
        } else {
            returnMap.put("好友排名", friendRanking);
            returnMap.put("注册排名", registerRanking);
//            returnMap.put("首充排名", firstChargeRanking);
        }

        return returnMap;
    }

    @Override
    @Synchronized
    public Map<String, List<FriendRegisterChargeVo>> getFriendRegisterDeptRanking(Long findInSetDeptId, Integer hierarchy, String beginTime, String endTime) {
        /**
         * 创建入参条件
         */
        FriendRegisterChargeDto dto = new FriendRegisterChargeDto();
        if (ObjUtil.isNotNull(beginTime) && ObjUtil.isNotNull(endTime)) {
            LocalDateTime begin = LocalDateTime.parse(beginTime);
            LocalDateTime end = LocalDateTime.parse(endTime);
            dto.setBeginTime(begin);
            dto.setEndTime(end);
        }
        dto.setFindInSetDeptId(findInSetDeptId);
        dto.setDeptType(DictConstants.SYS_DEPT_TYPE_EXTEND);
        dto.setHierarchy(hierarchy);

        List<FriendRegisterChargeVo> friendRanking = baseMapper.getFriendDept(dto).stream()
                .sorted(Comparator.comparing(FriendRegisterChargeVo::getFriend).reversed())        // 倒序排序
                .collect(Collectors.toList());                 // 好友排名
        for (int i = 0; i < friendRanking.size(); i++) {
            friendRanking.get(i).setRanking(i + 1);
        }
        List<FriendRegisterChargeVo> registerRanking = baseMapper.getRegisterDept(dto).stream()
                .sorted(Comparator.comparing(FriendRegisterChargeVo::getRegister).reversed())      // 倒序排序
                .collect(Collectors.toList());                 // 注册排名
        for (int i = 0; i < registerRanking.size(); i++) {
            registerRanking.get(i).setRanking(i + 1);
        }
//        List<FriendRegisterChargeVo> firstChargeRanking = baseMapper.getFirstChargeDept(dto).stream()
//                .sorted(Comparator.comparing(FriendRegisterChargeVo::getFirstCharge).reversed())   // 倒序排序
//                .collect(Collectors.toList());                 // 首充排名
//        for (int i = 0; i < firstChargeRanking.size(); i++) {
//            firstChargeRanking.get(i).setRanking(i + 1);
//        }

        Map<String, List<FriendRegisterChargeVo>> returnMap = new HashMap<>();
        returnMap.put("好友排名", friendRanking);
        returnMap.put("注册排名", registerRanking);
//        returnMap.put("首充排名", firstChargeRanking);

        return returnMap;
    }

    @Override
    public List<CrmCommonFriendVo> selReceiveCommonFriend(Page<CrmCommonFriendVo> page, FriendQuery query, Long userId) {
        return baseMapper.selReceiveCommonFriend(page, query, userId);
    }

    /**
     * 轮询客户列表
     *
     * @param page
     * @param query
     * @return
     */
    @DataScope(deptAlias = "d", userAlias = "u")
    @Override
    public List<PollingFriendVo> pollingFriendList(Page<PollingFriendVo> page, PollingFriendQuery query) {
        List<PollingFriendVo> list = baseMapper.pollingFriendList(page, query);
        list.forEach(l -> {
            l.setPollingApiChannelName(StringUtils.isEmpty(l.getPollingApiChannelName()) ? "" : l.getPollingApiChannelName());
            l.setContactModeName(StringUtils.isEmpty(l.getContactModeName()) ? "" : l.getContactModeName());
            l.setMainChanneName(StringUtils.isEmpty(l.getMainChanneName()) ? "" : l.getMainChanneName());
            l.setSubChanneName(StringUtils.isEmpty(l.getSubChanneName()) ? "" : l.getSubChanneName());
            l.setContactPhone(StringUtils.isEmpty(l.getContactPhone()) ? "" : "(" + l.getContactPhone() + ")");
            l.setPollingApiPhone(StringUtils.isEmpty(l.getPollingApiPhone()) ? "" : "(" + l.getPollingApiPhone() + ")");

            l.setPollingContactMode(l.getPollingApiChannelName() + l.getPollingApiPhone());
            l.setFriendContactMode(l.getContactModeName() + l.getContactPhone());
            l.setChannelName(l.getMainChanneName() + "-" + l.getSubChanneName());
        });
        return list;
    }

    /**
     * 轮询无效列表
     *
     * @param page
     * @param query
     * @return
     */
    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<PollingFriendInvalidVo> pollingInvalidList(Page<PollingFriendInvalidVo> page, PollingFriendQuery query) {
        List<PollingFriendInvalidVo> list = baseMapper.pollingInvalidList(page, query);
        list.forEach(l -> {
            l.setPollingApiChannelName(StringUtils.isEmpty(l.getPollingApiChannelName()) ? "" : l.getPollingApiChannelName());
            l.setContactModeName(StringUtils.isEmpty(l.getContactModeName()) ? "" : l.getContactModeName());
            l.setMainChanneName(StringUtils.isEmpty(l.getMainChanneName()) ? "" : l.getMainChanneName());
            l.setSubChanneName(StringUtils.isEmpty(l.getSubChanneName()) ? "" : l.getSubChanneName());
            l.setContactPhone(StringUtils.isEmpty(l.getContactPhone()) ? "" : "(" + l.getContactPhone() + ")");
            l.setPollingApiPhone(StringUtils.isEmpty(l.getPollingApiPhone()) ? "" : "(" + l.getPollingApiPhone() + ")");

            l.setPollingContactMode(l.getPollingApiChannelName() + l.getPollingApiPhone());
            l.setFriendContactMode(l.getContactModeName() + l.getContactPhone());
            l.setChannelName(l.getMainChanneName() + "-" + l.getSubChanneName());
        });
        return list;
    }

    /**
     * 轮询审核
     *
     * @param
     * @param state
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int pollingState(String reviewRemark, Long friendId, Long state) {
        // 审核按钮判断
        // 找到最新的审核数据
        CrmFriendPollingReview verifyReview = friendPollingReviewService.lambdaQuery()
                .eq(CrmFriendPollingReview::getFriendId, friendId)
                .orderByDesc(CrmFriendPollingReview::getCreateTime)
                .last("limit 1")
                .one();

        if (Objects.nonNull(verifyReview) && verifyReview.getState() != null) {
            if (2 == verifyReview.getState()) {
                throw new ServiceException("该数据已处理，请刷新后重试");
            } else if (verifyReview.getState() == state) {
                throw new ServiceException("该数据已处理，请刷新后重试");
            } else if (3 == verifyReview.getState() && (2 == state || 4 == state)) {
                throw new ServiceException("该数据已处理，请刷新后重试");
            } else if (4 == verifyReview.getState() && (2 == state || 3 == state)) {
                throw new ServiceException("该数据已处理，请刷新后重试");
            }
        }

        CrmFriendPollingReview crmFriendPollingReview = new CrmFriendPollingReview();
        crmFriendPollingReview.setFriendId(friendId);
        crmFriendPollingReview.setState(state);
        crmFriendPollingReview.setReviewRemark(reviewRemark);
        crmFriendPollingReview.setCreateBy(SecurityUtils.getUserId());
        crmFriendPollingReview.setCreateTime(LocalDateTime.now());
        friendPollingReviewService.save(crmFriendPollingReview);
        // 已确认
        if (state == 2) {
            this.lambdaUpdate().eq(CrmFriend::getFriendId, crmFriendPollingReview.getFriendId())
                    .set(CrmFriend::getPollingType, 2)
                    .set(CrmFriend::getUpdateBy, SecurityUtils.getUserId())
                    .set(CrmFriend::getUpdateTime, LocalDateTime.now()).update();
            ;
        }
        return 1;
    }

    @Override
    public String pollingImport(List<PollingFriendDto> pollingFriendList, boolean updateSupport) {
        RemoteDictDataService remoteDictDataService = SpringUtils.getBean(RemoteDictDataService.class);
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (PollingFriendDto pollingFriendDto : pollingFriendList) {
            try {
                // 验证是否存在这个客户(推广id、投手id、昵称一样)
                CrmFriend friend = this.lambdaQuery()
                        .eq(CrmFriend::getPitcherId, pollingFriendDto.getPitcherId())
                        .eq(CrmFriend::getExtendId, pollingFriendDto.getExtendId())
                        .eq(CrmFriend::getFriendName, pollingFriendDto.getFriendName())
                        .last("LIMIT 1").one();
                Boolean isAdd = false;
                if (ObjUtil.isNull(friend)) {
                    isAdd = true;
                } else {
                    friend = new CrmFriend();
                }


                CrmFriend newFriend = new CrmFriend();
                newFriend.setPollingType(1L);
                newFriend.setStatus(0);
                BeanValidators.validateWithException(validator, friend);
                BeanUtil.copyProperties(pollingFriendDto, newFriend);
                // 导入数据处理
                newFriend.setCreateTime(newFriend.getRecordDate().atStartOfDay());
                // 渠道
                CrmFriendChannel mainChanne = friendChannelService.lambdaQuery()
                        .eq(CrmFriendChannel::getChannelName, pollingFriendDto.getMainChannelName())
                        .last("LIMIT 1").one();
                CrmFriendChannel subChanne = friendChannelService.lambdaQuery()
                        .eq(CrmFriendChannel::getChannelName, pollingFriendDto.getSubChannelName())
                        .last("LIMIT 1").one();
                newFriend.setMainChannelId(mainChanne.getChannelId());
                newFriend.setSubChannelId(subChanne.getChannelId());

                // 联系渠道（api）
                DictDataQuery pollingDictQuery = new DictDataQuery();
                pollingDictQuery.setDictLabel(pollingFriendDto.getPollingApiChannel());
                pollingDictQuery.setDictType("crm_polling_channel_name");
                List<SysDictData> pollingDictData = remoteDictDataService.getList(pollingDictQuery, SecurityConstants.INNER).getData();
                if (pollingDictData.size() > 0) {
                    newFriend.setPollingApiChannel(Integer.valueOf(pollingDictData.get(0).getDictValue()));
                }

                // 第三方联系渠道
                DictDataQuery dictQuery = new DictDataQuery();
                dictQuery.setDictLabel(pollingFriendDto.getContactModeName());
                dictQuery.setDictType("crm_friend_contact_type");
                List<SysDictData> dictData = remoteDictDataService.getList(dictQuery, SecurityConstants.INNER).getData();
                if (dictData.size() > 0) {
                    newFriend.setContactMode(Integer.valueOf(dictData.get(0).getDictValue()));
                }
                // 导入
                if (ObjUtil.isNull(isAdd)) {
                    newFriend.setCreateBy(SecurityUtils.getUserId());
                    baseMapper.insert(newFriend);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、客户 ").append(newFriend.getFriendName()).append(" 导入成功");
                } else if (updateSupport) {
                    newFriend.setUpdateBy(SecurityUtils.getUserId());
                    newFriend.setUpdateTime(LocalDateTime.now());
                    baseMapper.updateById(newFriend);
                    successNum++;
                    successMsg.append("<br/>").append(successNum).append("、客户 ").append(newFriend.getFriendName()).append(" 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、客户 ").append(newFriend.getFriendName()).append(" 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、客户 " + pollingFriendDto.getFriendName() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    @Override
    public Boolean pollingEdit(CrmFriend friend) {

        return this.lambdaUpdate().eq(CrmFriend::getFriendId, friend.getFriendId())
                .set(StrUtil.isNotEmpty(friend.getPollingRemark()), CrmFriend::getPollingRemark, friend.getPollingRemark())
                .set(Objects.nonNull(friend.getContactMode()), CrmFriend::getContactMode, friend.getContactMode())
                .set(StrUtil.isNotEmpty(friend.getContactPhone()), CrmFriend::getContactPhone, friend.getContactPhone())
                .set(CrmFriend::getUpdateBy, SecurityUtils.getUserId())
                .set(CrmFriend::getUpdateTime, LocalDateTime.now())
                .update();
    }

    @Override
    public List<CrmFriendPollingChat> chatList(String pollingApiUserId) {
        List<CrmFriendPollingChat> list = friendPollingChatMapper.selectList(new QueryWrapper<CrmFriendPollingChat>()
                .eq("polling_api_user_id", pollingApiUserId)
                .orderByDesc("sequence_id"));
        return list;
    }

    @DataScope(deptAlias = "d", userAlias = "u")
    @Override
    public List<PollingStatisticsVo> statisticsList(Page<PollingStatisticsVo> page, PollingFriendQuery query) {
        List<PollingStatisticsVo> list = baseMapper.statisticsList(page, query);
        //effectiveRate、registerRate保留两位小数
        list.forEach(item -> {
            item.setEffectiveRate(new BigDecimal(item.getEffectiveRate()).setScale(2, RoundingMode.HALF_UP).toString());
            item.setRegisterRate(new BigDecimal(item.getRegisterRate()).setScale(2, RoundingMode.HALF_UP).toString());
        });
        return list;
    }

    @Override
    @DataScope(deptAlias = "d", userAlias = "u")
    public List<VipRechargeOrderVo> getVipRechargeOrder(Page page, VipRechargeOrderQuery query) {
        return baseMapper.getVipRechargeOrder(page, query);
    }

    @Override
    @Transactional (rollbackFor = Exception.class)
    @DataScope(deptAlias = "d", userAlias = "u")
    public String pollingBatchConfirm(PollingFriendQuery query) {
        query.setState(1L);
        List<PollingFriendInvalidVo> pollingInvalidList = baseMapper.pollingInvalidList(null, query);
        if (pollingInvalidList.size() == 0) {
            // 提示无确认数据
            throw new ServiceException("无确认数据");
        }
        List<Long> friendIds = pollingInvalidList.stream().map(PollingFriendInvalidVo::getFriendId).collect(Collectors.toList());
        List<CrmFriend> friendList = baseMapper.selectBatchIds(friendIds);

        List<CrmFriendPollingReview> friendPollingReviewList = new ArrayList<>();

        friendList.forEach(friend -> {
            friend.setPollingType(2L);
            friend.setUpdateBy(SecurityUtils.getUserId());
            friend.setUpdateTime(LocalDateTime.now());

            CrmFriendPollingReview crmFriendPollingReview = new CrmFriendPollingReview();
            crmFriendPollingReview.setFriendId(friend.getFriendId());
            crmFriendPollingReview.setState(2L);
            friendPollingReviewList.add(crmFriendPollingReview);
        });
        // 修改轮询类型
        this.updateBatchById(friendList);
        // 新增审核记录
        friendPollingReviewService.saveBatch(friendPollingReviewList);
        return "1";
    }

    /**
     * 投放 - 好友列表
     * @param page
     * @param query
     * @return
     */
    @Override
    @DataScope(deptAlias = "d4", userAlias = "u4")
    public List<FriendDetailsVo> pitcherListByFriend(Page<FriendDetailsVo> page, CustomerFriendQuery query) {
        List<FriendDetailsVo> list = baseMapper.pitcherSelectFriendList(page, query);
        for (FriendDetailsVo v : list) {
            // 拆分出部门-团队-小组
            String deptAncestorsNames = v.getReceiveAncestorsNames() + "-" + v.getReceiveDeptName();
            List<String> stringList = Arrays.stream(deptAncestorsNames.split("-")).collect(Collectors.toList());      // 拆分部门名
            int size = stringList.size() - 2;       // 要截取的
            if (size == 1) {
                v.setTeamName1(stringList.get(stringList.size() - 1));
            } else if (size == 2) {
                v.setTeamName1(stringList.get(stringList.size() - 2));
                v.setTeamName2(stringList.get(stringList.size() - 1));
            } else if (size == 3) {
                v.setTeamName1(stringList.get(stringList.size() - 3));
                v.setTeamName2(stringList.get(stringList.size() - 2));
                v.setTeamName3(stringList.get(stringList.size() - 1));
            }
        }

        return list;
    }

    /**
     * 投放订单逻辑
     * @param addFriend
     * @param beforeFriend
     */
    public void rechargeRecordPitcher(CrmFriend addFriend,CrmFriend beforeFriend,String customerId){
        // 找到投放订单关联表的时间
        CrmCustomerFriend customerFriend = customerFriendService.getOne(new LambdaQueryWrapper<CrmCustomerFriend>()
                .eq(CrmCustomerFriend::getFriendId, addFriend.getFriendId())
                .eq(CrmCustomerFriend::getStatus, 0L).last("limit 1"));
        if (Objects.nonNull(customerFriend)) {
            // 找到投放订单
            List<CrmCustomerOrder> pollingRecordList = customerOrderService.list(new LambdaQueryWrapper<CrmCustomerOrder>()
                    .eq(CrmCustomerOrder::getCustomerId, customerId)
                    .ge(CrmCustomerOrder::getOrderTime, customerFriend.getBeginTime()));
            // 投放部门id
            Long deptId = null;
            if (Objects.nonNull(addFriend.getPitcherId())) {
                UserQuery userQuery = new UserQuery();
                userQuery.setUserId(addFriend.getPitcherId());
                List<SysUserVo> userList = remoteUserService.getList(userQuery, SecurityConstants.INNER).getData();
                if (userList.size() > 0) {
                    deptId = userList.get(0).getDeptId();
                }
            }


            // 保存投放订单变更记录表
            List<CrmPitcherInvalidOrder> addPitcherInvalidOrder = new ArrayList<>();
            for (int i = 0; i < pollingRecordList.size(); i++) {
                CrmCustomerOrder customerOrder = pollingRecordList.get(i);
                CrmPitcherInvalidOrder pitcherInvalidOrder = new CrmPitcherInvalidOrder();
                pitcherInvalidOrder.setOrderId(customerOrder.getOrderId());
                pitcherInvalidOrder.setBeforeId(beforeFriend.getPitcherId());
                pitcherInvalidOrder.setAfterId(addFriend.getPitcherId());
                pitcherInvalidOrder.setRemark("投手变更");
                addPitcherInvalidOrder.add(pitcherInvalidOrder);
                pollingRecordList.get(i).setPitcherId(addFriend.getPitcherId());
                pollingRecordList.get(i).setPitcherDeptId(deptId);
            }
            pitcherInvalidOrderService.saveBatch(addPitcherInvalidOrder);
            // 修改订单表
            customerOrderService.updateBatchById(pollingRecordList);
        }
    }

    /**
     * 修改交接记录(去修改交接记录的充值金额、可以修改交接记录的流失时间)
     *
     * @param friendIds 好友ID集
     * @param type      是否流失(0:修改、1:不修改)
     */
    public void updateReceiveRecord(List<Long> friendIds, Integer type) {
/*        List<CrmCustomerFriend> crmCustomerFriendList = customerFriendMapper.selectList(new LambdaQueryWrapper<CrmCustomerFriend>()
                .in(CrmCustomerFriend::getFriendId, friendIds)
                .eq(CrmCustomerFriend::getStatus, 0));      // 查询好友和客户的关联记录集
        List<CrmFriendReceiveRecord> receiveRecordList = friendReceiveRecordMapper.selcetRelevanceList(friendIds);         // 查询好友的交接记录(用来查订单的时间范围)

        // 修改交接记录时统计的累充金额
        if (CollUtil.isNotEmpty(crmCustomerFriendList)) {        // 如果有关联关系就去找
            friendIds.forEach(f -> {
                List<CrmCustomerFriend> customerFriends = crmCustomerFriendList.stream()        // 赛选出这个好友下的客户ID集
                        .filter(c -> c.getFriendId().equals(f)).collect(Collectors.toList());
                CrmFriendReceiveRecord receiveRecord = receiveRecordList.stream().filter(r -> r.getFriendId().equals(f)).findFirst().orElse(null);
                if (CollUtil.isNotEmpty(customerFriends) && ObjUtil.isNotNull(receiveRecord)) {
                    List<CrmCustomerOrder> orderList = customerOrderMapper.selectList(new LambdaQueryWrapper<CrmCustomerOrder>()        // 查询这些客户的充值订单
                            .in(CrmCustomerOrder::getCustomerId, customerFriends.stream().map(CrmCustomerFriend::getCustomerId).toList())
                            .ge(CrmCustomerOrder::getOrderTime, receiveRecord.getReceiveTime()));
                    if (CollUtil.isNotEmpty(orderList)) {
                        BigDecimal big = orderList.stream().map(CrmCustomerOrder::getOrderMoney).reduce(BigDecimal.ZERO, BigDecimal::add);
                        if (big.compareTo(BigDecimal.ZERO) > 0) {
                            receiveRecord.setTopUp(big);
                            friendReceiveRecordMapper.updateById(receiveRecord);        // 修改金额
                        }
                    }
                }
            });
        }*/
        // 修改交接记录的流失时间
        if (type == 0) {
            // 批量修改交接记录的流失时间(批量数据过来，单条单条处理)
            // TODO
        }
    }
}
