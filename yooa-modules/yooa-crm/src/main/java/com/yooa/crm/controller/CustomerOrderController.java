package com.yooa.crm.controller;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.domain.R;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.security.annotation.InnerAuth;
import com.yooa.crm.api.domain.CrmCustomerOrder;
import com.yooa.crm.api.domain.query.CustomerRewardQuery;
import com.yooa.crm.api.domain.query.RechargeRecordPitcherQuery;
import com.yooa.crm.api.domain.vo.*;
import com.yooa.crm.service.CustomerOrderService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单 - 控制层
 */
@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/customer/order")
public class CustomerOrderController extends BaseController {

    private final CustomerOrderService customerOrderService;

    /**
     * 客户详情 - 充值记录
     */
    @GetMapping("/selRechargeRecord")
    public AjaxResult selRechargeRecord(@Valid @NotNull(message = "缺少参数!") @RequestParam(required = false) List<Long> customerIds, @Valid @NotNull(message = "缺少参数!") @RequestParam(required = false) Long userId) {
        if (CollUtil.isEmpty(customerIds) || userId == null) {
            return success(null);
        }
        return success(customerOrderService.selRechargeRecord(customerIds, userId));
    }

    /**
     * 客户详情 - 导出充值记录
     */
    @PostMapping("/selRechargeRecord/export")
    public void exportRechargeRecord(HttpServletResponse response, @Valid @NotNull(message = "缺少参数!") @RequestParam(required = false) List<Long> customerIds, @Valid @NotNull(message = "缺少参数!") @RequestParam(required = false) Long userId) {
        if (CollUtil.isNotEmpty(customerIds) && ObjectUtil.isNotNull(userId)) {
            List<CrmCustomerOrder> orderList = customerOrderService.selRechargeRecord(customerIds, userId).getOrderList();
            ExcelUtil<CrmCustomerOrder> util = new ExcelUtil<>(CrmCustomerOrder.class);
            util.exportExcel(response, orderList, "充值记录");
        }
    }

    /**
     * 投放 - 充值记录
     */
    @GetMapping("/selRechargeRecordPitcher")
    public AjaxResult selRechargeRecordPitcher(Page page, RechargeRecordPitcherQuery query) {
        return success(page.setRecords(customerOrderService.selRechargeRecordPitcher(page, query)));
    }

    /**
     * 投放 - 充值记录 - 导出
     */
    @PostMapping("/selRechargeRecordPitcher/export")
    public void selRechargeRecordPitcherExport(HttpServletResponse response, RechargeRecordPitcherQuery query) {
        List<RechargeRecordPitcherVo> rechargeRecordPitcheList = customerOrderService.selRechargeRecordPitcher(null, query);

        // 将rechargeRecordPitcheList复制到exportList中
        List<RechargeRecordPitcherExportVo> exportList = new ArrayList<>();
        for (RechargeRecordPitcherVo vo : rechargeRecordPitcheList) {
            RechargeRecordPitcherExportVo exportVo = new RechargeRecordPitcherExportVo();
            exportVo.setAppProject(vo.getAppProject());
            exportVo.setCustomerId(vo.getCustomerId());
            exportVo.setCustomerName(vo.getCustomerName());
            exportVo.setPitcherName(vo.getPitcherName());
            exportVo.setVipName(vo.getVipName());
            exportVo.setOrderMoney(vo.getOrderMoney());
            exportVo.setOrderTime(vo.getOrderTime());
            exportVo.setOrderStatus(vo.getOrderStatus());
            exportVo.setPdOrderNo(vo.getPdOrderNo());
            exportVo.setPaymentType(vo.getPaymentType());
            exportList.add(exportVo);
        }
        ExcelUtil<RechargeRecordPitcherExportVo> util = new ExcelUtil<>(RechargeRecordPitcherExportVo.class);
        util.exportExcel(response, exportList, "充值记录列表");
    }

    /**
     * 投放 - 充值记录 - 统计
     * @param query
     * @return
     */
    @GetMapping("/selRechargeRecordPitcher-statistics")
    public AjaxResult selRechargeRecordPitcherStatistics(RechargeRecordPitcherQuery query) {
        return success(customerOrderService.selRechargeRecordPitcherStatistics(query));
    }

    /**
     * 运营 - 充值记录
     */
    @GetMapping("/operation/selRechargeRecordPitcher")
    public AjaxResult selRechargeRecordPitcherForOperation(Page page, RechargeRecordPitcherQuery query) {
        return success(page.setRecords(customerOrderService.selRechargeRecordPitcherForOperate(page, query)));
    }

    /**
     * 运营 - 充值记录 - 导出
     */
    @PostMapping("/operation/selRechargeRecordPitcher/export")
    public void selRechargeRecordPitcherExportForOperation(HttpServletResponse response, RechargeRecordPitcherQuery query) {
        List<RechargeRecordPitcherVo> rechargeRecordPitcheList = customerOrderService.selRechargeRecordPitcherForOperate(null, query);

        // 将rechargeRecordPitcheList复制到exportList中
        List<RechargeRecordPitcherExportVo> exportList = new ArrayList<>();
        for (RechargeRecordPitcherVo vo : rechargeRecordPitcheList) {
            RechargeRecordPitcherExportVo exportVo = new RechargeRecordPitcherExportVo();
            exportVo.setAppProject(vo.getAppProject());
            exportVo.setCustomerId(vo.getCustomerId());
            exportVo.setCustomerName(vo.getCustomerName());
            exportVo.setPitcherName(vo.getPitcherName());
            exportVo.setVipName(vo.getVipName());
            exportVo.setOrderMoney(vo.getOrderMoney());
            exportVo.setOrderTime(vo.getOrderTime());
            exportVo.setOrderStatus(vo.getOrderStatus());
            exportVo.setPdOrderNo(vo.getPdOrderNo());
            exportVo.setPaymentType(vo.getPaymentType());
            exportList.add(exportVo);
        }
        ExcelUtil<RechargeRecordPitcherExportVo> util = new ExcelUtil<>(RechargeRecordPitcherExportVo.class);
        util.exportExcel(response, exportList, "运营充值记录列表");
    }

    /**
     * 运营 - 充值记录 - 统计
     * @param query
     * @return
     */
    @GetMapping("/operation/selRechargeRecordPitcher-statistics")
    public AjaxResult selRechargeRecordPitcherStatisticsForOperation(RechargeRecordPitcherQuery query) {
        return success(customerOrderService.selRechargeRecordPitcherStatisticsForOperate(query));
    }



    /**
     * 根据规则生成几百粉记录(计算充值金额)
     * <p>
     * 200粉：24小时充值达到200
     * 推广只能产生一次、更换推广可以再产生一次、判断好友下的所有客户
     * 200没有新老粉
     * 500跟200规则一样
     * 5k粉/5w粉：一月只能产生一次，只统计当月充值满5k/5w
     * 5k/5w新老粉：改绑从新算新老粉
     *
     * @param extendIds   PD推广ID集
     * @param customerIds 好友ID绑定的客户ID集
     */
    @InnerAuth
    @GetMapping("/manufactureUp")
    private R<ManufactureUp> manufactureUp(@RequestParam("extendIds") List<Long> extendIds,
                                           @RequestParam("customerIds") List<Long> customerIds) {
        return R.ok(customerOrderService.manufactureUp(extendIds, customerIds, CollUtil.newArrayList()));
    }

    /**
     * 查询推广/vip订单的总金额(按天分组)
     *
     * @param ids       要查询的id集(为空查所有)
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param type      0:推广/1:VIP
     */
    @InnerAuth
    @GetMapping("/selOrderDayMoney")
    public R<List<CrmCustomerOrder>> selOrderDayMoney(@RequestParam("ids") List<Long> ids,
                                                      @RequestParam("beginTime") String beginTime,
                                                      @RequestParam(value = "endTime", required = false) String endTime,
                                                      @RequestParam("type") int type) {
        return R.ok(customerOrderService.selOrderDayMoney(ids, beginTime, endTime, type));
    }

    /**
     * 查询推广/vip订单的新增金额(按天分组)
     *
     * @param ids       要查询的id集(为空查所有)
     * @param beginTime 开始时间
     * @param endTime   结束时间
     * @param type      0:推广/1:VIP
     */
    @InnerAuth
    @GetMapping("/selOrderDayNewMoney")
    public R<List<CrmCustomerOrder>> selOrderDayNewMoney(@RequestParam("ids") List<Long> ids,
                                                         @RequestParam("beginTime") String beginTime,
                                                         @RequestParam(value = "endTime", required = false) String endTime,
                                                         @RequestParam("type") int type) {
        return R.ok(customerOrderService.selOrderDayNewMoney(ids, beginTime, endTime, type));
    }

    /**
     * 客户打赏记录
     */
    @GetMapping("/getRewardRecord")
    public AjaxResult getRewardRecord(Page<CustomerRewardVo> page, @Validated CustomerRewardQuery customerRewardQuery) {
        return success(customerOrderService.getRewardRecord(page, customerRewardQuery));
    }

    /**
     * 客户打赏记录 (客户列表层级)
     */
    @GetMapping("/getRewardRecordList")
    public AjaxResult getRewardRecordList(Page<CustomerRewardVo> page, CustomerRewardQuery customerRewardQuery) {
        return success(customerOrderService.getRewardRecordList(page, customerRewardQuery));
    }

}

