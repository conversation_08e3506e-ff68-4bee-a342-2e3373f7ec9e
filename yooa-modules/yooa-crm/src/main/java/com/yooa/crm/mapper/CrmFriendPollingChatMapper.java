package com.yooa.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.crm.api.domain.CrmCustomerOrder;
import com.yooa.crm.api.domain.CrmFriend;
import com.yooa.crm.api.domain.CrmFriendPollingChat;
import com.yooa.crm.api.domain.dto.CustomerStatisticsDto;
import com.yooa.crm.api.domain.dto.FriendEmployeeDto;
import com.yooa.crm.api.domain.dto.FriendRegisterChargeDto;
import com.yooa.crm.api.domain.query.CustomerFriendQuery;
import com.yooa.crm.api.domain.query.FriendQuery;
import com.yooa.crm.api.domain.query.OperateCustomerFriendQuery;
import com.yooa.crm.api.domain.query.PollingFriendQuery;
import com.yooa.crm.api.domain.vo.*;
import com.yooa.extend.api.domain.ExtendVermicelli;
import com.yooa.extend.api.domain.dto.CommonalityDto;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 好友 - 数据层
 */
public interface CrmFriendPollingChatMapper extends BaseMapper<CrmFriendPollingChat> {

}




