package com.yooa.crm.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.crm.api.domain.CrmCustomerReward;
import com.yooa.crm.api.domain.dto.CustomerRewardDto;
import com.yooa.crm.mapper.CrmAnchorRewardMapper;
import com.yooa.crm.service.CustomerRewardService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> xh
 * @Date: 2025/3/4 15:33
 * @Description:
 */
@Service("RewardService")
@RequiredArgsConstructor
public class CustomerRewardServiceImpl extends ServiceImpl<CrmAnchorRewardMapper, CrmCustomerReward>
        implements CustomerRewardService {

    private final CrmAnchorRewardMapper crmAnchorRewardMapper;
    @Override
    public CustomerRewardDto getAnchorRewardMoneyRecord(Long anchorId) {
        return crmAnchorRewardMapper.getAnchorRewardMoneyRecord(anchorId);
    }
}
