package com.yooa.crm.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.crm.api.domain.CrmFriendChannel;
import com.yooa.crm.service.FriendChannelService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 好友渠道管理 - 控制层
 */
@AllArgsConstructor
@RestController
@RequestMapping("/friend/channel")
public class FriendChannelController extends BaseController {

    private final FriendChannelService friendChannelService;

    @GetMapping("/list")
    public AjaxResult list() {
        return success(friendChannelService.list());
    }

    @GetMapping(value = {"/tree", "/tree/{type}"})
    public AjaxResult tree(@PathVariable(required = false) String type) {
        List<CrmFriendChannel> parentList = friendChannelService.list(
                Wrappers.<CrmFriendChannel>lambdaQuery()
                        .eq(CrmFriendChannel::getParentId, 0L)
                        .eq(StrUtil.isNotBlank(type), CrmFriendChannel::getType, type)
                        .orderByAsc(CrmFriendChannel::getSort)
        );
        parentList.forEach(parent -> {
            parent.setChildren(friendChannelService.list(
                    Wrappers.<CrmFriendChannel>lambdaQuery()
                            .eq(CrmFriendChannel::getParentId, parent.getChannelId())
                            .orderByAsc(CrmFriendChannel::getSort))
            );
        });
        return success(parentList);
    }

    @GetMapping("/children-list/{parentId}")
    public AjaxResult childrenList(@PathVariable Long parentId) {
        return success(friendChannelService.list(Wrappers.<CrmFriendChannel>lambdaQuery().eq(CrmFriendChannel::getParentId, parentId)));
    }

    @GetMapping("/{channelId}")
    public AjaxResult info(@PathVariable Long channelId) {
        return success(friendChannelService.getById(channelId));
    }

    @PostMapping
    public AjaxResult add(@RequestBody CrmFriendChannel channelType) {
        return success(friendChannelService.save(channelType));
    }

    @PutMapping
    public AjaxResult edit(@RequestBody CrmFriendChannel channelType) {
        return success(friendChannelService.updateById(channelType));
    }

    @DeleteMapping("/{channelId}")
    public AjaxResult remove(@PathVariable Long channelId) {
        List<CrmFriendChannel> childrenList = friendChannelService.list(
                Wrappers.<CrmFriendChannel>lambdaQuery()
                        .eq(CrmFriendChannel::getParentId, channelId)
        );
        if (CollUtil.isNotEmpty(childrenList)) {
            return warn("存在下级渠道，不允许删除");
        }
        return success(friendChannelService.removeById(channelId));
    }

}
