package com.yooa.crm.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.common.datascope.annotation.DataScope;
import com.yooa.crm.api.domain.CrmCustomerJoinServe;
import com.yooa.crm.api.domain.query.CustomerJoinServeQuery;
import com.yooa.crm.api.domain.vo.CustomerJoinServeVo;
import com.yooa.crm.mapper.CrmCustomerJoinServeMapper;
import com.yooa.crm.service.CustomerJoinServeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 客户客服交接 - 服务实现层
 */
@Service
public class CustomerJoinServeServiceImpl extends ServiceImpl<CrmCustomerJoinServeMapper, CrmCustomerJoinServe>
        implements CustomerJoinServeService {


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void correctionExpireTime() {
        // 先清空所有的流失时间
        baseMapper.clearExpireTime();
        // 校正所有数据的流失时间
        baseMapper.correctionExpireTime();
    }

    /**
     * 二交列表
     */
    @Override
    @DataScope(
            deptAlias = "combined",
            userAlias = "combined"
    )
    public List<CustomerJoinServeVo> getList(Page page, CustomerJoinServeQuery query) {
        return baseMapper.getList(page,query);
    }


    @Override
    @DataScope(
            deptAlias = "d2",
            userAlias = "u2"
    )
    public List pitcherList(Page page, CustomerJoinServeQuery query) {
        return baseMapper.getPitcherList(page,query);
    }
}




