package com.yooa.crm.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.crm.api.domain.CrmCustomerOrder;
import com.yooa.crm.api.domain.query.RechargeRecordPitcherQuery;
import com.yooa.crm.api.domain.vo.RechargeRecordPitcherExportVo;
import com.yooa.crm.api.domain.vo.RechargeRecordPitcherVo;
import com.yooa.crm.service.CustomerOrderService;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 订单 - 运营模块控制层
 */
@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/customer/order/operate")
public class CustomerOrderOperateController extends BaseController {

    private final CustomerOrderService customerOrderService;

    /**
     * 运营模块 - 充值记录查询
     */
    @GetMapping("/selRechargeRecordPitcher")
    public AjaxResult selRechargeRecordPitcher(Page page, RechargeRecordPitcherQuery query) {
        return success(page.setRecords(customerOrderService.selRechargeRecordPitcherForOperate(page, query)));
    }

    /**
     * 运营模块 - 充值记录导出
     */
    @PostMapping("/selRechargeRecordPitcher/export")
    public void selRechargeRecordPitcherExport(HttpServletResponse response, RechargeRecordPitcherQuery query) {
        List<RechargeRecordPitcherVo> rechargeRecordPitcheList = customerOrderService.selRechargeRecordPitcherForOperate(null, query);

        // 将rechargeRecordPitcheList复制到exportList中
        List<RechargeRecordPitcherExportVo> exportList = new ArrayList<>();
        for (RechargeRecordPitcherVo vo : rechargeRecordPitcheList) {
            RechargeRecordPitcherExportVo exportVo = new RechargeRecordPitcherExportVo();
            exportVo.setAppProject(vo.getAppProject());
            exportVo.setCustomerId(vo.getCustomerId());
            exportVo.setCustomerName(vo.getCustomerName());
            exportVo.setPitcherName(vo.getPitcherName());
            exportVo.setVipName(vo.getVipName());
            exportVo.setOrderMoney(vo.getOrderMoney());
            exportVo.setOrderTime(vo.getOrderTime());
            exportVo.setOrderStatus(vo.getOrderStatus());
            exportVo.setPdOrderNo(vo.getPdOrderNo());
            exportVo.setPaymentType(vo.getPaymentType());
            exportList.add(exportVo);
        }
        ExcelUtil<RechargeRecordPitcherExportVo> util = new ExcelUtil<>(RechargeRecordPitcherExportVo.class);
        util.exportExcel(response, exportList, "运营模块充值记录列表");
    }

    /**
     * 运营模块 - 充值记录统计
     * @param query
     * @return
     */
    @GetMapping("/selRechargeRecordPitcher-statistics")
    public AjaxResult selRechargeRecordPitcherStatistics(RechargeRecordPitcherQuery query) {
        return success(customerOrderService.selRechargeRecordPitcherStatisticsForOperate(query));
    }
}
