package com.yooa.crm.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.crm.api.domain.CrmCustomerHandover;
import com.yooa.crm.api.domain.query.CustomerHandoverQuery;
import com.yooa.crm.api.domain.query.CustomerSecondaryHandoverQuery;
import com.yooa.crm.api.domain.query.HandoverQuery;
import com.yooa.crm.api.domain.vo.CustomerHandoverVo;
import com.yooa.crm.api.domain.vo.CustomerSecondaryHandoverVo;
import com.yooa.crm.api.domain.vo.HandoverListVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> xh
 * @Date: 2025/6/9 14:25
 * @Description:
 */
public interface CrmCustomerHandoverMapper extends BaseMapper<CrmCustomerHandover>{
    /**
     * 获取交接记录列表
     */
    List<HandoverListVo> getHandoverList(Page page, @Param("query") HandoverQuery query, @Param("type")String type, @Param("userId") Long userId);

    /**
     * 新一交客户列表
     */
    List<CustomerHandoverVo> list(Page page,@Param("query") CustomerHandoverQuery query);

    /**
     * 新二交客户列表 (推广)
     */
    List<CustomerSecondaryHandoverVo> getExtendSecondaryHandover(Page page, @Param("query") CustomerSecondaryHandoverQuery query);

    /**
     * 新二交客户列表(vip)
     */
    List<CustomerSecondaryHandoverVo> getServeSecondaryHandover(Page page, @Param("query") CustomerSecondaryHandoverQuery query);
}
