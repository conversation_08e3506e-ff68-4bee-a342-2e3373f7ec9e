package com.yooa.crm.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.crm.api.domain.CrmCustomerRefund;
import com.yooa.crm.api.domain.vo.CustomerRefundRecordVo;
import com.yooa.crm.mapper.CrmAnchorRefundMapper;
import com.yooa.crm.mapper.CrmCustomerOrderMapper;
import com.yooa.crm.service.CustomerRefundService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 退款表(CrmRefund)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-04 15:51:26
 */
@Service("crmRefundService")
@RequiredArgsConstructor
public class CustomerRefundServiceImpl extends ServiceImpl<CrmAnchorRefundMapper, CrmCustomerRefund>
        implements CustomerRefundService {

    private final CrmAnchorRefundMapper crmAnchorRefundMapper;

    private final CrmCustomerOrderMapper crmCustomerOrderMapper;

    @Override
    public List<CustomerRefundRecordVo> getRefundRecord(Page<CustomerRefundRecordVo> page, Long anchorId) {
        // 1. 分页查询主播旗下退款的用户列表
        List<CustomerRefundRecordVo> refundCustomerList = crmAnchorRefundMapper.getRefundCustomerIds(page, anchorId);
        if (refundCustomerList.isEmpty()) {
            return refundCustomerList;
        }

        // 2. 提取所有需要查询的 customerId
        List<Long> customerIds = refundCustomerList.stream()
                .map(CustomerRefundRecordVo::getCustomerId)
                .collect(Collectors.toList());

        // 3. 批量查询充值退款数据，并转换为 Map<customerId, data>
        List<CustomerRefundRecordVo> chargeRefundDataList = crmCustomerOrderMapper.getChargeRefundData(customerIds);
        //GROUP_CONCAT函数查不到数据也会返回一条空数据 这里做下处理
        chargeRefundDataList.removeAll(Collections.singleton(null));
        Map<Long, CustomerRefundRecordVo> chargeRefundMap = chargeRefundDataList.stream()
                .collect(Collectors.toMap(CustomerRefundRecordVo::getCustomerId, Function.identity()));

        // 4. 遍历并填充数据
        refundCustomerList.forEach(refundCustomerVo -> {
            CustomerRefundRecordVo chargeRefundData = chargeRefundMap.get(refundCustomerVo.getCustomerId());
            if (chargeRefundData != null) {
                refundCustomerVo.setThirdOrderNo(chargeRefundData.getThirdOrderNo());
                refundCustomerVo.setPaymentType(chargeRefundData.getPaymentType());
            }
        });

        return refundCustomerList;
    }
}
