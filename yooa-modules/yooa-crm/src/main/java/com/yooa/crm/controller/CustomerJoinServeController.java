package com.yooa.crm.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.domain.R;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.security.annotation.InnerAuth;
import com.yooa.crm.api.domain.query.CustomerJoinAnchorQuery;
import com.yooa.crm.api.domain.query.CustomerJoinServeQuery;
import com.yooa.crm.api.domain.vo.CustomerJoinAnchorVo;
import com.yooa.crm.api.domain.vo.CustomerJoinServeVo;
import com.yooa.crm.service.CustomerJoinServeService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/customer/join/serve")
public class CustomerJoinServeController {

    private final CustomerJoinServeService customerJoinServeService;

    /**
     * 流失时间/过期时间校正 - 内部调用
     */
    @InnerAuth
    @GetMapping("/correction-expire-time")
    public R<?> correctionExpireTime() {
        customerJoinServeService.correctionExpireTime();
        return R.ok();
    }


    /**
     * 二交列表
     */
    @GetMapping("/list")
    public R<?> list(Page page, CustomerJoinServeQuery query){
        return R.ok(page.setRecords(customerJoinServeService.getList(page,query)));
    }


    /**
     * 二交列表 (导出)
     */
    @PostMapping("/listExport")
    public void listExport(HttpServletResponse response, Page page, CustomerJoinServeQuery query){
        page.setSize(-1);
        List<CustomerJoinServeVo> anchorList = customerJoinServeService.getList(page, query);
        ExcelUtil<CustomerJoinServeVo> util = new ExcelUtil<>(CustomerJoinServeVo.class);
        util.exportExcel(response, anchorList, "二交列表");
    }

    /**
     * 投放二交列表
     */
    @GetMapping("/pitcherList")
    public R<?> pitcherList(Page page, CustomerJoinServeQuery query){
        return R.ok(page.setRecords(customerJoinServeService.pitcherList(page,query)));
    }

    /**
     * 投放二交列表(导出)
     */
    @PostMapping("/pitcherListExport")
    public void pitcherListExport(HttpServletResponse response, Page page, CustomerJoinServeQuery query){
        page.setSize(-1);
        List<CustomerJoinServeVo> anchorList = customerJoinServeService.pitcherList(page, query);
        ExcelUtil<CustomerJoinServeVo> util = new ExcelUtil<>(CustomerJoinServeVo.class);
        util.exportExcel(response, anchorList, "二交列表");
    }
}
