package com.yooa.crm.listen.dts;

import com.alibaba.nacos.shaded.com.google.common.collect.ImmutableList;
import com.yooa.common.core.constant.KafkaTopicConstants;
import com.yooa.common.core.domain.SubscribeConvertDbData;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.common.core.utils.DateUtil;
import com.yooa.crm.api.domain.CrmCustomerJoinAnchor;
import com.yooa.crm.mapper.CrmCustomerFriendMapper;
import com.yooa.crm.service.CustomerFriendService;
import com.yooa.crm.service.CustomerJoinAnchorService;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.springframework.kafka.support.Acknowledgment;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.yooa.common.core.utils.ObjectConvertUtil.convertToLong;


@Service
@RequiredArgsConstructor
public class UsersJoinLiveListener extends AbstractBaseListener {

    private final CustomerJoinAnchorService customerJoinAnchorService;

    private final CustomerFriendService customerFriendService;

    private final CrmCustomerFriendMapper customerFriendMapper;

    @Override
    public String getTopic() {
        return KafkaTopicConstants.CMF_USERS_JOIN_LIVE;
    }

//    @Override
//    public String getBusTopic() {
//        return KafkaBusinessTopicConstants.BUS_CMF_USERS_JOIN_LIVE;
//    }

    @Override
    public void dltBusiness(ConsumerRecord<String, String> consumerRecord, Acknowledgment acknowledgment) {
        acknowledgment.acknowledge();

    }


    @Override
    protected List<String> getFilterList() {
        return ImmutableList.of("id", "uid", "liveid", "adminid", "operateid", "jointype", "status", "addtime", "uptime", "feedback_msg", "img", "message");
    }

    @Override
    protected void handleInsert(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        saveOrUpdateCustomerJoinAnchor(data, fieldMap);
    }

    @Override
    protected void handleUpdate(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        saveOrUpdateCustomerJoinAnchor(data, fieldMap);
    }

    @Override
    protected void handleDelete(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        Long id = convertToLong(getBeforeValueByFieldName("id", fieldMap));
        customerJoinAnchorService.removeById(id);
    }

    private void saveOrUpdateCustomerJoinAnchor(SubscribeConvertDbData data, Map<String, SubscribeConvertDbData.Field> fieldMap) {
        CrmCustomerJoinAnchor crmCustomerJoinAnchor = new CrmCustomerJoinAnchor();
        crmCustomerJoinAnchor.setId(convertToLong(getAfterValueByFieldName("id", fieldMap)));

        // 客户id
        Long customerId = convertToLong(getAfterValueByFieldName("uid", fieldMap));
        // 交接类型（1首次交接 2二次交接）
        crmCustomerJoinAnchor.setType(getAfterValueByFieldName("jointype", fieldMap));
        // 客户id
        crmCustomerJoinAnchor.setCustomerId(customerId);
        // PD推广id
        crmCustomerJoinAnchor.setExtendId(convertToLong(getAfterValueByFieldName("adminid", fieldMap)));
        // PD运营id
        crmCustomerJoinAnchor.setOperateId(convertToLong(getAfterValueByFieldName("operateid", fieldMap)));
        // 主播id
        crmCustomerJoinAnchor.setAnchorId(convertToLong(getAfterValueByFieldName("liveid", fieldMap)));
        // 交接时间
        crmCustomerJoinAnchor.setHandoverTime(DateUtil.parseLocalDateTime(getAfterValueByFieldName("addtime", fieldMap)));
        // 接收时间
        crmCustomerJoinAnchor.setReceiveTime(DateUtil.parseLocalDateTime(getAfterValueByFieldName("uptime", fieldMap)));
        // 接收时间后续会舍弃
        crmCustomerJoinAnchor.setJoinTime(DateUtil.parseLocalDateTime(getAfterValueByFieldName("uptime", fieldMap)));
        // 交接状态（0待交接 1已交接 2拒绝交接）
        crmCustomerJoinAnchor.setStatus(getAfterValueByFieldName("status", fieldMap));
        // 图片
        crmCustomerJoinAnchor.setImg(getAfterValueByFieldName("img", fieldMap));
        // 反馈消息
        crmCustomerJoinAnchor.setFeedbackMsg(getAfterValueByFieldName("feedback_msg", fieldMap));
        // 备注
        crmCustomerJoinAnchor.setRemark(getAfterValueByFieldName("message", fieldMap));

        // 根据客户id和推广id查询是否绑定好友 绑定了好友塞好友id值到一交表
        Long friendId = customerFriendMapper.getBindByCustomerIdAndPyExtendId(customerId, convertToLong(getAfterValueByFieldName("adminid", fieldMap)));
        if (Objects.nonNull(friendId) && friendId != 0) {
            crmCustomerJoinAnchor.setFriendId(friendId);
        }

        // 插入更新
        super.sendBusTopic = customerJoinAnchorService.saveOrUpdate(crmCustomerJoinAnchor);
        if (!sendBusTopic) {
            throw new ServiceException("UsersJoinLiveListener saveOrUpdate failed");
        }
    }
}
