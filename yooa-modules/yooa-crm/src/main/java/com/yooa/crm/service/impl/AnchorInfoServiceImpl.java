package com.yooa.crm.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.XWPFTemplate;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.domain.R;
import com.yooa.common.core.exception.ServiceException;
import com.yooa.common.datascope.annotation.DataScope;
import com.yooa.crm.api.domain.CrmAnchorInfo;
import com.yooa.crm.api.domain.query.AnchorInfoQuery;
import com.yooa.crm.api.domain.query.AnchorRosterQuery;
import com.yooa.crm.api.domain.vo.AnchorAccountMappingVo;
import com.yooa.crm.api.domain.vo.AnchorInfoVo;
import com.yooa.crm.api.domain.vo.AnchorRosterVo;
import com.yooa.crm.mapper.CrmAnchorInfoMapper;
import com.yooa.crm.service.AnchorInfoService;
import com.yooa.system.api.RemoteDictDataService;
import com.yooa.system.api.RemoteFileService;
import com.yooa.system.api.domain.SysDictData;
import com.yooa.system.api.domain.SysFile;
import com.yooa.system.api.domain.query.DictDataQuery;
import lombok.RequiredArgsConstructor;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.List;

/**
 * 主播信息 - 业务实现层
 */
@Service
@RequiredArgsConstructor
public class AnchorInfoServiceImpl extends ServiceImpl<CrmAnchorInfoMapper, CrmAnchorInfo>
        implements AnchorInfoService {

    private final RemoteFileService remoteFileService;
    private final RemoteDictDataService remoteDictDataService;


    @DataScope(userAlias = "u", deptAlias = "d")
    @Override
    public List<AnchorInfoVo> list(Page<AnchorInfoVo> page, AnchorInfoQuery query) {
        return baseMapper.selectAnchorInfoList(page, query);
    }

    @Override
    public AnchorInfoVo getAnchorInfoById(Long anchorId) {
        return baseMapper.selectAnchorInfoById(anchorId);
    }

    @Override
    public List<AnchorAccountMappingVo> accountListByAnchorId(Long anchorId) {
        return baseMapper.selectAccountListByAnchorId(anchorId);
    }

    @Override
    @DataScope(userAlias = "u", deptAlias = "d")
    public List<AnchorRosterVo> getAnchorRoster(Page page, AnchorRosterQuery query) {
//        return baseMapper.getAnchorRoster(page, query)
//                .stream()
//                .peek(anchorRosterVo -> {
//                    String phone = anchorRosterVo.getPhone();
//                    if (phone != null) {
//                        String hidePhone = StrUtil.hide(phone, 3, 7);
//                        anchorRosterVo.setPhone(hidePhone);
//                    }
//                    String idCardNumber = anchorRosterVo.getIdCardNumber();
//                    if (idCardNumber != null) {
//                        String hideIdCard = StrUtil.hide(idCardNumber, 6, idCardNumber.length() - 4);
//                        anchorRosterVo.setIdCardNumber(hideIdCard);
//                    }
//                }).toList();
        return baseMapper.getAnchorRoster(page, query);
    }

    @Override
    public String getRegistrationFormUrl(Long anchorId) {
        AnchorInfoVo anchorInfo = baseMapper.selectAnchorInfoById(anchorId);
        if (ObjUtil.isNull(anchorInfo)) {
            throw new ServiceException("面试信息不存在");
        }

        if (StrUtil.isNotBlank(anchorInfo.getRegistrationFormUrl())) {
            return anchorInfo.getRegistrationFormUrl();
        }

        HashMap<String, Object> data = new HashMap<>();
        data.put("InterviewDate", anchorInfo.getInterviewDate());
        //
        data.put("InviterName", anchorInfo.getInviterName());
        // 应聘岗位
        data.put("Position", anchorInfo.getPosition());
        // 期望薪资
        data.put("SalaryExpectation", anchorInfo.getExpectationSalary());
        // 是否住宿
        data.put("Lodging", getDictLabel("common_default_status", anchorInfo.getIsLodging()));
        // 姓名
        data.put("AnchorName", anchorInfo.getAnchorName());
        // 年龄
        data.put("Age", anchorInfo.getAge());
        // 性别
        data.put("Sex", getDictLabel("common_sex_type", anchorInfo.getSex()));
        // 籍贯
        data.put("NativePlace", anchorInfo.getNativePlace());
        // 民族
        data.put("Nation", anchorInfo.getNation());
        // 婚姻状况
        data.put("Marital", getDictLabel("common_marital_status", anchorInfo.getMarital()));
        // 出生日期
        data.put("BirthdayDate", anchorInfo.getBirthdayDate());
        // 农历生日
        data.put("ChineseBirthdayDate", anchorInfo.getChineseBirthdayDate());
        // 身份证号
        data.put("IdCardNumber", anchorInfo.getIdCardNumber());
        // 身高
        data.put("Height", anchorInfo.getHeight());
        // 体重
        data.put("Weight", anchorInfo.getWeight());
        // 学历
        data.put("Education", getDictLabel("common_education_type", anchorInfo.getEducationType()));
        // 专业
        data.put("Major", anchorInfo.getMajor());
        // 健康状况
        data.put("HealthStatus", getDictLabel("common_health_status", anchorInfo.getHealthStatus()));
        // 有无遗传病
        data.put("IsInfectiousDiseases", getDictLabel("common_default_status", anchorInfo.getIsInfectiousDiseases()));
        // 联系电话
        data.put("Phone", anchorInfo.getPhone());
        // 邮箱
        data.put("Email", anchorInfo.getEmail());
        // 紧急联系人
        data.put("EmergencyContactName", anchorInfo.getEmergencyContactName());
        // 紧急联系电话
        data.put("EmergencyContactPhone", anchorInfo.getEmergencyContactPhone());
        // 户口所在地
        data.put("RegisteredResidence", anchorInfo.getRegisteredResidence());
        // 现住地址
        data.put("Address", anchorInfo.getAddress());
        // 目前是否与原单位存在竞业禁止协议
        data.put("IsNca", getDictLabel("common_default_status", anchorInfo.getIsNca()));
        data.put("RelativesName", anchorInfo.getRelativesName());
        data.put("RelativesRelationship", getDictLabel("common_relationship_type", anchorInfo.getRelativesRelationship()));

        // 学历扩展信息
        if (StrUtil.isNotBlank(anchorInfo.getEducationExtra())) {
            JSONArray educationExtra = JSON.parseArray(anchorInfo.getEducationExtra());
            if (ObjUtil.isNotNull(educationExtra)) {
                for (int i = 0; i < educationExtra.size(); i++) {
                    data.put("EnrollmentDate" + (i + 1), educationExtra.getJSONObject(i).getString("enrollmentDate"));
                    data.put("GraduationDate" + (i + 1), educationExtra.getJSONObject(i).getString("graduationDate"));
                    data.put("School" + (i + 1), educationExtra.getJSONObject(i).getString("school"));
                    data.put("Major" + (i + 1), educationExtra.getJSONObject(i).getString("major"));
                    data.put("Education" + (i + 1), educationExtra.getJSONObject(i).getString("education"));
                    data.put("Degree" + (i + 1), educationExtra.getJSONObject(i).getString("degree"));
                }
            }
        }

        // 工作扩展信息
        if (StrUtil.isNotBlank(anchorInfo.getWorkExtra())) {
            JSONArray workExtra = JSON.parseArray(anchorInfo.getWorkExtra());
            if (ObjUtil.isNotNull(workExtra)) {
                for (int i = 0; i < workExtra.size(); i++) {
                    data.put("EmploymentDate" + (i + 1), workExtra.getJSONObject(i).getString("employmentDate"));
                    data.put("ResignationDate" + (i + 1), workExtra.getJSONObject(i).getString("resignationDate"));
                    data.put("Company" + (i + 1), workExtra.getJSONObject(i).getString("company"));
                    data.put("Position" + (i + 1), workExtra.getJSONObject(i).getString("position"));
                    data.put("Salary" + (i + 1), workExtra.getJSONObject(i).getString("salary"));
                    data.put("Certifier" + (i + 1), workExtra.getJSONObject(i).getString("certifier"));
                    data.put("CertifierPhone" + (i + 1), workExtra.getJSONObject(i).getString("certifierPhone"));
                }
            }
        }

        // 家庭扩展信息
        if (StrUtil.isNotBlank(anchorInfo.getFamilyExtra())) {
            JSONArray familyExtra = JSON.parseArray(anchorInfo.getFamilyExtra());
            if (ObjUtil.isNotNull(familyExtra)) {
                for (int i = 0; i < familyExtra.size(); i++) {
                    data.put("MemberName" + (i + 1), familyExtra.getJSONObject(i).getString("memberName"));
                    data.put("Relationship" + (i + 1), familyExtra.getJSONObject(i).getString("relationship"));
                    data.put("ContactPhone" + (i + 1), familyExtra.getJSONObject(i).getString("contactPhone"));
                    data.put("Work" + (i + 1), familyExtra.getJSONObject(i).getString("work"));
                    data.put("remark" + (i + 1), familyExtra.getJSONObject(i).getString("remark"));
                }
            }
        }

        // 是否有过直播或试播经验
        data.put("IsLive", getDictLabel("common_default_status", anchorInfo.getIsLive()));
        // 直播平台及时长
        data.put("LivePlatform", anchorInfo.getLivePlatform());
        // 艺术特长
        data.put("Talent", anchorInfo.getTalent());

        try {
            // 获取模板文件
            ClassPathResource resource = new ClassPathResource("file/registration_form.docx");
            // 执行动态渲染数据
            XWPFTemplate template = XWPFTemplate.compile(resource.getInputStream());
            template.render(data);

            // 创建输出流
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            // 渲染后的数据生成至输出流中
            template.writeAndClose(bos);

            // 创建MultipartFile 并将数据放置其中
            FileItemFactory factory = new DiskFileItemFactory();
            FileItem item = factory.createItem(anchorInfo.getAnchorName(), "text/plain", false, anchorInfo.getAnchorName() + "面试登记表.docx");
            item.getOutputStream().write(bos.toByteArray());

            // 执行上传
            R<SysFile> uploadR = remoteFileService.upload(new CommonsMultipartFile(item));

            if (uploadR.getCode() == R.SUCCESS) {
                String registrationFormUrl = uploadR.getData().getUrl();
                anchorInfo.setRegistrationFormUrl(registrationFormUrl);
                baseMapper.updateById(anchorInfo);
                return registrationFormUrl;
            }

        }
        catch (Exception e) {
            log.error("获取面试登记表失败", e);
        }

        return null;
    }

    private String getDictLabel(String dictType, String dictValue) {
        DictDataQuery query = new DictDataQuery();
        query.setDictType(dictType);
        R<List<SysDictData>> r = remoteDictDataService.getList(query, SecurityConstants.INNER);
        if (R.SUCCESS == r.getCode() && ObjUtil.isNotNull(r.getData())) {
            List<SysDictData> DictDataList = r.getData();
            for (SysDictData dictData : DictDataList) {
                if (dictData.getDictValue().equals(dictValue)) {
                    return dictData.getDictLabel();
                }
            }
        }
        return null;
    }


}




