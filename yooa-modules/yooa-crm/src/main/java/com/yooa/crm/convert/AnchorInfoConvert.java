package com.yooa.crm.convert;

import com.yooa.crm.api.domain.CrmAnchorInfo;
import com.yooa.crm.api.domain.dto.AnchorInfoDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR> xh
 * @Date: 2025/3/31 11:34
 * @Description:
 */
@Mapper
public interface AnchorInfoConvert {

    AnchorInfoConvert INSTANCE =  Mappers.getMapper(AnchorInfoConvert.class);

    CrmAnchorInfo anchorInfoDtoToCrmAnchorInfo(AnchorInfoDto anchorInfoDto);
}
