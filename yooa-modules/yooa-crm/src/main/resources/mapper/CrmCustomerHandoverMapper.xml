<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.crm.mapper.CrmCustomerHandoverMapper">
    <select id="getHandoverList" resultType="com.yooa.crm.api.domain.vo.HandoverListVo">
        SELECT
            ch.handover_id handoverId,
            ch.customer_id customerId,
            ch.py_anchor_id anchorId,
            ch.handover_status handoverStatus,
            ch.handover_num handoverCount,
            ch.handover_type handoverType,
            ch.reject_info refuseMsg,
            ch.receive_time receiveTime,
            ch.handover_time handoverTime,
            <choose>
                <when test="type == 1">
                    CASE WHEN ch.handover_type = 1 THEN u2.nick_name
                    WHEN ch.handover_type = 2 THEN u3.nick_name
                    END receiveName,
                </when>
                <otherwise>
                    u2.nick_name receiveName,
                </otherwise>
            </choose>
            a.anchor_name anchorName,
            ai.flower_name anchorNickName,
            f.friend_id friendId,
            d.dept_name deptName,
            d.ancestors_names ancestorsNames,
            c.customer_name customerName,
            f.friend_name customerNickName,
            ch.handover_img handoverImg,
            <if test="type != 1">
                u2.user_name extendName,
                u2.nick_name extendNickName,
            </if>
            ch.remark,
            ch.handover_img handoverImg,
            ch.handover_info handoverInfo
        FROM
            crm_customer_handover ch
                <choose>
                    <when test="type == 1">
                        LEFT JOIN yooa_system.sys_user u on ch.extend_id = u.user_id
                        LEFT JOIN yooa_system.sys_dept d on d.dept_id = u.dept_id
                        LEFT JOIN yooa_system.sys_user u2 on ch.operate_id = u2.user_id
                        LEFT JOIN yooa_system.sys_user u3 on ch.serve_id = u3.user_id
                    </when>
                    <when test="type == 2">
                        INNER JOIN yooa_system.sys_user u on ch.operate_id = u.user_id
                        LEFT JOIN yooa_system.sys_dept d on d.dept_id = u.dept_id
                        LEFT JOIN yooa_system.sys_user u2 on ch.extend_id = u2.user_id
                    </when>
                    <when test="type == 3">
                        INNER JOIN yooa_system.sys_user u on ch.serve_id = u.user_id
                        LEFT JOIN yooa_system.sys_dept d on d.dept_id = u.dept_id
                        LEFT JOIN yooa_system.sys_user u2 on ch.extend_id = u2.user_id
                    </when>
                    <otherwise></otherwise>
                </choose>
                LEFT JOIN crm_anchor a on ch.py_anchor_id = a.anchor_id
                LEFT JOIN crm_anchor_account_mapping aam on aam.account_id = a.anchor_id
                LEFT JOIN crm_anchor_info ai on ai.anchor_id = aam.anchor_id
                LEFT JOIN crm_customer c on c.customer_id = ch.customer_id
                LEFT JOIN crm_friend f on friend_id = ch.customer_friend_id
                WHERE ch.lose_time IS NULL
                  AND u.user_id = #{userId}
                <if test="query.handoverType != null">
                    AND ch.handover_type = #{query.handoverType}
                </if>
                <if test="query.handoverNum != null">
                    AND ch.handover_num = #{query.handoverNum}
                </if>
                <if test="query.handoverStatus != null">
                    AND ch.handover_status = #{query.handoverStatus}
                </if>
                <if test="query.beginHandoverTime != null and query.afterHandoverTime != null">
                    AND ch.handover_time >= #{query.beginHandoverTime} AND ch.handover_time &lt;= #{query.afterHandoverTime}
                </if>
                <if test="query.beginReceiveTime != null and query.afterReceiveTime != null">
                    AND ch.receive_time >= #{query.beginReceiveTime} AND ch.receive_time &lt;= #{query.afterReceiveTime}
                </if>
                <if test="query.queryId != null and query.queryId != ''">
                    AND (ch.customer_id = #{query.queryId}
                    OR ch.py_anchor_id = #{query.queryId}
                    OR u.nick_name LIKE CONCAT('%',#{query.queryId},'%')
                    OR c.customer_name LIKE CONCAT('%',#{query.queryId},'%')
                    OR a.anchor_name LIKE CONCAT('%',#{query.queryId},'%'))
                </if>
                <if test="query.deptId != null">
                    AND ((FIND_IN_SET(#{query.deptId},d.ancestors))
                    OR d.dept_id = #{query.deptId})
                </if>
        ORDER BY ch.create_time
    </select>

    <select id="list" resultType="com.yooa.crm.api.domain.vo.CustomerHandoverVo">
        SELECT
            ch.customer_id AS customer_id,
            ch.receive_time,
            u.nick_name AS extendUserName,
            d.dept_id AS extendDeptId,
            d.ancestors_names AS extendAncestors,
            d.ancestors AS extendAncestorsId,
            d.dept_name AS extendDeptName,
            a.anchor_name anchorName,
            f.friend_id AS friendId,
            f.friend_name AS friendName,
            f.LANGUAGE,
            f.fans_type AS fansType,
            f.extend_id AS receiveId,
            ( SELECT channel_name FROM crm_friend_channel WHERE channel_id = main_channel_id ) AS channelName,
            cf.create_by,
            u2.nick_name AS channelNickName,
            CASE
                WHEN cf.id IS NULL THEN
                    '0' ELSE '1'
                END bindType,
            u3.nick_name operateUserName,
            d3.dept_id operateDeptId,
            d3.ancestors_names operateAncestors,
            d3.ancestors operateAncestorsId,
            d3.dept_name operateDeptName
        FROM
            crm_customer_handover ch
                LEFT JOIN crm_anchor a ON a.anchor_id = ch.py_anchor_id
                LEFT JOIN yooa_system.sys_user u ON ch.extend_id = u.user_id
                LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                LEFT JOIN crm_customer c ON ch.customer_id = c.customer_id
                LEFT JOIN crm_customer_friend cf ON ch.customer_friend_id = cf.id
                LEFT JOIN crm_friend f ON cf.friend_id = f.friend_id
                LEFT JOIN yooa_system.sys_user u2 ON f.pitcher_id = u2.user_id
                LEFT JOIN yooa_system.sys_user u3 ON ch.operate_id = u3.user_id
                LEFT JOIN yooa_system.sys_dept d3 ON u3.dept_id = d3.dept_id
        WHERE ch.handover_type = 1 AND ch.handover_status = 1
        ${query.params.dataScope}
        <if test="query.receiveBeginTime != null and query.receiveEndTime != null">
            AND ch.receive_time BETWEEN #{query.receiveBeginTime} AND #{query.receiveEndTime}
        </if>
        <if test="query.language != null">
            AND f.language = #{query.language}
        </if>
        <if test="query.fansType != null">
            AND f.fans_type = #{query.fansType}
        </if>
        <if test="query.deptId != null">
            AND (
            (FIND_IN_SET(#{query.deptId},d.ancestors))
            OR
            d.dept_id = #{query.deptId}
            )
        </if>
        <if test="query.customerNameOrIdQuery != null and query.customerNameOrIdQuery != ''">
            AND (f.friend_name LIKE CONCAT('%', #{query.customerNameOrIdQuery}, '%')
            OR ch.customer_id = #{query.customerNameOrIdQuery}
            OR u.nick_name LIKE CONCAT ('%',#{query.customerNameOrIdQuery},'%')
            OR u3.nick_name LIKE CONCAT ('%',#{query.customerNameOrIdQuery},'%')
            OR a.anchor_name LIKE CONCAT ('%',#{query.customerNameOrIdQuery},'%')
            )
        </if>
        <if test="query.bindType != null and query.bindType != ''">
            AND
            CASE
            WHEN cf.id IS NULL THEN
            '0' ELSE '1'
            END = #{query.bindType}
        </if>
        GROUP BY ch.handover_id
        ORDER BY ch.receive_time DESC
    </select>
    <select id="getExtendSecondaryHandover" resultType="com.yooa.crm.api.domain.vo.CustomerSecondaryHandoverVo">
        SELECT
            ch.handover_id,
            ch.customer_id,
            ch.receive_time,
            d.dept_name extendDeptName,
            u.nick_name AS extendUserName,
            u.dept_id AS extendDeptId,
            d.ancestors_names AS extendAncestors,
            d.ancestors AS extendAncestorsId,
            u3.nick_name AS serveUserName,
            d3.ancestors_names AS serveAncestors,
            d3.ancestors AS serveAncestorsId,
            u3.dept_id AS serveDeptId,
            ( SELECT channel_name FROM crm_friend_channel WHERE channel_id = main_channel_id ) AS channelName,
            u2.nick_name AS channelNickName,
            f.LANGUAGE,
            f.fans_type AS fansType,
            f.friend_id AS friendId,
            f.extend_id AS extendId,
            f.friend_name AS friendName,
            CASE
                WHEN cf.id IS NULL THEN
                    '0' ELSE '1'
                END bindType
        FROM
            crm_customer_handover ch
                LEFT JOIN crm_customer_friend cf ON cf.id = ch.customer_friend_id
                LEFT JOIN crm_friend f ON cf.friend_id = f.friend_id
                LEFT JOIN yooa_system.sys_user u ON ch.extend_id = u.user_id
                LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
                LEFT JOIN yooa_system.sys_user u2 ON f.pitcher_id = u2.user_id
                LEFT JOIN yooa_system.sys_user u3 ON ch.serve_id = u3.user_id
                LEFT JOIN yooa_system.sys_dept d3 ON u3.dept_id = d3.dept_id
        WHERE
        ch.handover_status = 1
        AND ch.handover_type = 2
        AND ch.lose_time IS NULL
        ${query.params.dataScope}
        <if test="query.receiveBeginTime != null and query.receiveEndTime != null">
            AND ch.receive_time BETWEEN #{query.receiveBeginTime} AND #{query.receiveEndTime}
        </if>
        <if test="query.language != null">
            AND f.language = #{query.language}
        </if>
        <if test="query.fansType != null">
            AND f.fans_type = #{query.fansType}
        </if>
        <if test="query.deptId != null ">
            AND (FIND_IN_SET(#{query.deptId},d.ancestors) OR d.dept_id = #{query.deptId})
        </if>
        <if test="query.customerNameOrIdQuery != null and query.customerNameOrIdQuery !=''">
            AND f.friend_name like CONCAT('%',#{query.customerNameOrIdQuery},'%')
            OR ch.customer_id = #{query.customerNameOrIdQuery}
            OR u.nick_name LIKE CONCAT ('%',#{query.customerNameOrIdQuery},'%')
            OR u3.nick_name LIKE CONCAT ('%',#{query.customerNameOrIdQuery},'%')
        </if>
        <if test="query.bindType != null and query.bindType != ''">
            AND
            CASE
            WHEN cf.id IS NULL THEN
            '0' ELSE '1'
            END = #{query.bindType}
        </if>
        ORDER BY ch.receive_time DESC
    </select>
    <select id="getServeSecondaryHandover" resultType="com.yooa.crm.api.domain.vo.CustomerSecondaryHandoverVo">
        SELECT
        ch.handover_id,
        ch.customer_id,
        ch.receive_time,
        d3.dept_name extendDeptName,
        u3.nick_name AS extendUserName,
        u3.dept_id AS extendDeptId,
        d3.ancestors_names AS extendAncestors,
        d3.ancestors AS extendAncestorsId,
        u.nick_name AS serveUserName,
        d.ancestors_names AS serveAncestors,
        d.ancestors AS serveAncestorsId,
        u.dept_id AS serveDeptId,
        ( SELECT channel_name FROM crm_friend_channel WHERE channel_id = main_channel_id ) AS channelName,
        u2.nick_name AS channelNickName,
        f.LANGUAGE,
        f.fans_type AS fansType,
        f.friend_id AS friendId,
        f.extend_id AS extendId,
        f.friend_name AS friendName,
        CASE
        WHEN cf.id IS NULL THEN
        '0' ELSE '1'
        END bindType
        FROM
        crm_customer_handover ch
        LEFT JOIN crm_customer_friend cf ON cf.id = ch.customer_friend_id
        LEFT JOIN crm_friend f ON cf.friend_id = f.friend_id
        LEFT JOIN yooa_system.sys_user u3 ON ch.extend_id = u3.user_id
        LEFT JOIN yooa_system.sys_dept d3 ON u3.dept_id = d3.dept_id
        LEFT JOIN yooa_system.sys_user u2 ON f.pitcher_id = u2.user_id
        LEFT JOIN yooa_system.sys_user u ON ch.serve_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        WHERE
        ch.handover_status = 1
        AND ch.handover_type = 2
        AND ch.lose_time IS NULL
        ${query.params.dataScope}
        <if test="query.receiveBeginTime != null and query.receiveEndTime != null">
            AND ch.receive_time BETWEEN #{query.receiveBeginTime} AND #{query.receiveEndTime}
        </if>
        <if test="query.language != null">
            AND f.language = #{query.language}
        </if>
        <if test="query.fansType != null">
            AND f.fans_type = #{query.fansType}
        </if>
        <if test="query.deptId != null ">
            AND (FIND_IN_SET(#{query.deptId},d.ancestors) OR d.dept_id = #{query.deptId})
        </if>
        <if test="query.customerNameOrIdQuery != null and query.customerNameOrIdQuery !=''">
            AND f.friend_name like CONCAT('%',#{query.customerNameOrIdQuery},'%')
            OR ch.customer_id = #{query.customerNameOrIdQuery}
            OR u.nick_name LIKE CONCAT ('%',#{query.customerNameOrIdQuery},'%')
            OR u3.nick_name LIKE CONCAT ('%',#{query.customerNameOrIdQuery},'%')
        </if>
        <if test="query.bindType != null and query.bindType != ''">
            AND
            CASE
            WHEN cf.id IS NULL THEN
            '0' ELSE '1'
            END = #{query.bindType}
        </if>
        ORDER BY ch.receive_time DESC
    </select>


</mapper>