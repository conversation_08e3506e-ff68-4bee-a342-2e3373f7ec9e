<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.crm.mapper.CrmCustomerFriendMapper">

    <update id="updateAllLose">
        UPDATE crm_customer_friend
        SET status = 1, lose_time = NOW()
        WHERE
        friend_id IN
        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <select id="getBindByCustomerIdAndPyExtendId" resultType="java.lang.Long">
        SELECT
        cf.friend_id
        FROM
        crm_customer_friend cf
        LEFT JOIN crm_customer_join_anchor cja ON cja.customer_id = cf.customer_id
        AND cja.extend_id = cf.py_extend_id
        AND cf.begin_time &lt; cja.receive_time AND ( cf.end_time IS NULL OR cf.end_time > cja.receive_time
        )
        WHERE
        cja.customer_id = #{customerId}
        AND cf.`status` = 0
        AND cf.py_extend_id = #{pyExtendId}
        LIMIT 1
    </select>

</mapper>
