<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.crm.mapper.CrmCustomerJoinAnchorMapper">


    <update id="updateCustomerJoinAnchorAmt">
        update crm_customer_join_anchor
        set join_amt = join_amt + #{totalAmount}
            where status = 1
              AND  anchor_id = #{anchorId}
              AND customer_id = #{customerId}
              AND operate_id = #{operateId}
    </update>

    <select id="getCustomerJoinAnchorByCustomerId" resultType="com.yooa.crm.api.domain.CrmCustomerJoinAnchor">
        SELECT * FROM crm_customer_join_anchor
        <where>
            customer_id = #{customerId}
            AND status = 1
            AND DATEDIFF(#{addTime} ,receive_time) &lt;= 90
            AND first_charge_date IS NULL
        </where>
    </select>

<!--    <select id="getList" resultMap="mainResultMap">-->
<!--        SELECT-->
<!--        cja.customer_id AS customer_id,-->
<!--        c.customer_name,-->
<!--        cja.receive_time,-->
<!--        CASE-->
<!--        WHEN  c.create_time = fmt.min_create_time  THEN-->
<!--        '0'-->
<!--        WHEN cf.id IS NULL THEN-->
<!--        '0'-->
<!--        ELSE '1'-->
<!--        END AS accountType,-->
<!--        u.nick_name AS extendUserName,-->
<!--        d.dept_id AS extendDeptId,-->
<!--        d.ancestors_names AS extendAncestors,-->
<!--        d.ancestors AS extendAncestorsId,-->
<!--        d.dept_name AS extendDeptName,-->
<!--        cja.anchor_name anchorName,-->
<!--        f.friend_id AS friendId,-->
<!--        f.NAME AS friendName,-->
<!--        f.LANGUAGE,-->
<!--        f.fans_type AS fansType,-->
<!--        f.receive_id AS receiveId,-->
<!--        ( SELECT channel_name FROM crm_friend_channel WHERE channel_id = main_channel_id ) AS channelName,-->
<!--        cf.create_by,-->
<!--        u2.nick_name AS channelNickName,-->
<!--        CASE-->
<!--        WHEN cf.id IS NULL THEN-->
<!--        '0' ELSE '1'-->
<!--        END bindType-->
<!--        FROM-->
<!--        (-->
<!--        SELECT-->
<!--        a.anchor_name,-->
<!--        u2.user_id,-->
<!--        u2.user_name,-->
<!--        u2.nick_name,-->
<!--        cja.extend_id,-->
<!--        cja.customer_id,-->
<!--        cja.receive_time,-->
<!--        cja.id-->
<!--        FROM-->
<!--        crm_customer_join_anchor cja-->
<!--        LEFT JOIN crm_anchor a ON a.anchor_id = cja.anchor_id-->
<!--        LEFT JOIN yooa_system.sys_user_pd up2 ON up2.pd_user_id = cja.operate_id-->
<!--        LEFT JOIN yooa_system.sys_user u2 ON up2.user_id = u2.user_id-->
<!--        LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.extend_id-->
<!--        LEFT JOIN yooa_system.sys_user u ON u.user_id = up.user_id-->
<!--        LEFT JOIN yooa_system.sys_dept d ON d.dept_id = u.dept_id-->
<!--        WHERE cja.status = 1-->
<!--        GROUP BY cja.id-->
<!--        ${query.params.dataScope}-->
<!--        ) cja-->
<!--        LEFT JOIN crm_customer AS c ON cja.customer_id = c.customer_id-->
<!--        LEFT JOIN crm_customer_friend AS cf ON cf.customer_id = c.customer_id-->
<!--        LEFT JOIN (-->
<!--        SELECT-->
<!--        f.friend_id,-->
<!--        f.NAME,-->
<!--        f.receive_user_id receive_id,-->
<!--        f.create_time,-->
<!--        f.record_time,-->
<!--        f.main_channel_id,-->
<!--        f.sub_channel_id,-->
<!--        f.pitcher_id,-->
<!--        up.py_extend_ids,-->
<!--        f.LANGUAGE,-->
<!--        f.fans_type-->
<!--        FROM-->
<!--        crm_friend_receive_record AS f-->
<!--        LEFT JOIN ( SELECT GROUP_CONCAT( pd_user_id ) AS py_extend_ids, user_id FROM yooa_system.sys_user_pd GROUP BY user_id ) up ON up.user_id = f.receive_user_id-->
<!--        ) AS f ON f.friend_id = cf.friend_id-->
<!--        AND FIND_IN_SET( cja.extend_id, f.py_extend_ids )-->
<!--        LEFT JOIN yooa_system.sys_user u ON cf.create_by = u.user_id-->
<!--        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id-->
<!--        LEFT JOIN (-->
<!--        SELECT-->
<!--        cf.friend_id,-->
<!--        MIN( cc.create_time ) AS min_create_time-->
<!--        FROM-->
<!--        crm_customer_friend cf-->
<!--        INNER JOIN crm_customer cc ON cf.customer_id = cc.customer_id-->
<!--        GROUP BY-->
<!--        cf.friend_id-->
<!--        ) fmt ON f.friend_id = fmt.friend_id-->
<!--        LEFT JOIN yooa_system.sys_user u2 ON f.pitcher_id = u2.user_id-->
<!--        <if test="query.receiveBeginTime != null and query.receiveEndTime != null">-->
<!--            AND cja.receive_time BETWEEN #{query.receiveBeginTime} AND #{query.receiveEndTime}-->
<!--        </if>-->
<!--        <if test="query.language != null">-->
<!--            AND f.language = #{query.language}-->
<!--        </if>-->
<!--        <if test="query.fansType != null">-->
<!--            AND f.fans_type = #{query.fansType}-->
<!--        </if>-->
<!--        <if test="query.deptId != null">-->
<!--            AND (-->
<!--            (FIND_IN_SET(#{query.deptId},d.ancestors))-->
<!--            OR-->
<!--            d.dept_id = #{query.deptId}-->
<!--            )-->
<!--            OR EXISTS (-->
<!--            SELECT 1-->
<!--            FROM crm_customer_join_anchor cja_op-->
<!--            LEFT JOIN yooa_system.sys_user_pd up_op ON cja_op.operate_id = up_op.pd_user_id-->
<!--            LEFT JOIN yooa_system.sys_user u_op ON up_op.user_id = u_op.user_id-->
<!--            LEFT JOIN yooa_system.sys_dept op_dept ON u_op.dept_id = op_dept.dept_id-->
<!--            WHERE cja_op.customer_id = cja.customer_id-->
<!--            AND (op_dept.dept_id = #{query.deptId} OR (FIND_IN_SET(#{query.deptId},op_dept.ancestors))-->
<!--            )-->
<!--          )-->
<!--        </if>-->
<!--        <if test="query.customerNameOrIdQuery != null and query.customerNameOrIdQuery != ''">-->
<!--            AND (f.name LIKE CONCAT('%', #{query.customerNameOrIdQuery}, '%')-->
<!--            OR c.customer_id = #{query.customerNameOrIdQuery})-->
<!--        </if>-->
<!--        GROUP BY-->
<!--        cja.id-->
<!--        <if test="query.accountType != null">-->
<!--        HAVING accountType = #{query.accountType}-->
<!--        </if>-->
<!--        ORDER BY cja.receive_time DESC-->
<!--    </select>-->
    <select id="getIds" resultType="java.lang.Long">
        SELECT
            cja.id
        FROM
            crm_customer_join_anchor cja
                LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.extend_id

                LEFT JOIN crm_customer_friend AS cf ON cf.customer_id = cja.customer_id AND cf.py_extend_id = cja.extend_id
                                                           AND cf.begin_time &lt; cja.receive_time AND ( cf.end_time IS NULL OR cf.end_time > cja.receive_time)
        <if test="query.moduleType == null">
            LEFT JOIN yooa_system.sys_user u ON u.user_id = up.user_id
        </if>
        <if test="query.moduleType != null and query.moduleType == 'pitcher'">
            LEFT JOIN
            crm_friend f
            ON cf.friend_id = f.friend_id
            LEFT JOIN yooa_system.sys_user u ON u.user_id = f.pitcher_id
        </if>

        LEFT JOIN yooa_system.sys_dept d ON d.dept_id = u.dept_id

                <if test="query.customerNameOrIdQuery != null and query.customerNameOrIdQuery != '' or (query.accountType != null) ">
                LEFT JOIN crm_customer c on cja.customer_id = c.customer_id
                LEFT JOIN yooa_system.sys_user_pd up2 ON up2.pd_user_id = cja.operate_id
                LEFT JOIN yooa_system.sys_user u2 ON u2.user_id = up2.user_id
                LEFT JOIN yooa_system.sys_dept d2 ON d2.dept_id = u2.dept_id
                </if>
                <if test="query.isItValid != null">
                    LEFT JOIN (
                    SELECT
                    cja.id
                    FROM
                    ( SELECT * FROM yooa_crm.crm_customer_join_anchor WHERE STATUS = '1' ) AS cja
                    LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cja.extend_id
                    JOIN (
                    SELECT
                    cf.customer_id,
                    up.user_id,
                    cf.begin_time,
                    cf.end_time,
                    cf.friend_id
                    FROM
                    yooa_crm.crm_customer_friend cf
                    LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cf.py_extend_id
                    ) cf ON cja.customer_id = cf.customer_id
                    AND up.user_id = cf.user_id
                    AND cf.begin_time &lt;cja.receive_time AND ( cf.end_time IS NULL OR cf.end_time > cja.receive_time)
                    GROUP BY
                    cf.user_id,
                    cf.friend_id
                    ORDER BY
                    cja.id DESC
                    ) cja1 ON cja1.id = cja.id
                </if>
                <if test="query.language != null or query.fansType != null
                or (query.customerNameOrIdQuery != null and query.customerNameOrIdQuery != '')
                or (query.accountType != null)">
                    LEFT JOIN (
                    SELECT
                    f.friend_id,
                    f.friend_name,
                    f.extend_id,
                    f.create_time,
                    f.record_date,
                    f.main_channel_id,
                    f.sub_channel_id,
                    f.pitcher_id,
                    up.py_extend_ids,
                    f.language,
                    f.fans_type
                    FROM
                    crm_friend AS f
                    LEFT JOIN ( SELECT GROUP_CONCAT( pd_user_id ) AS py_extend_ids, user_id FROM yooa_system.sys_user_pd GROUP BY user_id ) up ON up.user_id = f.extend_id
                    ) AS f ON f.friend_id = cf.friend_id AND FIND_IN_SET( cja.extend_id, f.py_extend_ids )
                    LEFT JOIN (
                    SELECT
                    cf.friend_id,
                    MIN( cc.create_time ) AS min_create_time
                    FROM
                    crm_customer_friend cf
                    INNER JOIN crm_customer cc ON cf.customer_id = cc.customer_id
                    GROUP BY
                    cf.friend_id
                    ) fmt ON f.friend_id = fmt.friend_id
                </if>
        WHERE cja.status = 1
        ${query.params.dataScope}
        <if test="query.receiveBeginTime != null and query.receiveEndTime != null">
            AND cja.receive_time BETWEEN #{query.receiveBeginTime} AND #{query.receiveEndTime}
        </if>
        <if test="query.language != null">
            AND f.language = #{query.language}
        </if>
        <if test="query.fansType != null">
            AND f.fans_type = #{query.fansType}
        </if>
        <if test="query.deptId != null">
            AND (
            (FIND_IN_SET(#{query.deptId},d.ancestors))
            OR
            d.dept_id = #{query.deptId}
            )
        </if>
        <if test="query.customerNameOrIdQuery != null and query.customerNameOrIdQuery != ''">
            AND (f.friend_name LIKE CONCAT('%', #{query.customerNameOrIdQuery}, '%')
            OR c.customer_id = #{query.customerNameOrIdQuery}
            OR u.nick_name LIKE CONCAT ('%',#{query.customerNameOrIdQuery},'%')
            OR u2.nick_name LIKE CONCAT ('%',#{query.customerNameOrIdQuery},'%')
            )
        </if>
        <if test="query.bindType != null and query.bindType != ''">
            AND
            CASE
            WHEN cf.id IS NULL THEN
            '0' ELSE '1'
            END = #{query.bindType}
        </if>
        <if test="query.accountType != null">
            AND
            CASE
            WHEN  c.create_time = fmt.min_create_time  THEN
            '0'
            WHEN cf.id IS NULL THEN
            '0'
            ELSE '1'
            END  = #{query.accountType}
        </if>

        <if test="query.isItValid != null">
            AND
            CASE WHEN cja1.id IS NOT NULL THEN
            '0'
            ELSE '1'
            END = #{query.isItValid}
        </if>

        ORDER BY cja.receive_time DESC
    </select>
    <select id="queryDetailById" resultType="com.yooa.crm.api.domain.vo.CustomerJoinAnchorVo">
        SELECT
        cja.customer_id AS customer_id,
        c.customer_name,
        cja.receive_time,
        CASE
        WHEN c.create_time = fmt.min_create_time THEN
        '0'
        WHEN cf.id IS NULL THEN
        '0'
        ELSE '1'
        END AS accountType,
        CASE
        WHEN cja1.id IS NOT NULL THEN
        '0'
        ELSE '1'
        END isItValid,
        u.nick_name AS extendUserName,
        d.dept_id AS extendDeptId,
        d.ancestors_names AS extendAncestors,
        d.ancestors AS extendAncestorsId,
        d.dept_name AS extendDeptName,
        a.anchor_name anchorName,
        f.friend_id AS friendId,
        f.friend_name AS friendName,
        f.LANGUAGE,
        f.fans_type AS fansType,
        f.extend_id AS receiveId,
        ( SELECT channel_name FROM crm_friend_channel WHERE channel_id = main_channel_id ) AS channelName,
        cf.create_by,
        u2.nick_name AS channelNickName,
        CASE
        WHEN cf.id IS NULL THEN
        '0' ELSE '1'
        END bindType,
        u3.nick_name operateUserName,
        d3.dept_id operateDeptId,
        d3.ancestors_names operateAncestors,
        d3.ancestors operateAncestorsId,
        a.anchor_name anchorName,
        d3.dept_name operateDeptName,
        cf.begin_time beginTime,
        cf.end_time endTime
        FROM
        crm_customer_join_anchor cja
        LEFT JOIN crm_anchor a ON a.anchor_id = cja.anchor_id
        LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.extend_id
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN crm_customer c ON cja.customer_id = c.customer_id
        LEFT JOIN crm_customer_friend cf ON cja.customer_id = cf.customer_id AND cf.py_extend_id = cja.extend_id
        LEFT JOIN crm_friend f ON cf.friend_id = f.friend_id
        LEFT JOIN ( SELECT GROUP_CONCAT( pd_user_id ) AS py_extend_ids, user_id yuserId FROM yooa_system.sys_user_pd
        GROUP BY user_id ) up ON up.yuserId = f.extend_id AND FIND_IN_SET( cja.extend_id, up.py_extend_ids )
        LEFT JOIN yooa_system.sys_user u2 ON f.pitcher_id = u2.user_id
        LEFT JOIN yooa_system.sys_user_pd up3 on up3.pd_user_id = cja.operate_id
        LEFT JOIN yooa_system.sys_user u3 ON up3.user_id = u3.user_id
        LEFT JOIN yooa_system.sys_dept d3 ON u3.dept_id = d3.dept_id
        LEFT JOIN (
        SELECT
        cf.friend_id,
        MIN( cc.create_time ) AS min_create_time
        FROM
        crm_customer_friend cf
        INNER JOIN crm_customer cc ON cf.customer_id = cc.customer_id
        GROUP BY
        cf.friend_id
        ) fmt ON f.friend_id = fmt.friend_id
        LEFT JOIN (
        SELECT
        cja.id
        FROM
        ( SELECT * FROM yooa_crm.crm_customer_join_anchor WHERE STATUS = '1' ) AS cja
        LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cja.extend_id
        JOIN (
        SELECT
        cf.customer_id,
        up.user_id,
        cf.begin_time,
        cf.end_time,
        cf.friend_id
        FROM
        yooa_crm.crm_customer_friend cf
        LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cf.py_extend_id
        ) cf ON cja.customer_id = cf.customer_id
        AND up.user_id = cf.user_id
        AND cf.begin_time &lt;cja.receive_time AND ( cf.end_time IS NULL OR cf.end_time > cja.receive_time)
        GROUP BY
        cf.user_id,
        cf.friend_id
        ORDER BY
        cja.id DESC
        ) cja1 ON cja1.id = cja.id
        where
        cja.status = 1
        AND cja.id IN
        <foreach collection="ids" index="index" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>
        ORDER BY cja.receive_time DESC
    </select>

    <select id="getOperatorJoinIds" resultType="java.lang.Long">
        SELECT cja.id
        FROM crm_customer_join_anchor cja
                 LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.operate_id
                 LEFT JOIN yooa_system.sys_user u ON u.user_id = up.user_id
                 LEFT JOIN yooa_system.sys_dept d ON d.dept_id = u.dept_id
                 LEFT JOIN crm_anchor a on a.anchor_id = cja.anchor_id
        <if test="query.queryId != null and query.queryId != ''">
            LEFT JOIN crm_customer c on c.customer_id = cja.customer_id
        </if>
        WHERE
        1 = 1
        ${query.params.dataScope}
        <if test="query.queryId != null and query.queryId != ''">
            AND (cja.anchor_id = #{query.queryId}
            OR cja.customer_id = #{query.queryId}
            OR c.customer_name LIKE CONCAT ('%',#{query.queryId},'%')
            OR a.anchor_name LIKE CONCAT('%',#{query.queryId},'%'))
        </if>
        <if test="query.type != null">
            AND cja.type = #{query.type}
        </if>
        <if test="query.beginHandoverTime != null and query.afterHandoverTime != null">
            AND cja.handover_time >= CONCAT(#{query.beginHandoverTime},' 00:00:00') AND cja.handover_time &lt;= CONCAT(#{query.afterHandoverTime},' 23:59:59')
        </if>
        <if test="query.beginReceiveTime != null and query.afterReceiveTime != null">
            AND cja.receive_time >= CONCAT(#{query.beginReceiveTime},' 00:00:00') AND cja.receive_time &lt;= CONCAT(#{query.afterReceiveTime},' 23:59:59')
        </if>
        <if test="query.status != null">
            AND cja.status = #{query.status}
        </if>
        ORDER BY cja.handover_time DESC
    </select>

    <select id="queryOperateJoinAnchorById"
            resultType="com.yooa.crm.api.domain.vo.CustomerOperateJoinAnchorVo">
        SELECT
            cja.customer_id customerId,
            cja.anchor_id anchorId,
            a.anchor_name anchorNickName,
            ai.anchor_name anchorName,
            c.customer_name customerName,
            d1.dept_name deptName,
            d1.ancestors_names ancestorsNames,
            u1.nick_name operationName,
            u2.nick_name extendUserName,
            d2.ancestors_names extendAncestors,
            d2.dept_name extendDeptName,
            cja.handover_time handoverTime,
            cja.receive_time receiveTime,
            cja.type,
            cja.status,
            cja.img,
            cja.feedback_msg feedbackMsg,
            cja.remark
        FROM
            crm_customer_join_anchor cja
                LEFT JOIN crm_customer c ON cja.customer_id = c.customer_id
                LEFT JOIN crm_anchor a ON cja.anchor_id = a.anchor_id
                LEFT JOIN crm_anchor_account_mapping aam ON a.anchor_id = aam.anchor_id
                LEFT JOIN crm_anchor_info ai ON ai.anchor_id = aam.anchor_id
                LEFT JOIN yooa_system.sys_user_pd up1 ON up1.pd_user_id = cja.operate_id
                LEFT JOIN yooa_system.sys_user u1 ON u1.user_id = up1.user_id
                LEFT JOIN yooa_system.sys_dept d1 ON u1.dept_id = d1.dept_id
                LEFT JOIN yooa_system.sys_user_pd up2 ON up2.pd_user_id = cja.extend_id
                LEFT JOIN yooa_system.sys_user u2 ON u2.user_id = up2.user_id
                LEFT JOIN yooa_system.sys_dept d2 ON u2.dept_id = d2.dept_id
        WHERE cja.id  IN
        <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="queryDetail" resultType="com.yooa.crm.api.domain.vo.CustomerJoinAnchorVo">
        SELECT
        cja.customer_id AS customer_id,
        c.customer_name,
        cja.receive_time,
        CASE
        WHEN c.create_time = fmt.min_create_time THEN
        '0'
        WHEN cf.id IS NULL THEN
        '0'
        ELSE '1'
        END AS accountType,
        u.nick_name AS extendUserName,
        d.dept_id AS extendDeptId,
        d.ancestors_names AS extendAncestors,
        d.ancestors AS extendAncestorsId,
        d.dept_name AS extendDeptName,
        a.anchor_name anchorName,
        f.friend_id AS friendId,
        f.friend_name AS friendName,
        f.LANGUAGE,
        f.fans_type AS fansType,
        f.extend_id AS receiveId,
        ( SELECT channel_name FROM crm_friend_channel WHERE channel_id = main_channel_id ) AS channelName,
        cf.create_by,
        u2.nick_name AS channelNickName,
        CASE
        WHEN cf.id IS NULL THEN
        '0' ELSE '1'
        END bindType,
        u3.nick_name operateUserName,
        d3.dept_id operateDeptId,
        d3.ancestors_names operateAncestors,
        d3.ancestors operateAncestorsId,
        a.anchor_name anchorName,
        CASE
        WHEN cja1.id IS NOT NULL THEN
        '0'
        ELSE '1'
        END isItValid
        FROM
        crm_customer_join_anchor cja
        LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.extend_id
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN crm_customer c ON cja.customer_id = c.customer_id
        LEFT JOIN crm_customer_friend cf ON cja.customer_id = cf.customer_id AND cf.py_extend_id = cja.extend_id
        LEFT JOIN crm_friend f ON cf.friend_id = f.friend_id
        LEFT JOIN ( SELECT GROUP_CONCAT( pd_user_id ) AS py_extend_ids, user_id yuserId FROM yooa_system.sys_user_pd
        GROUP BY user_id ) up ON up.yuserId = f.extend_id AND FIND_IN_SET( cja.extend_id, up.py_extend_ids )
        LEFT JOIN yooa_system.sys_user u2 ON f.pitcher_id = u2.user_id
        LEFT JOIN crm_anchor a ON a.anchor_id = cja.anchor_id
        LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = cja.anchor_id
        LEFT JOIN yooa_system.sys_user_pd up3 on up3.pd_user_id = cja.operate_id
        LEFT JOIN yooa_system.sys_user u3 ON up3.user_id = u3.user_id
        LEFT JOIN yooa_system.sys_dept d3 ON u3.dept_id = d3.dept_id
        LEFT JOIN (
        SELECT
        cf.friend_id,
        MIN( cc.create_time ) AS min_create_time
        FROM
        crm_customer_friend cf
        INNER JOIN crm_customer cc ON cf.customer_id = cc.customer_id
        GROUP BY
        cf.friend_id
        ) fmt ON f.friend_id = fmt.friend_id
        LEFT JOIN (
        SELECT
        cja.id
        FROM
        ( SELECT * FROM yooa_crm.crm_customer_join_anchor WHERE STATUS = '1' ) AS cja
        LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cja.extend_id
        JOIN (
        SELECT
        cf.customer_id,
        up.user_id,
        cf.begin_time,
        cf.end_time,
        cf.friend_id
        FROM
        yooa_crm.crm_customer_friend cf
        LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cf.py_extend_id
        ) cf ON cja.customer_id = cf.customer_id
        AND up.user_id = cf.user_id
        AND cf.begin_time &lt;cja.receive_time AND ( cf.end_time IS NULL OR cf.end_time > cja.receive_time)
        GROUP BY
        cf.user_id,
        cf.friend_id
        ORDER BY
        cja.id DESC
        ) cja1 ON cja1.id = cja.id
        where cja.`status` = 1
        ${query.params.dataScope}
        <if test="query.receiveBeginTime != null and query.receiveEndTime != null">
            AND cja.receive_time BETWEEN #{query.receiveBeginTime} AND #{query.receiveEndTime}
        </if>
        <if test="query.language != null">
            AND f.language = #{query.language}
        </if>
        <if test="query.fansType != null and f.fansType != ''">
            AND f.fans_type = #{query.fansType}
        </if>
        <if test="query.deptId != null">
            AND (
            (FIND_IN_SET(#{query.deptId},d.ancestors))
            OR
            d.dept_id = #{query.deptId}
            )
        </if>
        <if test="query.customerNameOrIdQuery != null and query.customerNameOrIdQuery != ''">
            AND (f.friend_name LIKE CONCAT('%', #{query.customerNameOrIdQuery}, '%')
            OR c.customer_id = #{query.customerNameOrIdQuery}
            OR u.nick_name LIKE CONCAT ('%',#{query.customerNameOrIdQuery},'%')
            OR u3.nick_name LIKE CONCAT ('%',#{query.customerNameOrIdQuery},'%')
            )
        </if>
        <if test="query.bindType != null and query.bindType != ''">
            AND
            CASE
            WHEN cf.id IS NULL THEN
            '0' ELSE '1'
            END = #{query.bindType}
        </if>
        <if test="query.isItValid != null">
            AND
            CASE WHEN cja1.id IS NOT NULL THEN
            '0'
            ELSE '1'
            END = #{query.isItValid}
        </if>
        GROUP BY cja.id
        ORDER BY cja.receive_time DESC
    </select>
    <select id="getOperatorJoinAnchorListExport"
            resultType="com.yooa.crm.api.domain.vo.CustomerOperateJoinAnchorVo">
        SELECT
        cja.customer_id customerId,
        cja.anchor_id anchorId,
        a.anchor_name anchorNickName,
        ai.anchor_name anchorName,
        c.customer_name customerName,
        d.dept_name deptName,
        d.ancestors_names ancestorsNames,
        u.nick_name operationName,
        u2.nick_name extendUserName,
        d2.ancestors_names extendAncestors,
        d2.dept_name extendDeptName,
        cja.handover_time handoverTime,
        cja.receive_time receiveTime,
        cja.type,
        cja.status,
        cja.img,
        cja.feedback_msg feedbackMsg,
        cja.remark
        FROM
        crm_customer_join_anchor cja
        LEFT JOIN crm_customer c ON cja.customer_id = c.customer_id
        LEFT JOIN crm_anchor a ON cja.anchor_id = a.anchor_id
        LEFT JOIN crm_anchor_account_mapping aam ON a.anchor_id = aam.anchor_id
        LEFT JOIN crm_anchor_info ai ON ai.anchor_id = aam.anchor_id
        LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.operate_id
        LEFT JOIN yooa_system.sys_user u ON u.user_id = up.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN yooa_system.sys_user_pd up2 ON up2.pd_user_id = cja.extend_id
        LEFT JOIN yooa_system.sys_user u2 ON u2.user_id = up2.user_id
        LEFT JOIN yooa_system.sys_dept d2 ON u2.dept_id = d2.dept_id
        WHERE
        1 = 1
        ${query.params.dataScope}
        <if test="query.queryId != null and query.queryId != ''">
            AND (cja.anchor_id = #{query.queryId}
            OR cja.customer_id = #{query.queryId}
            OR c.customer_name LIKE CONCAT ('%',#{query.queryId},'%')
            OR a.anchor_name LIKE CONCAT('%',#{query.queryId},'%'))
        </if>
        <if test="query.type != null">
            AND cja.type = #{query.type}
        </if>
        <if test="query.status != null">
            AND cja.status = #{query.status}
        </if>
        <if test="query.beginHandoverTime != null and query.afterHandoverTime != null">
            AND cja.handover_time >= CONCAT(#{query.beginHandoverTime},' 00:00:00') AND cja.handover_time &lt;= CONCAT(#{query.afterHandoverTime},' 23:59:59')
        </if>
        <if test="query.beginReceiveTime != null and query.afterReceiveTime != null">
            AND cja.receive_time >= CONCAT(#{query.beginReceiveTime},' 00:00:00') AND cja.receive_time &lt;= CONCAT(#{query.afterReceiveTime},' 23:59:59')
        </if>
        ORDER BY cja.handover_time DESC
    </select>

    <select id="joinAnchorOperateVermicelli" resultType="com.yooa.extend.api.domain.OperateVermicelli">
        SELECT
            cja.customer_id,
            cja.anchor_id                                                                              AS anchorId,
            cja.operate_id                                                                             AS pyOperateId,
            u.user_id                                                                                  AS operateId,
            u.dept_id                                                                                  AS operateDeptId,
            (SELECT GROUP_CONCAT(customer_id) FROM crm_customer_friend WHERE friend_id = cf.friend_id) AS customerIds,
            cf.friend_id                                                                               AS friendId
        FROM
        (
            SELECT
                cja.anchor_id,
                cja.customer_id,
                cja.operate_id,
                cja.extend_id,
                cja.receive_time
            FROM crm_customer_join_anchor cja
            WHERE cja.customer_id = #{customerId}
                AND cja.`status` = 1
                AND cja.receive_time >= #{beginTime}
                AND cja.receive_time &lt;= #{endTime}
        ) cja
        LEFT JOIN `yooa_system`.sys_user_pd up ON up.pd_user_id = cja.operate_id
        LEFT JOIN `yooa_system`.sys_user u ON u.user_id = up.user_id
        LEFT JOIN crm_customer_friend cf ON cja.customer_id = cf.customer_id AND cja.extend_id = cf.py_extend_id AND cf.begin_time &lt;= cja.receive_time AND (cf.end_time IS NULL OR cf.end_time > cja.receive_time)
        GROUP BY cf.friend_id
    </select>

    <!-- 投手一交列表查询 -->
    <select id="selectPitcherJoinAnchorList" resultType="com.yooa.crm.api.domain.vo.CustomerJoinAnchorVo">
        SELECT
            cja.customer_id AS customerId,
            c.customer_name AS customerName,
            cja.receive_time AS receiveTime,
            CASE
                WHEN c.create_time = fmt.min_create_time THEN 0
                WHEN cf.id IS NULL THEN 0
                ELSE 1
            END AS accountType,
            CASE
                WHEN cja1.id IS NOT NULL THEN 0
                ELSE 1
            END AS isItValid,
            u.nick_name AS extendUserName,
            d.dept_id AS extendDeptId,
            d.ancestors_names AS extendAncestors,
            d.dept_name AS extendDeptName,
            f.friend_id AS friendId,
            f.friend_name AS friendName,
            f.language,
            f.fans_type AS fansType,
            f.extend_id AS receiveId,
            (SELECT channel_name FROM crm_friend_channel WHERE channel_id = f.main_channel_id) AS channelName,
            cf.create_by,
            u2.nick_name AS channelNickName,
            CASE
                WHEN cf.id IS NULL THEN '0'
                ELSE '1'
            END AS bindType,
            cf.begin_time AS beginTime,
            cf.end_time AS endTime
        FROM
            crm_customer_join_anchor cja
            LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.extend_id
            LEFT JOIN crm_customer c ON cja.customer_id = c.customer_id
            LEFT JOIN crm_customer_friend cf ON cf.customer_id = cja.customer_id
                AND cf.py_extend_id = cja.extend_id
                AND cf.begin_time &lt; cja.receive_time
                AND (cf.end_time IS NULL OR cf.end_time > cja.receive_time)
        LEFT JOIN crm_friend f ON cf.friend_id = f.friend_id
        LEFT JOIN yooa_system.sys_user u ON u.user_id = f.pitcher_id
            LEFT JOIN yooa_system.sys_dept d ON d.dept_id = u.dept_id
            LEFT JOIN yooa_system.sys_user u2 ON f.pitcher_id = u2.user_id
            LEFT JOIN (
                SELECT
                    cf.friend_id,
                    MIN(cc.create_time) AS min_create_time
                FROM
                    crm_customer_friend cf
                    INNER JOIN crm_customer cc ON cf.customer_id = cc.customer_id
                GROUP BY
                    cf.friend_id
            ) fmt ON f.friend_id = fmt.friend_id
            <if test="query.isItValid != null">
                LEFT JOIN (
                    SELECT
                        cja.id
                    FROM
                        (SELECT * FROM yooa_crm.crm_customer_join_anchor WHERE status = '1') AS cja
                        LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cja.extend_id
                        JOIN (
                            SELECT
                                cf.customer_id,
                                up.user_id,
                                cf.begin_time,
                                cf.end_time,
                                cf.friend_id
                            FROM
                                yooa_crm.crm_customer_friend cf
                                LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cf.py_extend_id
                        ) cf ON cja.customer_id = cf.customer_id
                            AND up.user_id = cf.user_id
                            AND cf.begin_time &lt; cja.receive_time
                            AND (cf.end_time IS NULL OR cf.end_time > cja.receive_time)
                    GROUP BY
                        cf.user_id,
                        cf.friend_id
                    ORDER BY
                        cja.id DESC
                ) cja1 ON cja1.id = cja.id
            </if>
        <where>
            cja.status = 1
            ${query.params.dataScope}
            <if test="query.receiveBeginTime != null and query.receiveEndTime != null">
                AND cja.receive_time BETWEEN #{query.receiveBeginTime} AND #{query.receiveEndTime}
            </if>
            <if test="query.language != null">
                AND f.language = #{query.language}
            </if>
            <if test="query.fansType != null">
                AND f.fans_type = #{query.fansType}
            </if>
            <if test="query.deptId != null">
                AND d.dept_id = #{query.deptId}
            </if>
            <if test="query.customerNameOrIdQuery != null and query.customerNameOrIdQuery != ''">
                AND (cja.customer_id = #{query.customerNameOrIdQuery}
                OR c.customer_name LIKE CONCAT('%', #{query.customerNameOrIdQuery}, '%'))
            </if>
            <if test="query.accountType != null">
                AND CASE
                    WHEN c.create_time = fmt.min_create_time THEN 0
                    WHEN cf.id IS NULL THEN 0
                    ELSE 1
                END = #{query.accountType}
            </if>
            <if test="query.bindType != null and query.bindType != ''">
                AND CASE
                    WHEN cf.id IS NULL THEN '0'
                    ELSE '1'
                END = #{query.bindType}
            </if>
            <if test="query.isItValid != null">
                AND CASE
                    WHEN cja1.id IS NOT NULL THEN 0
                    ELSE 1
                END = #{query.isItValid}
            </if>
        </where>
        GROUP BY cja.id
        ORDER BY cja.receive_time DESC
    </select>

    <!-- 导出投手一交列表查询 -->
    <select id="exportPitcherJoinAnchorList" resultType="com.yooa.crm.api.domain.vo.CustomerJoinAnchorVo">
        SELECT
        cja.customer_id AS customer_id,
        c.customer_name,
        cja.receive_time,
        CASE
        WHEN c.create_time = fmt.min_create_time THEN
        '0'
        WHEN cf.id IS NULL THEN
        '0'
        ELSE '1'
        END AS accountType,
        u.nick_name AS extendUserName,
        d.dept_id AS extendDeptId,
        d.ancestors_names AS extendAncestors,
        d.ancestors AS extendAncestorsId,
        d.dept_name AS extendDeptName,
        a.anchor_name anchorName,
        f.friend_id AS friendId,
        f.friend_name AS friendName,
        f.LANGUAGE,
        f.fans_type AS fansType,
        f.extend_id AS receiveId,
        ( SELECT channel_name FROM crm_friend_channel WHERE channel_id = main_channel_id ) AS channelName,
        cf.create_by,
        u2.nick_name AS channelNickName,
        CASE
        WHEN cf.id IS NULL THEN
        '0' ELSE '1'
        END bindType,
        u3.nick_name operateUserName,
        d3.dept_id operateDeptId,
        d3.ancestors_names operateAncestors,
        d3.ancestors operateAncestorsId,
        a.anchor_name anchorName,
        CASE
        WHEN cja1.id IS NOT NULL THEN
        '0'
        ELSE '1'
        END isItValid
        FROM
        crm_customer_join_anchor cja
        LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.extend_id
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN crm_customer c ON cja.customer_id = c.customer_id
        LEFT JOIN crm_customer_friend cf ON cja.customer_id = cf.customer_id AND cf.py_extend_id = cja.extend_id
        LEFT JOIN crm_friend f ON cf.friend_id = f.friend_id
        LEFT JOIN ( SELECT GROUP_CONCAT( pd_user_id ) AS py_extend_ids, user_id yuserId FROM yooa_system.sys_user_pd
        GROUP BY user_id ) up ON up.yuserId = f.extend_id AND FIND_IN_SET( cja.extend_id, up.py_extend_ids )
        LEFT JOIN yooa_system.sys_user u2 ON f.pitcher_id = u2.user_id
        LEFT JOIN yooa_system.sys_dept d2 ON u2.dept_id = d2.dept_id
        LEFT JOIN crm_anchor a ON a.anchor_id = cja.anchor_id
        LEFT JOIN crm_anchor_operate ao ON ao.anchor_id = cja.anchor_id
        LEFT JOIN yooa_system.sys_user_pd up3 on up3.pd_user_id = cja.operate_id
        LEFT JOIN yooa_system.sys_user u3 ON up3.user_id = u3.user_id
        LEFT JOIN yooa_system.sys_dept d3 ON u3.dept_id = d3.dept_id
        LEFT JOIN (
        SELECT
        cf.friend_id,
        MIN( cc.create_time ) AS min_create_time
        FROM
        crm_customer_friend cf
        INNER JOIN crm_customer cc ON cf.customer_id = cc.customer_id
        GROUP BY
        cf.friend_id
        ) fmt ON f.friend_id = fmt.friend_id
        LEFT JOIN (
        SELECT
        cja.id
        FROM
        ( SELECT * FROM yooa_crm.crm_customer_join_anchor WHERE STATUS = '1' ) AS cja
        LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cja.extend_id
        JOIN (
        SELECT
        cf.customer_id,
        up.user_id,
        cf.begin_time,
        cf.end_time,
        cf.friend_id
        FROM
        yooa_crm.crm_customer_friend cf
        LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cf.py_extend_id
        ) cf ON cja.customer_id = cf.customer_id
        AND up.user_id = cf.user_id
        AND cf.begin_time &lt;cja.receive_time AND ( cf.end_time IS NULL OR cf.end_time > cja.receive_time)
        GROUP BY
        cf.user_id,
        cf.friend_id
        ORDER BY
        cja.id DESC
        ) cja1 ON cja1.id = cja.id
        where cja.`status` = 1
        AND f.pitcher_id IS NOT NULL
        ${query.params.dataScope}
        <if test="query.receiveBeginTime != null and query.receiveEndTime != null">
            AND cja.receive_time BETWEEN #{query.receiveBeginTime} AND #{query.receiveEndTime}
        </if>
        <if test="query.language != null">
            AND f.language = #{query.language}
        </if>
        <if test="query.fansType != null and f.fansType != ''">
            AND f.fans_type = #{query.fansType}
        </if>
        <if test="query.deptId != null">
            AND (
            (FIND_IN_SET(#{query.deptId},d.ancestors))
            OR
            d.dept_id = #{query.deptId}
            )
        </if>
        <if test="query.customerNameOrIdQuery != null and query.customerNameOrIdQuery != ''">
            AND (f.friend_name LIKE CONCAT('%', #{query.customerNameOrIdQuery}, '%')
            OR c.customer_id = #{query.customerNameOrIdQuery}
            OR u.nick_name LIKE CONCAT ('%',#{query.customerNameOrIdQuery},'%')
            OR u3.nick_name LIKE CONCAT ('%',#{query.customerNameOrIdQuery},'%')
            )
        </if>
        <if test="query.bindType != null and query.bindType != ''">
            AND
            CASE
            WHEN cf.id IS NULL THEN
            '0' ELSE '1'
            END = #{query.bindType}
        </if>
        <if test="query.isItValid != null">
            AND
            CASE WHEN cja1.id IS NOT NULL THEN
            '0'
            ELSE '1'
            END = #{query.isItValid}
        </if>
        GROUP BY cja.id
        ORDER BY cja.receive_time DESC
    </select>

    <select id="getPitcherIds" resultType="java.lang.Long">
        SELECT
        cja.id
        FROM
        crm_customer_join_anchor cja
        LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.extend_id

        LEFT JOIN crm_customer_friend AS cf ON cf.customer_id = cja.customer_id AND cf.py_extend_id = cja.extend_id
        AND cf.begin_time &lt; cja.receive_time AND ( cf.end_time IS NULL OR cf.end_time > cja.receive_time)
        LEFT JOIN
        crm_friend f
        ON cf.friend_id = f.friend_id
        LEFT JOIN yooa_system.sys_user u ON u.user_id = f.pitcher_id

        LEFT JOIN yooa_system.sys_dept d ON d.dept_id = u.dept_id

        <if test="query.customerNameOrIdQuery != null and query.customerNameOrIdQuery != '' or (query.accountType != null) ">
            LEFT JOIN crm_customer c on cja.customer_id = c.customer_id
            LEFT JOIN yooa_system.sys_user_pd up2 ON up2.pd_user_id = cja.operate_id
            LEFT JOIN yooa_system.sys_user u2 ON u2.user_id = up2.user_id
            LEFT JOIN yooa_system.sys_dept d2 ON d2.dept_id = u2.dept_id
        </if>
        <if test="query.isItValid != null">
            LEFT JOIN (
            SELECT
            cja.id
            FROM
            ( SELECT * FROM yooa_crm.crm_customer_join_anchor WHERE STATUS = '1' ) AS cja
            LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cja.extend_id
            JOIN (
            SELECT
            cf.customer_id,
            up.user_id,
            cf.begin_time,
            cf.end_time,
            cf.friend_id
            FROM
            yooa_crm.crm_customer_friend cf
            LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cf.py_extend_id
            ) cf ON cja.customer_id = cf.customer_id
            AND up.user_id = cf.user_id
            AND cf.begin_time &lt;cja.receive_time AND ( cf.end_time IS NULL OR cf.end_time > cja.receive_time)
            GROUP BY
            cf.user_id,
            cf.friend_id
            ORDER BY
            cja.id DESC
            ) cja1 ON cja1.id = cja.id
        </if>
        <if test="query.language != null or query.fansType != null
                or (query.customerNameOrIdQuery != null and query.customerNameOrIdQuery != '')
                or (query.accountType != null)">
            LEFT JOIN (
            SELECT
            f.friend_id,
            f.friend_name,
            f.extend_id,
            f.create_time,
            f.record_date,
            f.main_channel_id,
            f.sub_channel_id,
            f.pitcher_id,
            up.py_extend_ids,
            f.language,
            f.fans_type
            FROM
            crm_friend AS f
            LEFT JOIN ( SELECT GROUP_CONCAT( pd_user_id ) AS py_extend_ids, user_id FROM yooa_system.sys_user_pd GROUP BY user_id ) up ON up.user_id = f.extend_id
            ) AS f ON f.friend_id = cf.friend_id AND FIND_IN_SET( cja.extend_id, f.py_extend_ids )
            LEFT JOIN (
            SELECT
            cf.friend_id,
            MIN( cc.create_time ) AS min_create_time
            FROM
            crm_customer_friend cf
            INNER JOIN crm_customer cc ON cf.customer_id = cc.customer_id
            GROUP BY
            cf.friend_id
            ) fmt ON f.friend_id = fmt.friend_id
        </if>
        WHERE cja.status = 1
        AND f.pitcher_id IS NOT NULL
        ${query.params.dataScope}
        <if test="query.receiveBeginTime != null and query.receiveEndTime != null">
            AND cja.receive_time BETWEEN #{query.receiveBeginTime} AND #{query.receiveEndTime}
        </if>
        <if test="query.language != null">
            AND f.language = #{query.language}
        </if>
        <if test="query.fansType != null">
            AND f.fans_type = #{query.fansType}
        </if>
        <if test="query.deptId != null">
            AND (
            (FIND_IN_SET(#{query.deptId},d.ancestors))
            OR
            d.dept_id = #{query.deptId}
            )
        </if>
        <if test="query.customerNameOrIdQuery != null and query.customerNameOrIdQuery != ''">
            AND (f.friend_name LIKE CONCAT('%', #{query.customerNameOrIdQuery}, '%')
            OR c.customer_id = #{query.customerNameOrIdQuery}
            OR u.nick_name LIKE CONCAT ('%',#{query.customerNameOrIdQuery},'%')
            OR u2.nick_name LIKE CONCAT ('%',#{query.customerNameOrIdQuery},'%')
            )
        </if>
        <if test="query.bindType != null and query.bindType != ''">
            AND
            CASE
            WHEN cf.id IS NULL THEN
            '0' ELSE '1'
            END = #{query.bindType}
        </if>
        <if test="query.accountType != null">
            AND
            CASE
            WHEN  c.create_time = fmt.min_create_time  THEN
            '0'
            WHEN cf.id IS NULL THEN
            '0'
            ELSE '1'
            END  = #{query.accountType}
        </if>

        <if test="query.isItValid != null">
            AND
            CASE WHEN cja1.id IS NOT NULL THEN
            '0'
            ELSE '1'
            END = #{query.isItValid}
        </if>

        ORDER BY cja.receive_time DESC
    </select>

    <select id="queryPitcherDetailById" resultType="com.yooa.crm.api.domain.vo.CustomerJoinAnchorVo">
        SELECT
        cja.customer_id AS customer_id,
        c.customer_name,
        cja.receive_time,
        CASE
        WHEN c.create_time = fmt.min_create_time THEN
        '0'
        WHEN cf.id IS NULL THEN
        '0'
        ELSE '1'
        END AS accountType,
        CASE
        WHEN cja1.id IS NOT NULL THEN
        '0'
        ELSE '1'
        END isItValid,
        u.nick_name AS extendUserName,
        d.dept_id AS extendDeptId,
        d.ancestors_names AS extendAncestors,
        d.ancestors AS extendAncestorsId,
        d.dept_name AS extendDeptName,
        a.anchor_name anchorName,
        f.friend_id AS friendId,
        f.friend_name AS friendName,
        f.LANGUAGE,
        f.fans_type AS fansType,
        f.extend_id AS receiveId,
        ( SELECT channel_name FROM crm_friend_channel WHERE channel_id = main_channel_id ) AS channelName,
        cf.create_by,
        u2.nick_name AS channelNickName,
        CASE
        WHEN cf.id IS NULL THEN
        '0' ELSE '1'
        END bindType,
        u3.nick_name operateUserName,
        d3.dept_id operateDeptId,
        d3.ancestors_names operateAncestors,
        d3.ancestors operateAncestorsId,
        a.anchor_name anchorName,
        d3.dept_name operateDeptName,
        cf.begin_time beginTime,
        cf.end_time endTime
        FROM
        crm_customer_join_anchor cja
        LEFT JOIN crm_anchor a ON a.anchor_id = cja.anchor_id
        LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cja.extend_id
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN crm_customer c ON cja.customer_id = c.customer_id
        LEFT JOIN crm_customer_friend cf ON cja.customer_id = cf.customer_id AND cf.py_extend_id = cja.extend_id
        LEFT JOIN crm_friend f ON cf.friend_id = f.friend_id
        LEFT JOIN ( SELECT GROUP_CONCAT( pd_user_id ) AS py_extend_ids, user_id yuserId FROM yooa_system.sys_user_pd
        GROUP BY user_id ) up ON up.yuserId = f.extend_id AND FIND_IN_SET( cja.extend_id, up.py_extend_ids )
        LEFT JOIN yooa_system.sys_user u2 ON f.pitcher_id = u2.user_id
        LEFT JOIN yooa_system.sys_user_pd up3 on up3.pd_user_id = cja.operate_id
        LEFT JOIN yooa_system.sys_user u3 ON up3.user_id = u3.user_id
        LEFT JOIN yooa_system.sys_dept d3 ON u3.dept_id = d3.dept_id
        LEFT JOIN (
        SELECT
        cf.friend_id,
        MIN( cc.create_time ) AS min_create_time
        FROM
        crm_customer_friend cf
        INNER JOIN crm_customer cc ON cf.customer_id = cc.customer_id
        GROUP BY
        cf.friend_id
        ) fmt ON f.friend_id = fmt.friend_id
        LEFT JOIN (
        SELECT
        cja.id
        FROM
        ( SELECT * FROM yooa_crm.crm_customer_join_anchor WHERE STATUS = '1' ) AS cja
        LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cja.extend_id
        JOIN (
        SELECT
        cf.customer_id,
        up.user_id,
        cf.begin_time,
        cf.end_time,
        cf.friend_id
        FROM
        yooa_crm.crm_customer_friend cf
        LEFT JOIN yooa_system.sys_user_pd AS up ON up.pd_user_id = cf.py_extend_id
        ) cf ON cja.customer_id = cf.customer_id
        AND up.user_id = cf.user_id
        AND cf.begin_time &lt;cja.receive_time AND ( cf.end_time IS NULL OR cf.end_time > cja.receive_time)
        GROUP BY
        cf.user_id,
        cf.friend_id
        ORDER BY
        cja.id DESC
        ) cja1 ON cja1.id = cja.id
        where
        cja.status = 1
          AND f.pitcher_id IS NOT NULL
        AND cja.id IN
        <foreach collection="ids" index="index" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>
        ORDER BY cja.receive_time DESC
    </select>
</mapper>
