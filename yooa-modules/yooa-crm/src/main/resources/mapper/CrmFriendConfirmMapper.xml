<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.crm.mapper.CrmFriendConfirmMapper">

    <select id="selectListByExtend" resultType="com.yooa.crm.api.domain.vo.FriendConfirmVo">
        SELECT
            fc.*,
            d.dept_id AS extendDeptId,
            d.ancestors_names AS extendDeptAncestorName,
            d.dept_name AS extendDeptName,
            d1.dept_id AS pitcherDeptId,
            d1.ancestors_names AS pitcherDeptAncestorName,
            d1.dept_name AS pitcherDeptName,
            u.nick_name AS extendName,
            u1.nick_name AS pitcherName,
            cfc1.channel_name AS mainChannelName,
            cfc2.channel_name AS subChannelName
        FROM crm_friend_confirm fc
        LEFT JOIN yooa_system.sys_user u ON fc.extend_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN yooa_system.sys_user u1 ON fc.pitcher_id = u1.user_id
        LEFT JOIN yooa_system.sys_dept d1 ON u1.dept_id = d1.dept_id
        LEFT JOIN crm_friend_channel cfc1 ON fc.main_channel_id = cfc1.channel_id
        LEFT JOIN crm_friend_channel cfc2 ON fc.sub_channel_id = cfc2.channel_id
        <where>
            <if test="query.friendDate != null">
                AND (fc.friend_date BETWEEN #{query.friendDate[0]} AND #{query.friendDate[1]})
            </if>
            <if test="query.keyWord != null and query.keyWord != ''">
                AND (u.nick_name like concat('%',#{query.keyWord},'%')
                OR u1.nick_name like concat('%',#{query.keyWord},'%'))
            </if>
            <if test="query.publicType != null and query.publicType != ''">
                AND fc.public_type = #{query.publicType}
            </if>
            <if test="query.mainChannelId != null and query.mainChannelId != ''">
                AND fc.main_channel_id = #{query.mainChannelId}
            </if>
            <if test="query.subChannelId != null and query.subChannelId != ''">
                AND fc.sub_channel_id = #{query.subChannelId}
            </if>
            <if test="query.sex != null and query.sex != ''">
                AND fc.sex = #{query.sex}
            </if>
            <if test="query.language != null and query.language != ''">
                AND fc.language = #{query.language}
            </if>
            <if test="query.status != null and query.status != ''">
                AND fc.status = #{query.status}
            </if>
            <if test= "query.extendId == null">
                ${query.params.dataScope}
            </if>
            <if test="query.extendId != null">
                AND fc.extend_id = #{query.extendId}
            </if>
        </where>
        ORDER BY fc.confirm_id DESC
    </select>

    <select id="selectListByPitcher" resultType="com.yooa.crm.api.domain.vo.FriendConfirmVo">
        SELECT
            fc.*,
            d1.dept_id AS extendDeptId,
            d1.ancestors_names AS extendDeptAncestorName,
            d1.dept_name AS extendDeptName,
            d.dept_id AS pitcherDeptId,
            d.ancestors_names AS pitcherDeptAncestorName,
            d.dept_name AS pitcherDeptName,
            u1.nick_name AS extendName,
            u.nick_name AS pitcherName,
            cfc1.channel_name AS mainChannelName,
            cfc2.channel_name AS subChannelName
        FROM crm_friend_confirm fc
        LEFT JOIN yooa_system.sys_user u ON fc.pitcher_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN yooa_system.sys_user u1 ON fc.extend_id = u1.user_id
        LEFT JOIN yooa_system.sys_dept d1 ON u1.dept_id = d1.dept_id
        LEFT JOIN crm_friend_channel cfc1 ON fc.main_channel_id = cfc1.channel_id
        LEFT JOIN crm_friend_channel cfc2 ON fc.sub_channel_id = cfc2.channel_id
        <where>
            fc.status != '4'
            <if test="query.friendDate != null">
                AND (fc.friend_date BETWEEN #{query.friendDate[0]} AND #{query.friendDate[1]})
            </if>
            <if test="query.keyWord != null and query.keyWord != ''">
                AND (u1.nick_name like concat('%',#{query.keyWord},'%')
                OR u.nick_name like concat('%',#{query.keyWord},'%'))
            </if>
            <if test="query.publicType != null and query.publicType != ''">
                AND fc.public_type = #{query.publicType}
            </if>
            <if test="query.mainChannelId != null and query.mainChannelId != ''">
                AND fc.main_channel_id = #{query.mainChannelId}
            </if>
            <if test="query.subChannelId != null and query.subChannelId != ''">
                AND fc.sub_channel_id = #{query.subChannelId}
            </if>
            <if test="query.sex != null and query.sex != ''">
                AND fc.sex = #{query.sex}
            </if>
            <if test="query.language != null and query.language != ''">
                AND fc.language = #{query.language}
            </if>
            <if test="query.status != null and query.status != ''">
                AND fc.status = #{query.status}
            </if>
            <if test= "query.pitcherId == null">
                ${query.params.dataScope}
            </if>
            <if test="query.pitcherId != null">
               and fc.pitcher_id = #{query.pitcherId}
            </if>
        </where>
        ORDER BY fc.confirm_id DESC
    </select>

    <select id="selectByConfirmId" resultType="com.yooa.crm.api.domain.vo.FriendConfirmVo">
        SELECT
            fc.*,
            d.dept_id AS extendDeptId,
            d.ancestors_names AS extendDeptAncestorName,
            d.dept_name AS extendDeptName,
            d1.dept_id AS pitcherDeptId,
            d1.ancestors_names AS pitcherDeptAncestorName,
            d1.dept_name AS pitcherDeptName,
            u.nick_name AS extendName,
            u1.nick_name AS pitcherName,
            cfc1.channel_name AS mainChannelName,
            cfc2.channel_name AS subChannelName,
            (SELECT remark FROM crm_friend_confirm_review WHERE confirm_id = fc.confirm_id AND status = '1' ORDER BY create_time DESC LIMIT 1) AS confirmRemark,
            (SELECT remark FROM crm_friend_confirm_review WHERE confirm_id = fc.confirm_id AND status IN ('2','3') ORDER BY create_time DESC LIMIT 1) AS reviewRemark
        FROM crm_friend_confirm fc
        LEFT JOIN yooa_system.sys_user u ON fc.pitcher_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN yooa_system.sys_user u1 ON fc.extend_id = u1.user_id
        LEFT JOIN yooa_system.sys_dept d1 ON u1.dept_id = d1.dept_id
        LEFT JOIN crm_friend_channel cfc1 ON fc.main_channel_id = cfc1.channel_id
        LEFT JOIN crm_friend_channel cfc2 ON fc.sub_channel_id = cfc2.channel_id
        WHERE confirm_id = #{confirmId}
    </select>
</mapper>
