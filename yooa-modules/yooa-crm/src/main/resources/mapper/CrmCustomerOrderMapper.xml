<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.crm.mapper.CrmCustomerOrderMapper">

    <select id="getOrderUpMessage" resultType="com.yooa.crm.api.domain.dto.OrderUpMessageDto">
        SELECT
            customer_id AS customerId,
            SUM( order_money ) AS totalUp,
            SUM( 1 ) AS totalUpNumber,
            SUM( CASE WHEN DATE_FORMAT( order_time, '%Y-%m' ) = DATE_FORMAT( #{time}, '%Y-%m' ) AND
                py_extend_id IN (
            <foreach collection="pdIds" item="p" separator=",">#{p}
                </foreach>
                )
                           THEN order_money ELSE 0 END ) AS monthUp,
            SUM( CASE WHEN DATE_FORMAT( order_time, '%Y-%m' ) = DATE_FORMAT( #{time}, '%Y-%m' ) AND
                py_extend_id IN (
                <foreach collection="pdIds" item="p" separator=",">
                    #{p}
                </foreach>
                )
                           THEN 1 ELSE 0 END ) AS monthUpNumber
        FROM
            crm_customer_order
        WHERE
            customer_id IN
            <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        GROUP BY
            customer_id
    </select>

    <select id="selectOrderListByCompleteDate" resultType="com.yooa.crm.api.domain.vo.CustomerOrderVo">
        SELECT
            customer_id,
            extend_id,
            order_money,
            ( CASE WHEN cr_update_time IS NOT NULL THEN cr_update_time ELSE c_update_time END ) AS update_time
        FROM
            (
                SELECT
                    co.customer_id AS customer_id,
                    co.py_extend_id AS extend_id,
                    IFNULL( SUM( co.order_money ), 0 ) AS order_money,
                    DATE(c.update_time) AS c_update_time,
                    ( SELECT bind_time FROM crm_customer_user_bind_record WHERE customer_id = co.customer_id AND after_pd_user_id = co.py_extend_id AND type = '1' ORDER BY id DESC LIMIT 1 ) cr_update_time
                FROM
                    crm_customer_order co
                        INNER JOIN crm_customer c ON co.customer_id = c.customer_id
                WHERE
                    DATE(co.order_time) = #{completeDate}
                GROUP BY
                    co.customer_id,
                    co.py_extend_id
            ) co
    </select>

    <select id="selExtendOrderDayMoney" resultType="com.yooa.crm.api.domain.CrmCustomerOrder">
        SELECT DATE(order_time) AS order_time,SUM(order_money) AS order_money,py_extend_id
        FROM crm_customer_order
        <where>
            <if test="ids != null and ids.size > 0">
                py_extend_id IN
                <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="beginTime != null">
                AND order_time >= #{beginTime}
            </if>
            <if test="endTime != null">
                AND order_time &lt; #{endTime}
            </if>
        </where>
        GROUP BY py_extend_id,DATE(order_time)
    </select>

    <select id="selVipOrderDayMoney" resultType="com.yooa.crm.api.domain.CrmCustomerOrder">
        SELECT DATE(order_time) AS order_time,SUM(order_money) AS order_money,serve_id
        FROM crm_customer_order
        <where>
            <if test="ids != null and ids.size > 0">
                serve_id IN
                <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="beginTime != null">
                AND order_time >= #{beginTime}
            </if>
            <if test="endTime != null">
                AND order_time &lt; #{endTime}
            </if>
        </where>
        GROUP BY serve_id,DATE(order_time)
    </select>

    <select id="selExtendOrderDayNewMoney" resultType="com.yooa.crm.api.domain.CrmCustomerOrder">
        SELECT
            DATE(o.order_time) AS order_time,SUM(o.order_money) AS order_money,o.py_extend_id
        FROM crm_customer AS c
        <!-- TODO extendId拆分 -->
        LEFT JOIN crm_customer_order AS o ON c.customer_id = o.customer_id AND c.extend_id = o.py_extend_id
            AND DATE_ADD(c.update_time,INTERVAL 45 DAY) > o.order_time AND c.update_time &lt; o.order_time
        <where>
            <if test="ids != null and ids.size > 0">
                AND c.extend_id IN
                <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="beginTime != null">
                AND order_time >= #{beginTime}
            </if>
            <if test="endTime != null">
                AND order_time &lt; #{endTime}
            </if>
        </where>
        GROUP BY py_extend_id,DATE(order_time)
    </select>

    <select id="selVipOrderDayNewMoney" resultType="com.yooa.crm.api.domain.CrmCustomerOrder">
        SELECT
            DATE(o.order_time) AS order_time,SUM(o.order_money) AS order_money,o.serve_id
        FROM crm_customer AS c
        LEFT JOIN crm_customer_order AS o ON c.customer_id = o.customer_id AND c.serve_id = o.serve_id
            AND DATE_ADD(c.update_time,INTERVAL 45 DAY) > o.order_time AND c.update_time &lt; o.order_time
        <where>
            <if test="ids != null and ids.size > 0">
                AND c.extend_id IN
                <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="beginTime != null">
                AND order_time >= #{beginTime}
            </if>
            <if test="endTime != null">
                AND order_time &lt; #{endTime}
            </if>
        </where>
        GROUP BY serve_id,DATE(order_time)
    </select>

    <!--查询充值退款信息-->
    <select id="getChargeRefundData" resultType="com.yooa.crm.api.domain.vo.CustomerRefundRecordVo">
        SELECT
          co.customer_id AS customerId,
          GROUP_CONCAT(co.pd_third_order_no) thirdOrderNo,
          GROUP_CONCAT(co.payment_type) paymentType
        FROM
        crm_customer_charge_refund acr
              LEFT JOIN crm_customer_order co  ON co.order_id = acr.order_id
        WHERE
            acr.status = '1'
            <if test="customerIds !=null and customerIds.size > 0">
                and  co.customer_id IN
                <foreach collection="customerIds"  item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
    </select>

    <select id="getCustomerReward" resultType="com.yooa.crm.api.domain.vo.CustomerRewardInfoVo">
        SELECT
            cr.customer_id customerId,
            a.anchor_id anchorId,
            cr.add_time rewardTime,
            cr.total_amount rewardAmt,
            cr.action,
            g.gift_name giftName,
            a.anchor_name anchorName
        FROM
            crm_customer_reward cr
                LEFT JOIN crm_anchor a on a.anchor_id = cr.anchor_id
                LEFT JOIN crm_gift g on g.gift_id = cr.gift_id
        WHERE
            cr.customer_id in
        <foreach collection="customerIdList" index="index" item="item" open="("
                 separator="," close=")">
           #{item}
        </foreach>
        <if test="customerRewardQuery.anchorAccountId != null ">
            AND a.anchor_id = #{customerRewardQuery.anchorAccountId}
        </if>
        <if test="customerRewardQuery.minRewardAmount != null">
            AND cr.total_amount &gt;=  #{customerRewardQuery.minRewardAmount}
        </if>
        <if test="customerRewardQuery.maxRewardAmount != null ">
            AND cr.total_amount &lt;= #{customerRewardQuery.maxRewardAmount}
        </if>
        <if test="customerRewardQuery.startTime != null and customerRewardQuery.endTime != null ">
            AND cr.add_time BETWEEN #{customerRewardQuery.startTime} AND DATE_ADD(#{customerRewardQuery.endTime},INTERVAL 1 DAY) - INTERVAL 1 SECOND
        </if>
        <if test="customerRewardQuery.queryId != null and customerRewardQuery.queryId != ''">
            AND (a.anchor_id = #{customerRewardQuery.queryId}
                OR a.anchor_name = #{customerRewardQuery.queryId})
        </if>

        ORDER BY cr.add_time DESC
    </select>

    <select id="getCustomerRewardCollect" resultType="com.yooa.crm.api.domain.vo.CustomerRewardCollectVo">
        SELECT
            COALESCE(SUM(CASE WHEN DATE(add_time) = DATE(NOW()) THEN total_amount END), 0) AS todayRewardAmt,
            COALESCE(SUM(CASE WHEN DATE_FORMAT(add_time, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m') THEN total_amount END), 0) AS monthRewardAmt,
            COALESCE(SUM(total_amount), 0) AS totalRewardAmt
        FROM crm_customer_reward
        WHERE customer_id in
        <foreach collection="customerId" index="index" item="item" open="("
                 separator="," close=")">
        #{item}
        </foreach>
    </select>

    <select id="getRewardCollect" resultType="com.yooa.crm.api.domain.vo.CustomerRewardCollectVo">
        SELECT
            COALESCE(SUM(CASE WHEN DATE(add_time) = DATE(NOW()) THEN total_amount END), 0) AS todayRewardAmt,
            COALESCE(SUM(CASE WHEN DATE_FORMAT(add_time, '%Y-%m') = DATE_FORMAT(CURRENT_DATE, '%Y-%m') THEN total_amount END), 0) AS monthRewardAmt,
            COALESCE(SUM(total_amount), 0) AS totalRewardAmt
        FROM crm_customer_reward
    </select>
    <select id="getRewardRecordList" resultType="com.yooa.crm.api.domain.vo.CustomerRewardInfoVo">
        SELECT
        cr.customer_id customerId,
        a.anchor_id anchorId,
        cr.add_time rewardTime,
        cr.total_amount rewardAmt,
        cr.action,
        g.gift_name giftName,
        c.customer_name customerName,
        a.anchor_name anchorName
        FROM
        crm_customer_reward cr
        LEFT JOIN crm_anchor a on a.anchor_id = cr.anchor_id
        LEFT JOIN crm_customer c on cr.customer_id = c.customer_id
        LEFT JOIN crm_gift g on g.gift_id = cr.gift_id
        LEFT JOIN yooa_system.sys_user_pd up on up.pd_user_id = cr.operate_id
        LEFT JOIN yooa_system.sys_user u on up.user_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d on d.dept_id = u.dept_id
        WHERE
        1 = 1
        ${query.params.dataScope}
        <if test="query.anchorAccountId != null ">
            AND a.anchor_id = #{query.anchorAccountId}
        </if>
        <if test="query.minRewardAmount != null">
            AND cr.total_amount &gt;=  #{query.minRewardAmount}
        </if>
        <if test="query.maxRewardAmount != null ">
            AND cr.total_amount &lt;= #{query.maxRewardAmount}
        </if>
        <if test="query.startTime != null and query.endTime != null ">
            AND cr.add_time BETWEEN #{query.startTime} AND DATE_ADD(#{query.endTime},INTERVAL 1 DAY) - INTERVAL 1 SECOND
        </if>
        <if test="query.queryId != null and query.queryId != ''">
            AND (a.anchor_name LIKE CONCAT ('%',#{query.queryId},'%')
                OR a.anchor_id = #{query.queryId}
                OR cr.customer_id = #{query.queryId}
                OR c.customer_name LIKE CONCAT('%', #{query.queryId}, '%')
            )
        </if>
        <if test="query.action != null and query.action != '' ">
            AND cr.action = #{query.action}
        </if>
        ORDER BY cr.add_time DESC
    </select>

    <select id="calculationExtendTotalMoney" resultType="java.math.BigDecimal">
        SELECT
            SUM(order_money) AS money
        FROM crm_customer_order o
        WHERE
            o.order_status = 1
            <if test="customerIds !=null and customerIds.size > 0">
                AND o.customer_id IN
                <foreach collection="customerIds"  item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="extendIds !=null and extendIds.size > 0">
                AND o.py_extend_id IN
                <foreach collection="extendIds"  item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
    </select>

    <select id="calculationVipTotalMoney" resultType="java.math.BigDecimal">
        SELECT
            SUM(order_money) AS money
        FROM crm_customer_order o
        WHERE
        o.order_status = 1
        <if test="customerIds !=null and customerIds.size > 0">
            AND o.customer_id IN
            <foreach collection="customerIds"  item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="vipIds !=null and vipIds.size > 0">
            AND o.py_serve_id IN
            <foreach collection="vipIds"  item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getRewardRecordCount" resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM
        crm_customer_reward cr
        LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cr.operate_id
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON d.dept_id = u.dept_id
        LEFT JOIN crm_customer c ON cr.customer_id = c.customer_id
        LEFT JOIN crm_anchor a ON a.anchor_id = cr.anchor_id
        WHERE
        1 = 1
        ${query.params.dataScope}
        <if test="query.anchorAccountId != null ">
            AND a.anchor_id = #{query.anchorAccountId}
        </if>
        <if test="query.minRewardAmount != null">
            AND cr.total_amount &gt;=  #{query.minRewardAmount}
        </if>
        <if test="query.maxRewardAmount != null ">
            AND cr.total_amount &lt;= #{query.maxRewardAmount}
        </if>
        <if test="query.startTime != null and query.endTime != null ">
            AND cr.add_time BETWEEN #{query.startTime} AND DATE_ADD(#{query.endTime},INTERVAL 1 DAY) - INTERVAL 1 SECOND
        </if>
        <if test="query.queryId != null and query.queryId != ''">
            AND (a.anchor_name LIKE CONCAT ('%',#{query.queryId},'%')
            OR a.anchor_id = #{query.queryId}
            OR cr.customer_id = #{query.queryId}
            OR c.customer_name LIKE CONCAT('%', #{query.queryId}, '%')
            )
        </if>
        <if test="query.action != null and query.action != '' ">
            AND cr.action = #{query.action}
        </if>
    </select>

    <select id="getRewardDetailByIds" resultType="com.yooa.crm.api.domain.vo.CustomerRewardInfoVo">
        SELECT
        cr.customer_id customerId,
        a.anchor_id anchorId,
        cr.add_time rewardTime,
        cr.total_amount rewardAmt,
        cr.action,
        g.gift_name giftName,
        c.customer_name customerName,
        a.anchor_name anchorName
        FROM crm_customer_reward cr
        LEFT JOIN crm_anchor a ON a.anchor_id = cr.anchor_id
        LEFT JOIN crm_customer c ON c.customer_id = cr.customer_id
        LEFT JOIN crm_gift g ON g.gift_id = cr.gift_id
        LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cr.operate_id
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON d.dept_id = u.dept_id
        WHERE cr.id IN
        <foreach collection="ids" index="index" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="getPagedRewardIds" resultType="java.lang.Long">
        SELECT cr.id
        FROM crm_customer_reward cr
        LEFT JOIN yooa_system.sys_user_pd up ON up.pd_user_id = cr.operate_id
        LEFT JOIN yooa_system.sys_user u ON up.user_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON d.dept_id = u.dept_id
        WHERE 1 = 1
        ${query.params.dataScope}
        <if test="query.anchorAccountId != null">
            AND cr.anchor_id = #{query.anchorAccountId}
        </if>
        <if test="query.minRewardAmount != null">
            AND cr.total_amount &gt;= #{query.minRewardAmount}
        </if>
        <if test="query.maxRewardAmount != null">
            AND cr.total_amount &lt;= #{query.maxRewardAmount}
        </if>
        <if test="query.startTime != null and query.endTime != null">
            AND cr.add_time BETWEEN #{query.startTime} AND DATE_ADD(#{query.endTime}, INTERVAL 1 DAY) - INTERVAL 1 SECOND
        </if>
        <if test="query.queryId != null and query.queryId != ''">
            AND ( (cr.anchor_id = #{query.queryId} AND cr.anchor_id != '0')
            OR  ( cr.customer_id = #{query.queryId} AND cr.customer_id != '0')
            <if test="query.customerIdList != null and query.customerIdList.size() > 0">
                OR cr.customer_id IN
                <foreach collection="query.customerIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            <if test="query.anchorIdList != null and query.anchorIdList.size() > 0">
                OR cr.anchor_id IN
                <foreach collection="query.anchorIdList" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
            )
        </if>

        <if test="query.action != null and query.action != '' ">
            AND cr.action = #{query.action}
        </if>
        ORDER BY cr.add_time DESC

    </select>
    <select id="selRechargeRecordPitcher" resultType="com.yooa.crm.api.domain.vo.RechargeRecordPitcherVo">
        select co.*,
               cc.customer_name customerName,
               cc.customer_id customerId,
               cc.customer_account customerAccount,
               exu.nick_name extendName,
               vipu.nick_name vipName,
               u.nick_name pitcherName
        from crm_customer_order co
        LEFT JOIN crm_customer cc ON co.customer_id = cc.customer_id
        LEFT JOIN yooa_system.sys_user exu ON co.extend_id = exu.user_id
        LEFT JOIN yooa_system.sys_user vipu ON co.serve_id = vipu.user_id
        LEFT JOIN yooa_system.sys_user u ON co.pitcher_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        <if test="query.type != null and query.type == 1">
            JOIN (
            SELECT customer_id, MIN(order_time) AS min_order_time
            FROM crm_customer_order
            GROUP BY customer_id
            ) o1
            ON co.customer_id = o1.customer_id AND co.order_time = o1.min_order_time
        </if>
        <where>
            1 = 1
            <if test="query.searchBox != null and query.searchBox != ''">
                AND (co.customer_id = #{query.searchBox}
                OR cc.customer_name LIKE CONCAT('%',#{query.searchBox})
                OR co.pd_order_no LIKE CONCAT('%',#{query.searchBox},'%')
                OR u.nick_name LIKE CONCAT('%',#{query.searchBox},'%')
                )
            </if>
            <if test="query.extendDeptIds != null and query.extendDeptIds.size > 0">
                AND co.extend_dept_id IN
                <foreach collection="query.extendDeptIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.extendDeptIds == null and query.extendDeptId != null and query.extendDeptId != ''">
                AND co.extend_dept_id = #{query.extendDeptId}
            </if>
            <if test="query.appProject != null and query.appProject != ''">
                AND co.app_project = #{query.appProject}
            </if>
        <if test="query.pitcherDeptIds != null and query.pitcherDeptIds.size > 0">
            AND co.pitcher_dept_id IN
            <foreach collection="query.pitcherDeptIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.pitcherDeptIds == null and query.pitcherDeptId != null and query.pitcherDeptId != ''">
            AND co.pitcher_dept_id = #{query.pitcherDeptId}
        </if>
        <if test="query.startOrderTime != null and query.endOrderTime != null">
            AND co.order_time BETWEEN #{query.startOrderTime} AND #{query.endOrderTime}
        </if>

        <if test="query.orderStatus != null and query.orderStatus != ''">
            AND co.order_status = #{query.orderStatus}
        </if>
        <if test="query.paymentType != null and query.paymentType != ''">
            AND co.payment_type = #{query.paymentType}
        </if>
            <if test="query.startCustomerTime != null and query.endCustomerTime != null">
                AND cc.create_time BETWEEN #{query.startCustomerTime} AND #{query.endCustomerTime}
            </if>
            ${query.params.dataScope}
            <if test="query.type != null and query.type == 2">
                AND co.order_time >= DATE_SUB(NOW(), INTERVAL 45 DAY)
            </if>
        </where>
        ORDER BY co.order_time DESC
    </select>
    <select id="selRechargeRecordPitcherStatistics" resultType="java.util.Map">
        select sum(co.order_money) orderMoneySum
        from crm_customer_order co
        LEFT JOIN crm_customer cc ON co.customer_id = cc.customer_id
        LEFT JOIN yooa_system.sys_user exu ON co.extend_id = exu.user_id
        LEFT JOIN yooa_system.sys_user vipu ON co.serve_id = vipu.user_id
        LEFT JOIN yooa_system.sys_user u ON co.pitcher_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        <if test="query.type != null and query.type == 1">
            JOIN (
            SELECT customer_id, MIN(order_time) AS min_order_time
            FROM crm_customer_order
            GROUP BY customer_id
            ) o1
            ON co.customer_id = o1.customer_id AND co.order_time = o1.min_order_time
        </if>
        <where>
            1 = 1
            <if test="query.searchBox != null and query.searchBox != ''">
                AND (co.customer_id = #{query.searchBox}
                OR cc.customer_name LIKE CONCAT('%',#{query.searchBox})
                OR co.pd_order_no LIKE CONCAT('%',#{query.searchBox},'%')
                OR u.nick_name LIKE CONCAT('%',#{query.searchBox},'%')
                )
            </if>
            <if test="query.extendDeptIds != null and query.extendDeptIds.size > 0">
                AND co.extend_dept_id IN
                <foreach collection="query.extendDeptIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.extendDeptIds == null and query.extendDeptId != null and query.extendDeptId != ''">
                AND co.extend_dept_id = #{query.extendDeptId}
            </if>
            <if test="query.appProject != null and query.appProject != ''">
                AND co.app_project = #{query.appProject}
            </if>
            <if test="query.pitcherDeptIds != null and query.pitcherDeptIds.size > 0">
                AND co.pitcher_dept_id IN
                <foreach collection="query.pitcherDeptIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.pitcherDeptIds == null and query.pitcherDeptId != null and query.pitcherDeptId != ''">
                AND co.pitcher_dept_id = #{query.pitcherDeptId}
            </if>
            <if test="query.startOrderTime != null and query.endOrderTime != null">
                AND co.order_time BETWEEN #{query.startOrderTime} AND #{query.endOrderTime}
            </if>
            <if test="query.orderStatus != null and query.orderStatus != ''">
                AND co.order_status = #{query.orderStatus}
            </if>
            <if test="query.paymentType != null and query.paymentType != ''">
                AND co.payment_type = #{query.paymentType}
            </if>
            <if test="query.startCustomerTime != null and query.endCustomerTime != null">
                AND cc.create_time BETWEEN #{query.startCustomerTime} AND #{query.endCustomerTime}
            </if>
            ${query.params.dataScope}

            <if test="query.type != null and query.type == 2">
                AND co.order_time >= DATE_SUB(NOW(), INTERVAL 45 DAY)
            </if>
        </where>
        ORDER BY co.order_time DESC
    </select>

    <!-- 运营 - 充值记录 -->
    <select id="selRechargeRecordPitcherForOperate" resultType="com.yooa.crm.api.domain.vo.RechargeRecordPitcherVo">
        select co.*,
               cc.customer_name customerName,
               cc.customer_id customerId,
               cc.customer_account customerAccount,
               exu.nick_name extendName,
               vipu.nick_name vipName,
               u.nick_name pitcherName,
               opu.nick_name operateName
        from crm_customer_order co
        LEFT JOIN crm_customer cc ON co.customer_id = cc.customer_id
        LEFT JOIN yooa_system.sys_user exu ON co.extend_id = exu.user_id
        LEFT JOIN yooa_system.sys_user vipu ON co.serve_id = vipu.user_id
        LEFT JOIN yooa_system.sys_user u ON co.pitcher_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        INNER JOIN crm_customer_join_anchor cja ON co.customer_id = cja.customer_id
        INNER JOIN yooa_system.sys_user_pd sup ON cja.extend_id = sup.pd_user_id
        INNER JOIN yooa_system.sys_user opu ON sup.user_id = opu.user_id
        INNER JOIN yooa_system.sys_dept opd ON opu.dept_id = opd.dept_id
        <if test="query.type != null and query.type == 1">
            JOIN (
            SELECT customer_id, MIN(order_time) AS min_order_time
            FROM crm_customer_order
            GROUP BY customer_id
            ) o1
            ON co.customer_id = o1.customer_id AND co.order_time = o1.min_order_time
        </if>
        <where>
            1 = 1
            AND cja.status = '1'
            <if test="query.searchBox != null and query.searchBox != ''">
                AND (co.customer_id = #{query.searchBox}
                OR cc.customer_name LIKE CONCAT('%',#{query.searchBox})
                OR co.pd_order_no LIKE CONCAT('%',#{query.searchBox},'%')
                OR u.nick_name LIKE CONCAT('%',#{query.searchBox},'%')
                )
            </if>
            <if test="query.extendDeptIds != null and query.extendDeptIds.size > 0">
                AND co.extend_dept_id IN
                <foreach collection="query.extendDeptIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.operateDeptIds != null and query.operateDeptIds.size > 0">
                AND opd.dept_id IN
                <foreach collection="query.operateDeptIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.extendDeptIds == null and query.extendDeptId != null and query.extendDeptId != ''">
                AND co.extend_dept_id = #{query.extendDeptId}
            </if>
            <if test="query.appProject != null and query.appProject != ''">
                AND co.app_project = #{query.appProject}
            </if>
        <if test="query.pitcherDeptIds != null and query.pitcherDeptIds.size > 0">
            AND co.pitcher_dept_id IN
            <foreach collection="query.pitcherDeptIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.pitcherDeptIds == null and query.pitcherDeptId != null and query.pitcherDeptId != ''">
            AND co.pitcher_dept_id = #{query.pitcherDeptId}
        </if>
        <if test="query.startOrderTime != null and query.endOrderTime != null">
            AND co.order_time BETWEEN #{query.startOrderTime} AND #{query.endOrderTime}
        </if>

        <if test="query.orderStatus != null and query.orderStatus != ''">
            AND co.order_status = #{query.orderStatus}
        </if>
        <if test="query.paymentType != null and query.paymentType != ''">
            AND co.payment_type = #{query.paymentType}
        </if>
            <if test="query.startCustomerTime != null and query.endCustomerTime != null">
                AND cc.create_time BETWEEN #{query.startCustomerTime} AND #{query.endCustomerTime}
            </if>
            ${query.params.dataScope}
            <if test="query.type != null and query.type == 2">
                AND co.order_time >= DATE_SUB(NOW(), INTERVAL 45 DAY)
            </if>
        </where>
        ORDER BY co.order_time DESC
    </select>

    <!-- 运营 - 充值记录 - 统计 -->
    <select id="selRechargeRecordPitcherStatisticsForOperate" resultType="java.util.Map">
        select sum(co.order_money) orderMoneySum
        from crm_customer_order co
        LEFT JOIN crm_customer cc ON co.customer_id = cc.customer_id
        LEFT JOIN yooa_system.sys_user exu ON co.extend_id = exu.user_id
        LEFT JOIN yooa_system.sys_user vipu ON co.serve_id = vipu.user_id
        LEFT JOIN yooa_system.sys_user u ON co.pitcher_id = u.user_id
        LEFT JOIN yooa_system.sys_dept d ON u.dept_id = d.dept_id
        INNER JOIN crm_customer_join_anchor cja ON co.customer_id = cja.customer_id
        INNER JOIN yooa_system.sys_user_pd sup ON cja.extend_id = sup.pd_user_id
        INNER JOIN yooa_system.sys_user opu ON sup.user_id = opu.user_id
        INNER JOIN yooa_system.sys_dept opd ON opu.dept_id = opd.dept_id
        <if test="query.type != null and query.type == 1">
            JOIN (
            SELECT customer_id, MIN(order_time) AS min_order_time
            FROM crm_customer_order
            GROUP BY customer_id
            ) o1
            ON co.customer_id = o1.customer_id AND co.order_time = o1.min_order_time
        </if>
        <where>
            1 = 1
            AND cja.status = '1'
            <if test="query.searchBox != null and query.searchBox != ''">
                AND (co.customer_id = #{query.searchBox}
                OR cc.customer_name LIKE CONCAT('%',#{query.searchBox})
                OR co.pd_order_no LIKE CONCAT('%',#{query.searchBox},'%')
                OR u.nick_name LIKE CONCAT('%',#{query.searchBox},'%')
                )
            </if>
            <if test="query.extendDeptIds != null and query.extendDeptIds.size > 0">
                AND co.extend_dept_id IN
                <foreach collection="query.extendDeptIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.operateDeptIds != null and query.operateDeptIds.size > 0">
                AND opd.dept_id IN
                <foreach collection="query.operateDeptIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="query.extendDeptIds == null and query.extendDeptId != null and query.extendDeptId != ''">
                AND co.extend_dept_id = #{query.extendDeptId}
            </if>
            <if test="query.appProject != null and query.appProject != ''">
                AND co.app_project = #{query.appProject}
            </if>
        <if test="query.pitcherDeptIds != null and query.pitcherDeptIds.size > 0">
            AND co.pitcher_dept_id IN
            <foreach collection="query.pitcherDeptIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="query.pitcherDeptIds == null and query.pitcherDeptId != null and query.pitcherDeptId != ''">
            AND co.pitcher_dept_id = #{query.pitcherDeptId}
        </if>
        <if test="query.startOrderTime != null and query.endOrderTime != null">
            AND co.order_time BETWEEN #{query.startOrderTime} AND #{query.endOrderTime}
        </if>

        <if test="query.orderStatus != null and query.orderStatus != ''">
            AND co.order_status = #{query.orderStatus}
        </if>
        <if test="query.paymentType != null and query.paymentType != ''">
            AND co.payment_type = #{query.paymentType}
        </if>
            <if test="query.startCustomerTime != null and query.endCustomerTime != null">
                AND cc.create_time BETWEEN #{query.startCustomerTime} AND #{query.endCustomerTime}
            </if>
            ${query.params.dataScope}
            <if test="query.type != null and query.type == 2">
                AND co.order_time >= DATE_SUB(NOW(), INTERVAL 45 DAY)
            </if>
        </where>
    </select>
</mapper>
