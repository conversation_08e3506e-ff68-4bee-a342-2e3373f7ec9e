package com.yooa.job.task.crm;

import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.crm.api.RemoteWebHookService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

/**
 *
 */
@AllArgsConstructor
@Component("webHookTask")
public class WebHookTask {

    private final RemoteWebHookService remoteWebHookService;

    /**
     * 拉取轮询聊天记录
     * extend
     */
    public void pullPollingChat() {
        remoteWebHookService.GetChatList(SecurityConstants.INNER);
    }

}
