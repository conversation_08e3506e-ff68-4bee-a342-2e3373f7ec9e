package com.yooa.job.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.job.domain.JobLog;
import com.yooa.job.mapper.JobLogMapper;
import com.yooa.job.service.JobLogService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * 定时任务调度日志信息 - 服务实现层
 */
@AllArgsConstructor
@Service
public class JobLogServiceImpl extends ServiceImpl<JobLogMapper, JobLog> implements JobLogService {

    @Override
    public void cleanJobLog() {
        baseMapper.cleanJobLog();
    }
}
