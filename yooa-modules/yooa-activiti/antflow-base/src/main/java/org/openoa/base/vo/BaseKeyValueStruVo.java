package org.openoa.base.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 0.0.5
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BaseKeyValueStruVo {
    //流程类型主键ID
    private Long id;
    private String key;
    private String value;
    private String type;
    private String remark;
    //部门ID
    private Long[] deptIds;
    //用户ID
    private Long[] userIds;
    private Date createTime;
    /**
     * 是否包含发起人自选模块,否为不包含,true为包含
     */
    private Boolean hasStarUserChooseModule=false;
}
