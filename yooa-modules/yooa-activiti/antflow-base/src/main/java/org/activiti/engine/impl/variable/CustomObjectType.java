/* Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * 
 *      http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.activiti.engine.impl.variable;


/**
 * Custom object type
 * 
 * <AUTHOR> Luna
 */
public class CustomObjectType implements VariableType {
  
  protected String typeName;
  protected Class<?> theClass;

  public CustomObjectType(String typeName, Class<?> theClass) {
    this.theClass = theClass;
    this.typeName = typeName;
  }
  
  public String getTypeName() {
    return this.typeName;
  }

  public Object getValue(ValueFields valueFields) {
    return valueFields.getCachedValue();
  }

  public boolean isAbleToStore(Object value) {
    if (value==null) {
      return true;
    }
    return this.theClass.isAssignableFrom(value.getClass());
  }

  public boolean isCachable() {
    return true;
  }

  public void setValue(Object value, ValueFields valueFields) {
    valueFields.setCachedValue(value);
  }
}
