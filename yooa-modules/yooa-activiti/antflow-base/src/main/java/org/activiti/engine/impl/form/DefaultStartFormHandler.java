/* Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * 
 *      http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.activiti.engine.impl.form;

import java.util.List;
import java.util.Map;

import org.activiti.bpmn.model.FormProperty;
import org.activiti.engine.form.StartFormData;
import org.activiti.engine.impl.persistence.entity.DeploymentEntity;
import org.activiti.engine.impl.persistence.entity.ExecutionEntity;
import org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.apache.commons.lang3.StringUtils;


/**
 * <AUTHOR>
 */
public class DefaultStartFormHandler extends DefaultFormHandler implements StartFormHandler {
  
  @Override
  public void parseConfiguration(List<FormProperty> formProperties, String formKey, DeploymentEntity deployment, ProcessDefinitionEntity processDefinition) {
    super.parseConfiguration(formProperties, formKey, deployment, processDefinition);
    if (StringUtils.isNotEmpty(formKey)) {
      processDefinition.setStartFormKey(true);
    }
  }

  public StartFormData createStartFormData(ProcessDefinitionEntity processDefinition) {
    StartFormDataImpl startFormData = new StartFormDataImpl();
    if (formKey != null) {
      startFormData.setFormKey(formKey.getExpressionText());
    }
    startFormData.setDeploymentId(deploymentId);
    startFormData.setProcessDefinition(processDefinition);
    initializeFormProperties(startFormData, null);
    return startFormData;
  }

  public ExecutionEntity submitStartFormData(ExecutionEntity processInstance, Map<String, String> properties) {
    submitFormProperties(properties, processInstance);
    return processInstance;
  }
}
