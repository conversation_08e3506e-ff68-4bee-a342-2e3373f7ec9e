/* Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * 
 *      http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.activiti.engine.impl.json;

import java.io.Reader;

import org.activiti.engine.ActivitiException;
import org.activiti.engine.impl.persistence.entity.TaskEntity;
import org.activiti.engine.impl.util.json.JSONObject;
import org.activiti.engine.task.Task;


/**
 * <AUTHOR>
 */
public class JsonTaskConverter extends JsonObjectConverter<Task> {

  public Task toObject(Reader reader) {
    throw new ActivitiException("not yet implemented");
  }

  public JSONObject toJsonObject(Task task) {
    TaskEntity taskEntity = (TaskEntity) task;
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("id", taskEntity.getId());
    jsonObject.put("dbversion", taskEntity.getRevision());
    jsonObject.put("assignee", taskEntity.getAssignee());
    jsonObject.put("name", taskEntity.getName());
    jsonObject.put("priority", taskEntity.getPriority());      
    jsonObject.put("createTime", taskEntity.getCreateTime());
    if (taskEntity.getExecutionId()!=null) {
      jsonObject.put("activityInstance", taskEntity.getExecutionId());
    }
    if (taskEntity.getProcessDefinitionId()!=null) {
      jsonObject.put("processDefinition", taskEntity.getProcessDefinitionId());
    }
    return jsonObject;
  }
}
