<?xml version="1.0" encoding="UTF-8" ?>

<!--
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~       http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.activiti.engine.impl.persistence.entity.HistoricVariableInstanceEntity">

  <!-- HISTORIC PROCESS VARIABLE INSERT -->
  
  <insert id="insertHistoricVariableInstance" parameterType="org.activiti.engine.impl.persistence.entity.HistoricVariableInstanceEntity">
    insert into ${prefix}ACT_HI_VARINST (ID_, PROC_INST_ID_, EXECUTION_ID_, TASK_ID_, NAME_, REV_, VAR_TYPE_, BYTEARRAY_ID_, DOUBLE_, LONG_ , TEXT_, TEXT2_, CREATE_TIME_, LAST_UPDATED_TIME_)
    values (
      #{id, jdbcType=VARCHAR},
      #{processInstanceId, jdbcType=VARCHAR},
      #{executionId, jdbcType=VARCHAR},
      #{taskId, jdbcType=VARCHAR},
      #{variableName, jdbcType=VARCHAR},
      #{revision, jdbcType=VARCHAR},
      #{variableType, jdbcType=VARCHAR},
      #{byteArrayRef, typeHandler=ByteArrayRefTypeHandler},
      #{doubleValue, jdbcType=DOUBLE},
      #{longValue, jdbcType=BIGINT},
      #{textValue, jdbcType=VARCHAR},
      #{textValue2, jdbcType=VARCHAR},
      #{createTime, jdbcType=TIMESTAMP},
      #{lastUpdatedTime, jdbcType=TIMESTAMP}
    )
  </insert>

  <insert id="bulkInsertHistoricVariableInstance" parameterType="java.util.List">
    insert into ${prefix}ACT_HI_VARINST (ID_, PROC_INST_ID_, EXECUTION_ID_, TASK_ID_, NAME_, REV_, VAR_TYPE_, BYTEARRAY_ID_, DOUBLE_, LONG_ , TEXT_, TEXT2_, CREATE_TIME_, LAST_UPDATED_TIME_)
    values 
      <foreach collection="list" item="historicVariable" index="index" separator=",">
        (#{historicVariable.id, jdbcType=VARCHAR},
         #{historicVariable.processInstanceId, jdbcType=VARCHAR},
         #{historicVariable.executionId, jdbcType=VARCHAR},
         #{historicVariable.taskId, jdbcType=VARCHAR},
         #{historicVariable.variableName, jdbcType=VARCHAR},
         #{historicVariable.revision, jdbcType=VARCHAR},
         #{historicVariable.variableType, jdbcType=VARCHAR},
         #{historicVariable.byteArrayRef, typeHandler=ByteArrayRefTypeHandler},
         #{historicVariable.doubleValue, jdbcType=DOUBLE},
         #{historicVariable.longValue, jdbcType=BIGINT},
         #{historicVariable.textValue, jdbcType=VARCHAR},
         #{historicVariable.textValue2, jdbcType=VARCHAR},
         #{historicVariable.createTime, jdbcType=TIMESTAMP},
         #{historicVariable.lastUpdatedTime, jdbcType=TIMESTAMP})
       </foreach>
  </insert>

  <insert id="bulkInsertHistoricVariableInstance_oracle" parameterType="java.util.List">
    INSERT ALL 
      <foreach collection="list" item="historicVariable" index="index">
      INTO ${prefix}ACT_HI_VARINST (ID_, PROC_INST_ID_, EXECUTION_ID_, TASK_ID_, NAME_, REV_,
      VAR_TYPE_, BYTEARRAY_ID_, DOUBLE_, LONG_ , TEXT_, TEXT2_, CREATE_TIME_, LAST_UPDATED_TIME_) VALUES 
          (#{historicVariable.id, jdbcType=VARCHAR},
           #{historicVariable.processInstanceId, jdbcType=VARCHAR},
           #{historicVariable.executionId, jdbcType=VARCHAR},
           #{historicVariable.taskId, jdbcType=VARCHAR},
           #{historicVariable.variableName, jdbcType=VARCHAR},
           #{historicVariable.revision, jdbcType=VARCHAR},
           #{historicVariable.variableType, jdbcType=VARCHAR},
           #{historicVariable.byteArrayRef, typeHandler=ByteArrayRefTypeHandler},
           #{historicVariable.doubleValue, jdbcType=DOUBLE},
           #{historicVariable.longValue, jdbcType=BIGINT},
           #{historicVariable.textValue, jdbcType=VARCHAR},
           #{historicVariable.textValue2, jdbcType=VARCHAR},
           #{historicVariable.createTime, jdbcType=TIMESTAMP},
           #{historicVariable.lastUpdatedTime, jdbcType=TIMESTAMP})
       </foreach>
    SELECT * FROM dual
  </insert>

  <!-- HISTORIC PROCESS VARIABLE UPDATE -->
  
  <update id="updateHistoricVariableInstance" parameterType="org.activiti.engine.impl.persistence.entity.HistoricVariableInstanceEntity">
    update ${prefix}ACT_HI_VARINST set
      EXECUTION_ID_ = #{executionId, jdbcType=VARCHAR},
      REV_ = #{revisionNext, jdbcType=INTEGER},
      BYTEARRAY_ID_ = #{byteArrayRef, typeHandler=ByteArrayRefTypeHandler},
      DOUBLE_ = #{doubleValue, jdbcType=DOUBLE},
      LONG_ = #{longValue, jdbcType=BIGINT},
      TEXT_ = #{textValue, jdbcType=VARCHAR},
      TEXT2_ = #{textValue2, jdbcType=VARCHAR},
      VAR_TYPE_ = #{variableType, jdbcType=VARCHAR},
      LAST_UPDATED_TIME_ = #{lastUpdatedTime, jdbcType=TIMESTAMP}
    where ID_ = #{id, jdbcType=VARCHAR}
      and REV_ = #{revision, jdbcType=INTEGER}
  </update>
  
  <!-- HISTORIC PROCESS VARIABLE DELETE -->

  <delete id="deleteHistoricVariableInstance" parameterType="org.activiti.engine.impl.persistence.entity.HistoricVariableInstanceEntity">
    delete from ${prefix}ACT_HI_VARINST where ID_ = #{id} and REV_ = #{revision}
  </delete>
  
  <delete id="bulkDeleteHistoricVariableInstance" parameterType="java.util.Collection">
    delete from ${prefix}ACT_HI_VARINST where 
    <foreach item="variable" collection="list" index="index" separator=" or ">
        ID_ = #{variable.id, jdbcType=VARCHAR}
    </foreach>
  </delete>
  
  <!-- HISTORIC PROCESS VARIABLE RESULTMAP -->
  <resultMap id="historicProcessVariableResultMap" type="org.activiti.engine.impl.persistence.entity.HistoricVariableInstanceEntity">
    <id property="id" column="ID_" jdbcType="VARCHAR" />
    <result property="processInstanceId" column="PROC_INST_ID_" jdbcType="VARCHAR" />
    <result property="executionId" column="EXECUTION_ID_" jdbcType="VARCHAR" />
    <result property="taskId" column="TASK_ID_" jdbcType="VARCHAR" />
    <result property="name" column="NAME_" javaType="String" jdbcType="VARCHAR" />
    <result property="revision" column="REV_" jdbcType="INTEGER" />
    <result property="variableType" column="VAR_TYPE_" javaType="org.activiti.engine.impl.variable.VariableType" jdbcType="VARCHAR"/>
    <result property="byteArrayRef" column="BYTEARRAY_ID_" typeHandler="ByteArrayRefTypeHandler"/>
    <result property="doubleValue" column="DOUBLE_" jdbcType="DOUBLE" />
    <result property="textValue" column="TEXT_" jdbcType="VARCHAR" />
    <result property="textValue2" column="TEXT2_" jdbcType="VARCHAR" />
    <result property="longValue" column="LONG_" jdbcType="BIGINT" />
    <result property="createTime" column="CREATE_TIME_" jdbcType="TIMESTAMP" />
    <result property="lastUpdatedTime" column="LAST_UPDATED_TIME_" jdbcType="TIMESTAMP" />
  </resultMap>
  
  <!-- HISTORIC VARIABLE SELECT -->

  <select id="selectHistoricVariableInstanceByQueryCriteria" parameterType="org.activiti.engine.impl.HistoricVariableInstanceQueryImpl" resultMap="historicProcessVariableResultMap">
  	${limitBefore}
    select RES.* ${limitBetween}
    <include refid="selectHistoricVariableInstanceByQueryCriteriaSql"/>
    ${orderBy}
    ${limitAfter}
  </select>
  
  <select id="selectHistoricVariableInstanceCountByQueryCriteria" parameterType="org.activiti.engine.impl.HistoricVariableInstanceQueryImpl" resultType="long">
    select count(RES.ID_)
    <include refid="selectHistoricVariableInstanceByQueryCriteriaSql"/>
  </select>
  
  <sql id="selectHistoricVariableInstanceByQueryCriteriaSql">
    from ${prefix}ACT_HI_VARINST RES
    <where>
      <if test="id != null">
        RES.ID_ = #{id}
      </if>
      <if test="processInstanceId != null">
        and RES.PROC_INST_ID_ = #{processInstanceId}
      </if>
      <if test="executionId != null">
        and RES.EXECUTION_ID_ = #{executionId}
      </if>
      <if test="executionIds != null and !executionIds.isEmpty()">
          and RES.EXECUTION_ID_ in
          <foreach item="item" index="index" collection="executionIds" open="(" separator="," close=")">
            #{item}
          </foreach>
      </if>
      <if test="taskId != null">
        and RES.TASK_ID_ = #{taskId}
      </if>
      <if test="taskIds != null and !taskIds.isEmpty()">
          and RES.TASK_ID_ in
          <foreach item="item" index="index" collection="taskIds" open="(" separator="," close=")">
            #{item}
          </foreach>
      </if>
      <if test="excludeTaskRelated">
        and RES.TASK_ID_ is NULL
      </if>
      <if test="variableName != null">
        and RES.NAME_ = #{variableName}
      </if>
      <if test="variableNameLike != null">
        and RES.NAME_ like #{variableNameLike}${wildcardEscapeClause}
      </if>
      
      <!-- PLEASE NOTE: If you change anything have a look into the Execution, the same query object is used there! -->
      <if test="queryVariableValue != null" >
        <if test="!queryVariableValue.type.equals('null')">
        <!-- When operator is not-equals or type of value is null, type doesn't matter! -->
          and RES.VAR_TYPE_ = #{queryVariableValue.type}
        </if>
        <if test="queryVariableValue.textValue != null &amp;&amp; queryVariableValue.longValue == null &amp;&amp; queryVariableValue.doubleValue == null">
        and RES.TEXT_
        <choose>
          <when test="queryVariableValue.operator.equals('LIKE') || queryVariableValue.operator.equals('LIKE_IGNORE_CASE')">LIKE</when>
          <otherwise><include refid="executionVariableOperator" /></otherwise>
        </choose>
          #{queryVariableValue.textValue}
          <choose>
            <when test="queryVariableValue.operator.equals('LIKE') || queryVariableValue.operator.equals('LIKE_IGNORE_CASE')">${wildcardEscapeClause}</when>
          </choose>
        </if>
        <if test="queryVariableValue.textValue2 != null">
        and RES.TEXT2_
        <choose>
          <when test="queryVariableValue.operator.equals('LIKE') || queryVariableValue.operator.equals('LIKE_IGNORE_CASE')">LIKE</when>
          <otherwise><include refid="executionVariableOperator" /></otherwise>
        </choose>
          #{queryVariableValue.textValue2}
          <choose>
            <when test="queryVariableValue.operator.equals('LIKE') || queryVariableValue.operator.equals('LIKE_IGNORE_CASE')">${wildcardEscapeClause}</when>
          </choose>
        </if>
        <if test="queryVariableValue.longValue != null">
        and RES.LONG_
        <include refid="executionVariableOperator" />
        #{queryVariableValue.longValue}
        </if>
        <if test="queryVariableValue.doubleValue != null">
        and RES.DOUBLE_
        <include refid="executionVariableOperator" />
        #{queryVariableValue.doubleValue}
        </if>
        <!-- Null variable type -->
        <if test="queryVariableValue.textValue == null &amp;&amp; queryVariableValue.textValue2 == null &amp;&amp; queryVariableValue.longValue == null &amp;&amp; queryVariableValue.doubleValue == null">
          <choose>
          <when test="queryVariableValue.operator.equals('NOT_EQUALS')">
            and (RES.TEXT_ is not null or RES.TEXT2_ is not null or RES.LONG_ is not null or RES.DOUBLE_ is not null or RES.BYTEARRAY_ID_ is not null)
          </when>
          <otherwise>
        and RES.TEXT_ is null and RES.TEXT2_ is null and RES.LONG_ is null and RES.DOUBLE_ is null and RES.BYTEARRAY_ID_ is null
          </otherwise>
        </choose>
        </if>
      </if>
    </where>
  </sql>

  <sql id="executionVariableOperator">
    <choose>
      <when test="queryVariableValue.operator.equals('EQUALS')">=</when>
      <when test="queryVariableValue.operator.equals('NOT_EQUALS')">&lt;&gt;</when>
      <when test="queryVariableValue.operator.equals('GREATER_THAN')">&gt;</when>
      <when test="queryVariableValue.operator.equals('GREATER_THAN_OR_EQUAL')">&gt;=</when>
      <when test="queryVariableValue.operator.equals('LESS_THAN')">&lt;</when>
      <when test="queryVariableValue.operator.equals('LESS_THAN_OR_EQUAL')">&lt;=</when>
   </choose>
  </sql>
  
  <select id="selectHistoricVariableInstanceByVariableInstanceId" resultMap="historicProcessVariableResultMap">
    select * from ${prefix}ACT_HI_VARINST where ID_ = #{variableInstanceId}
  </select>

  <select id="selectHistoricVariableInstanceByNativeQuery" parameterType="java.util.Map" resultMap="historicProcessVariableResultMap">
    <if test="resultType == 'LIST_PAGE'">
      ${limitBefore}
    </if>
    ${sql}
    <if test="resultType == 'LIST_PAGE'">
      ${limitAfter}
    </if>
  </select>

  <select id="selectHistoricVariableInstanceByNativeQuery_mssql_or_db2" parameterType="java.util.Map" resultMap="historicProcessVariableResultMap">
    <if test="resultType == 'LIST_PAGE'">
      ${limitBeforeNativeQuery}
    </if>
    ${sql}
    <if test="resultType == 'LIST_PAGE'">
      ${limitAfter}
    </if>
  </select>

  <select id="selectHistoricVariableInstanceCountByNativeQuery" parameterType="java.util.Map" resultType="long">
    ${sql}
  </select>
  
</mapper>
