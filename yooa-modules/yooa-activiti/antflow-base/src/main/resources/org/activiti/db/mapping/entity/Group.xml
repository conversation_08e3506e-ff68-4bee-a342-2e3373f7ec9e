<?xml version="1.0" encoding="UTF-8" ?> 

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd"> 
  
<mapper namespace="org.activiti.engine.impl.persistence.entity.GroupEntity">

  <!-- GROUP INSERT -->

  <insert id="insertGroup" parameterType="org.activiti.engine.impl.persistence.entity.GroupEntity">
    insert into ${prefix}ACT_ID_GROUP (ID_, REV_, NAME_, TYPE_)
    values (
      #{id ,jdbcType=VARCHAR},
      1, 
      #{name ,jdbcType=VARCHAR},
      #{type ,jdbcType=VARCHAR}
    )
  </insert>

  <insert id="bulkInsertGroup" parameterType="java.util.List">
    insert into ${prefix}ACT_ID_GROUP (ID_, REV_, NAME_, TYPE_)
    values 
      <foreach collection="list" item="group" index="index" separator=",">
        (#{group.id ,jdbcType=VARCHAR},
         1,
         #{group.name ,jdbcType=VARCHAR},
         #{group.type ,jdbcType=VARCHAR})
      </foreach>
  </insert>

  <insert id="bulkInsertGroup_oracle" parameterType="java.util.List">
    INSERT ALL 
      <foreach collection="list" item="group" index="index">
        INTO ${prefix}ACT_ID_GROUP (ID_, REV_, NAME_, TYPE_) VALUES 
            (#{group.id ,jdbcType=VARCHAR},
             1,
             #{group.name ,jdbcType=VARCHAR},
             #{group.type ,jdbcType=VARCHAR})
      </foreach>
    SELECT * FROM dual
  </insert>

  <!-- GROUP UPDATE -->
  
  <update id="updateGroup" parameterType="org.activiti.engine.impl.persistence.entity.GroupEntity">
    update ${prefix}ACT_ID_GROUP set
      REV_ = #{revisionNext ,jdbcType=INTEGER},
      NAME_ = #{name ,jdbcType=VARCHAR},
      TYPE_ = #{type ,jdbcType=VARCHAR}
    where ID_ = #{id}
       and REV_ = #{revision}
  </update>
  
  <!-- GROUP DELETE -->

  <delete id="deleteGroup" parameterType="org.activiti.engine.impl.persistence.entity.GroupEntity">
    delete from ${prefix}ACT_ID_GROUP where ID_ = #{id} and REV_ = #{revision} 
  </delete>

  <!-- GROUP RESULTMAP -->

  <resultMap id="groupResultMap" type="org.activiti.engine.impl.persistence.entity.GroupEntity">
    <id property="id" column="ID_" jdbcType="VARCHAR" />
    <result property="revision" column="REV_" jdbcType="INTEGER" />
    <result property="name" column="NAME_" jdbcType="VARCHAR" />
    <result property="type" column="TYPE_" jdbcType="VARCHAR" />
  </resultMap>
  
  <!-- GROUP SELECT -->

  <select id="selectGroup" parameterType="string" resultMap="groupResultMap">
    select * from ${prefix}ACT_ID_GROUP where ID_ = #{id}
  </select>
    
  <select id="selectGroupsByUserId" parameterType="org.activiti.engine.impl.db.ListQueryParameterObject" resultMap="groupResultMap">
    select g.*
    from ${prefix}ACT_ID_GROUP g, ${prefix}ACT_ID_MEMBERSHIP membership
    where g.ID_ = membership.GROUP_ID_
      and membership.USER_ID_ = #{parameter}
  </select>

  <select id="selectGroupsByUserIdAndGroupType" parameterType="org.activiti.engine.impl.db.ListQueryParameterObject" resultMap="groupResultMap">
    select g.*
    from ${prefix}ACT_ID_GROUP g, ${prefix}ACT_ID_MEMBERSHIP membership
    where g.ID_ = membership.GROUP_ID_
      and membership.USER_ID_ = #{parameter.userId}
      <if test="groupType != null">
         and g.TYPE_ = #{parameter.groupType}
      </if>
  </select>
  
  <select id="selectGroupByQueryCriteria" parameterType="org.activiti.engine.impl.GroupQueryImpl" resultMap="groupResultMap">
  	${limitBefore}
    select RES.* ${limitBetween}
    <include refid="selectGroupByQueryCriteriaSql" />
    ${orderBy}
    ${limitAfter}
  </select>
  
   <select id="selectGroupCountByQueryCriteria" parameterType="org.activiti.engine.impl.GroupQueryImpl" resultType="long">
    select count(RES.ID_)
    <include refid="selectGroupByQueryCriteriaSql" />
  </select>
  
  <sql id="selectGroupByQueryCriteriaSql">
    from ${prefix}ACT_ID_GROUP RES
    <if test="userId != null">
      inner join ${prefix}ACT_ID_MEMBERSHIP M on RES.ID_ = M.GROUP_ID_
      inner join ${prefix}ACT_ID_USER U on M.USER_ID_ = U.ID_
    </if>
    <where>
      <if test="id != null">
        RES.ID_ = #{id}
      </if>
      <if test="name != null">
        and RES.NAME_ = #{name}
      </if>
      <if test="nameLike != null">
        and RES.NAME_ like #{nameLike}${wildcardEscapeClause}
      </if>
      <if test="type != null">
        and RES.TYPE_ = #{type}
      </if>
      <if test="userId != null">
        and U.ID_ = #{userId}
      </if>
      <if test="procDefId != null">
        and exists (select ID_ from ${prefix}ACT_RU_IDENTITYLINK where PROC_DEF_ID_ = #{procDefId} and GROUP_ID_=RES.ID_ )
      </if>
      
    </where>
  </sql>

  <select id="selectGroupByNativeQuery" parameterType="java.util.Map" resultMap="groupResultMap">
    <if test="resultType == 'LIST_PAGE'">
      ${limitBefore}
    </if>
    ${sql}
    <if test="resultType == 'LIST_PAGE'">
      ${limitAfter}
    </if>
  </select>

  <select id="selectGroupByNativeQuery_mssql_or_db2" parameterType="java.util.Map" resultMap="groupResultMap">
    <if test="resultType == 'LIST_PAGE'">
      ${limitBeforeNativeQuery}
    </if>
    ${sql}
    <if test="resultType == 'LIST_PAGE'">
      ${limitAfter}
    </if>
  </select>

  <select id="selectGroupCountByNativeQuery" parameterType="java.util.Map" resultType="long">
    ${sql}
  </select>
  
</mapper>