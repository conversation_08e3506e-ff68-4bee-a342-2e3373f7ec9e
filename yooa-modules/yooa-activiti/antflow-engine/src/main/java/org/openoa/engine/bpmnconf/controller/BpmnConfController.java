package org.openoa.engine.bpmnconf.controller;

import com.alibaba.fastjson2.JSONObject;

import com.yooa.common.core.domain.R;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.security.annotation.InnerAuth;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.system.api.domain.SysDept;
import com.yooa.system.api.domain.query.DeptQuery;
import com.yooa.system.api.domain.vo.SysProcessBacklogVo;
import com.yooa.system.api.domain.vo.TaskMgmtDaVO;
import lombok.extern.slf4j.Slf4j;
import org.openoa.base.entity.Result;
import org.openoa.base.vo.*;
import org.openoa.engine.bpmnconf.confentity.BpmnOutsideConf;
import org.openoa.engine.bpmnconf.mapper.BpmnConfMapper;
import org.openoa.engine.bpmnconf.mapper.ProcessApprovalMapper;
import org.openoa.engine.bpmnconf.service.biz.BpmVerifyInfoBizServiceImpl;
import org.openoa.engine.bpmnconf.service.biz.BpmnConfCommonServiceImpl;
import org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl;
import org.openoa.engine.bpmnconf.service.impl.BpmnNodeServiceImpl;
import org.openoa.engine.bpmnconf.service.impl.BpmnNodeToServiceImpl;
import org.openoa.base.interf.ActivitiServiceAnno;
import org.openoa.base.dto.PageDto;
import org.openoa.base.interf.ActivitiService;

import org.openoa.base.exception.JiMuBizException;
import org.openoa.engine.bpmnconf.service.biz.ProcessApprovalServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.yooa.common.core.web.domain.AjaxResult.success;

/**
 * @Classname BpmnConfController
 * @Description TODO
 * @Date 2022-02-19 16:21
 * @Created by AntOffice
 */

@Slf4j
@RestController
@RequestMapping(value = "/bpmnConf")
public class BpmnConfController {
    @Autowired
    private BpmnConfServiceImpl bpmnConfService;
    @Autowired
    private BpmnConfMapper bpmnConfMapper;
    @Autowired
    private BpmnNodeToServiceImpl bpmnNodeToService;
    @Autowired
    private ProcessApprovalServiceImpl processApprovalService;

    @Autowired
    private BpmnConfCommonServiceImpl bpmnConfCommonService;
    @Autowired
    private BpmVerifyInfoBizServiceImpl bpmVerifyInfoBizService;
    @Autowired(required = false)
    private Map<String, ActivitiService> activitiServices;

    @Autowired
    private BpmnNodeServiceImpl testService;
    @Autowired
    private ProcessApprovalMapper processApprovalMapper;

    @GetMapping("/todoList")
    public AjaxResult todoList() {
        TaskMgmtVO taskMgmtVO = processApprovalService.processStatistics();
        return success(taskMgmtVO);
    }

    @PostMapping("/edit")
    public AjaxResult edit( @RequestBody BpmnConfVo bpmnConfVo) {
        bpmnConfService.edit(bpmnConfVo);
        return success("操作成功");
    }

    /**
     * 流程设计列表
     * @param dto
     * @return
     */
    @PostMapping("/listPage")
    public Result<ResultAndPage<BpmnConfVo>> listPage( @RequestBody ConfDetailRequestDto dto) {
        PageDto page = dto.getPageDto();
        BpmnConfVo vo = dto.getEntity();
        return Result.newSuccessResult(bpmnConfService.selectPage(page, vo));
    }

    /**
     * admin's preview
     */
    @PostMapping("/preview")
    public AjaxResult preview( @RequestBody String params) {
        bpmVerifyInfoBizService.getVerifyInfoList("DSFZH_WMA_21");
        return success(bpmnConfCommonService.previewNode(params));
    }

    /**
     * start page preview
     * 流程图预览
     */
    @PostMapping("/startPagePreviewNode")
    public AjaxResult startPagePreviewNode( @RequestBody String params) {
        JSONObject jsonObject = JSONObject.parseObject(params);
        Boolean isStartPreview = jsonObject.getBoolean("isStartPreview");

        if (isStartPreview == null || isStartPreview) {
            return success(bpmnConfCommonService.startPagePreviewNode(params));
        } else {
            return success(bpmnConfCommonService.taskPagePreviewNode(params));
        }

    }

    @GetMapping("/getBpmVerifyInfoVos")
    public Result<List<BpmVerifyInfoVo>> getBpmVerifyInfoVos( @RequestParam("processNumber") String processNumber) {
        return Result.newSuccessResult(bpmVerifyInfoBizService.getBpmVerifyInfoVos(processNumber, false));
    }

    /**
     * 表单信息
     * @param values
     * @param formCode
     * @return
     */
    @PostMapping("/process/viewBusinessProcess")
    public Result<BusinessDataVo> viewBusinessProcess( @RequestBody String values,  String formCode) {
        return Result.newSuccessResult(processApprovalService.getBusinessInfo(values, formCode));
    }

    /**
     *发起流程
     */
    @PostMapping("/process/buttonsOperation")
    public Result buttonsOperation( @RequestBody String values, String formCode) {
        BusinessDataVo resultData = processApprovalService.buttonsOperation(values, formCode);
        return Result.newSuccessResult(resultData);
    }

    /**
     * effective a config
     *
     * @param id
     * @return
     */
    @GetMapping("/effectiveBpmn/{id}")
    public Result effectiveBpmn(@PathVariable("id") Integer id) {
        bpmnConfService.effectiveBpmnConf(id);
        return Result.newSuccessResult(null);
    }

    @RequestMapping("/detail/{id}")
    public Result<BpmnConfVo> detail( @PathVariable("id") Integer id) {
        return Result.newSuccessResult(bpmnConfService.detail(id));
    }

    /**
     *流程设计删除
     */
    @PostMapping("/delete/{id}")
    public Result delete( @PathVariable("id") Integer id) {
        Result result=bpmnConfService.deleteById(id);
        return result;
    }

    /**
     * 任务中心
     * @param requestDto
     * @param type 3我的发起 5待办任务 4已办任务 9抄送到我
     * @return
     * @throws JiMuBizException
     */
    @RequestMapping("/process/listPage/{type}")
    public ResultAndPage<TaskMgmtVO> viewPcProcessList( @RequestBody DetailRequestDto requestDto, @PathVariable("type") Integer type) throws JiMuBizException {
        PageDto pageDto = requestDto.getPageDto();
        TaskMgmtVO taskMgmtVO = requestDto.getTaskMgmtVO();
        taskMgmtVO.setType(type);
        return processApprovalService.findPcProcessList(pageDto, taskMgmtVO);
    }

    /**
     * 我的发起不分页
     * @return
     * @throws JiMuBizException
     */
    @InnerAuth
    @PostMapping("/getTaskMgmtList")
    public R<List<TaskMgmtDaVO>> getTaskMgmtList(@RequestBody SysProcessBacklogVo query){
        TaskMgmtDaVO taskMgmtVO = new TaskMgmtDaVO();
        taskMgmtVO.setApplyUser(SecurityUtils.getUserId().toString());
        taskMgmtVO.setPlanTime(query.getPlanTime());
        taskMgmtVO.setState(query.getState());
        return processApprovalService.getInitiate(taskMgmtVO);
    }

    /**
     * 我的代办不分页
     * @return
     * @throws JiMuBizException
     */
    @InnerAuth
    @PostMapping("/getAllPcToDoList")
    public R<List<TaskMgmtDaVO>> getAllPcToDoList(@RequestBody SysProcessBacklogVo query){
        TaskMgmtDaVO taskMgmtVO = new TaskMgmtDaVO();
        taskMgmtVO.setApplyUser(SecurityUtils.getUserId().toString());
        taskMgmtVO.setPlanTime(query.getPlanTime());
        return processApprovalService.getPcProcess(taskMgmtVO);
    }


    @RequestMapping("/listOutsideConfs")
    public Result<List<BpmnOutsideConf>> listOutsideConfs() {
        Map<String, ActivitiService> activitiServices = this.activitiServices;
        List<BpmnOutsideConf> bpmnOutsideConfs = new ArrayList<>();

        for (String key : activitiServices.keySet()) {
            ActivitiService activitiService = activitiServices.get(key);
            ActivitiServiceAnno annotation = activitiService.getClass().getAnnotation(ActivitiServiceAnno.class);
            String desc = annotation.desc();
            BpmnOutsideConf conf = new BpmnOutsideConf();
            conf.setFormCode(key);
            conf.setBusinessName(desc);
            bpmnOutsideConfs.add(conf);
        }

        return Result.newSuccessResult(bpmnOutsideConfs);
    }
}
