server.port=7001
## mysql
spring.datasource.url=****************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.druid.min-idle=5
spring.datasource.druid.initial-size=5
spring.datasource.druid.keep-alive=true
spring.datasource.druid.max-wait=60000
spring.datasource.druid.max-active=100
spring.datasource.druid.removeAbandoned=true
spring.datasource.druid.removeAbandonedTimeout=1800
spring.datasource.druid.logAbandoned=true
spring.datasource.druid.validation-query=SELECT 1 FROM DUAL
spring.datasource.druid.validation-query-timeout=2000
spring.datasource.druid.test-on-borrow=false
spring.datasource.druid.test-on-return=false
spring.datasource.druid.test-while-idle=true
spring.datasource.druid.time-between-eviction-runs-millis=60000
spring.datasource.druid.min-evictable-idle-time-millis=300000

spring.datasource.hikari.max-lifetime=120000


## mybatis
mybatis.configuration.map-underscore-to-camel-case=true
mybatis.type-aliases-package=org.openoa.**.entity
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
logging.level.org.openoa.mapper=debug
logging.level.org.activiti.engine.impl.persistence.entity=debug
#logging.level.org.openoa.engine.conf.mvc.JiMuMDCCommonsRequestLoggingFilter=debug

# activiti Disable Auto Table Creation
spring.activiti.database-schema-update=none

##å¤ç§æ·æ°æ®æº,éè¦æ¶å¼å¯
#spring.antflow.tenanta.url=***********************************
#spring.antflow.tenanta.username=root
#spring.antflow.tenanta.password=123456

#spring.antflow.tenantb.url=***********************************
#spring.antflow.tenantb.username=root
#spring.antflow.tenantb.password=123456