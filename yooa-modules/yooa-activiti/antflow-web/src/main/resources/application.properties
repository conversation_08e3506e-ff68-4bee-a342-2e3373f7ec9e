spring.profiles.active=@activatedProperties@
## activiti
spring.activiti.check-process-definitions=false
spring.activiti.database-schema-update=none
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8
spring.jackson.serialization.write-dates-as-timestamps=false
spring.aop.proxy-target-class=true
mybatis.config-location=classpath:mybatis-config.xml
spring.main.allow-circular-references=true



# AntFlowEngine Config
#antflow.common.empTable.empTblName=t_user
#antflow.common.empTable.idField=id
#antflow.common.empTable.nameField=user_name
#antflow.common.scan-packages=org.openoa,com.package


# email notification config
#message.email.host=smtp.163.com
#message.email.account=<EMAIL>
#message.email.password=HHVZDETFJMCATUGS

