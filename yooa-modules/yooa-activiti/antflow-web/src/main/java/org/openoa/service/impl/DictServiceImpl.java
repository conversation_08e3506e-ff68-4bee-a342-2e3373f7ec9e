package org.openoa.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.system.api.RemoteDeptService;
import com.yooa.system.api.domain.SysDept;
import com.yooa.system.api.domain.SysRole;
import com.yooa.system.api.domain.SysUser;
import com.yooa.system.api.domain.vo.SysUserVo;
import com.yooa.system.api.model.LoginUser;
import org.apache.commons.lang3.StringUtils;
import org.openoa.base.constant.enums.BpmnConfFlagsEnum;
import org.openoa.base.dto.PageDto;
import org.openoa.base.interf.FormOperationAdaptor;
import org.openoa.base.util.PageUtils;
//import org.openoa.base.util.SecurityUtils;
import org.openoa.base.vo.BaseKeyValueStruVo;
import org.openoa.base.vo.ResultAndPage;
import org.openoa.base.vo.TaskMgmtVO;
import org.openoa.engine.bpmnconf.confentity.BpmnConf;
import com.yooa.common.security.utils.SecurityUtils;
import org.openoa.engine.bpmnconf.confentity.OutSideBpmBusinessParty;
import org.openoa.engine.bpmnconf.mapper.BpmnConfMapper;
import org.openoa.engine.bpmnconf.service.biz.LowCodeFlowBizService;
import org.openoa.engine.bpmnconf.service.impl.BpmnConfServiceImpl;
import org.openoa.entity.DicDateDept;
import org.openoa.entity.DicDateDepts;
import org.openoa.entity.DictData;
import org.openoa.mapper.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 字典表为demo表,一般用户系统都有自己的字典表,可以替换为自己的字典表
 * 目前用途:
 * 1.配置低代码流程
 */
@Service
public class DictServiceImpl implements LowCodeFlowBizService {
    @Autowired
    private DictMainMapper dictMainMapper;
    @Autowired
    private DicDataMapper dicDataMapper;
    @Autowired
    private DicDataDeptMapper dicDataDeptMapper;
    @Autowired
    private DicDataDeptMappers dicDataDeptMappers;
    @Autowired
    private BpmnConfMapper bpmnConfMapper;
    @Autowired
    private BpmnConfServiceImpl bpmnConfService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;

    /**
     * 获取全部 LF FormCodes 在流程设计时选择使用
     * @return
     */
    @Override
    public List<BaseKeyValueStruVo> getLowCodeFlowFormCodes() {
        List<DictData> lowcodeList = getDictItemsByType("lowcodeflow");
        List<BaseKeyValueStruVo> results=new ArrayList<>();
        for (DictData item : lowcodeList) {
            results.add(
                    BaseKeyValueStruVo
                            .builder()
                            .key(item.getValue())
                            .value(item.getLabel())
                            .type("LF")
                            .remark(item.getRemark())
                            .build()
            );
        }
        return results;
    }

    /**
     * 获取LF FormCode Page List 模板列表使用
     * @param pageDto
     * @param taskMgmtVO
     * @return
     */
    @Override
    public ResultAndPage<BaseKeyValueStruVo> selectLFFormCodePageList(PageDto pageDto, TaskMgmtVO taskMgmtVO) {
        Page<BaseKeyValueStruVo> page = PageUtils.getPageByPageDto(pageDto);
        List<DictData> dictDataList = dicDataMapper.selectLFFormCodePageList(page,taskMgmtVO);
        return handleLFFormCodePageList(page,dictDataList);
    }
    /**
     * 获取 已设计流程并且启用的 LF FormCode Page List 发起页面使用
     * @param pageDto
     * @param taskMgmtVO
     * @return
     */
    @Override
    public ResultAndPage<BaseKeyValueStruVo> selectLFActiveFormCodePageList(PageDto pageDto, TaskMgmtVO taskMgmtVO) {
        Page<BaseKeyValueStruVo> page = PageUtils.getPageByPageDto(pageDto);
        taskMgmtVO.setThisUserId(SecurityUtils.getUserId());
        SysUser sysUser= sysUserMapper.selectById(SecurityUtils.getUserId());
        taskMgmtVO.setThisDeptId(sysUser.getDeptId());
        SysDept sysDept=sysDeptMapper.selectById(sysUser.getDeptId());
        //有部门的查祖籍
        if (sysDept != null) {
            String ancestors=sysDept.getAncestors()+","+sysUser.getDeptId();
            List<String> ancestorsList= Arrays.asList(ancestors.split(","));
            taskMgmtVO.setAncestorsList(ancestorsList);
        }
        //管理员查询所有的
        List<DictData> dictDataLists = dicDataMapper.selectLFActiveFormCodePageList(page,taskMgmtVO);
        return handleLFFormCodePageList(page,dictDataLists);
    }

    /**
     * 新增LF FormCode
     * @param vo
     * @return
     */
    @Override
    public Integer addFormCode(BaseKeyValueStruVo vo) {
        Integer result = 0;
        LambdaQueryWrapper<DictData> qryByValue =  Wrappers.<DictData>lambdaQuery()
                .eq(DictData::getValue, vo.getKey());
        List<DictData> dictData = dicDataMapper.selectList(qryByValue);
        if (dictData.isEmpty()){
            DictData  entity = new DictData();
            entity.setDictType("lowcodeflow");
            entity.setValue(vo.getKey());
            entity.setLabel(vo.getValue());
            entity.setRemark(vo.getRemark());
            entity.setIsDefault("N");
            entity.setIsDel(0);

            entity.setCreateUser(SecurityUtils.getNickName());
            entity.setCreateTime(new Date());
            result = dicDataMapper.insert(entity);

            //新增流程权限关联表信息
            DicDateDept dicDateDept=new DicDateDept();
            dicDateDept.setDidId(entity.getId());
            if (vo.getDeptIds()!=null){
                dicDateDept.setDeptIds(Arrays.toString(vo.getDeptIds()));
                List<Long> deptList = new ArrayList<>(Arrays.asList(vo.getDeptIds()));
                for (Long deptId : deptList) {
                    DicDateDepts dicDateDepts=new DicDateDepts();
                    dicDateDepts.setDidId(entity.getId());
                    dicDateDepts.setPermissionId(deptId.toString());
                    dicDateDepts.setPermissionsType("1");
                    dicDataDeptMappers.insert(dicDateDepts);
                }
            }
            if (vo.getUserIds()!=null){
                dicDateDept.setUserIds(Arrays.toString(vo.getUserIds()));
                List<Long> userList = new ArrayList<>(Arrays.asList(vo.getUserIds()));
                for (Long userId : userList) {
                    DicDateDepts dicDateDepts=new DicDateDepts();
                    dicDateDepts.setDidId(entity.getId());
                    dicDateDepts.setPermissionId(userId.toString());
                    dicDateDepts.setPermissionsType("2");
                    dicDataDeptMappers.insert(dicDateDepts);
                }
            }
        }
        return  result;
    }

    @Override
    public Integer updateFormCode(BaseKeyValueStruVo vo) {
        Integer result = 0;
        DictData  entity = new DictData();
        entity.setId(vo.getId());
        entity.setValue(vo.getKey());
        entity.setLabel(vo.getValue());
        entity.setRemark(vo.getRemark());
        entity.setUpdateUser(SecurityUtils.getUsername());
        entity.setCreateTime(new Date());
        result = dicDataMapper.updateById(entity);

        //修改流程权限关联表信息先删除再新增
        LambdaQueryWrapper<DicDateDepts> dicDateDeptWrapper = new LambdaQueryWrapper<>();
        dicDateDeptWrapper.eq(DicDateDepts::getDidId, entity.getId());
        dicDataDeptMappers.delete(dicDateDeptWrapper);

        if (vo.getDeptIds()!=null){
            List<Long> deptList = new ArrayList<>(Arrays.asList(vo.getDeptIds()));
            for (Long deptId : deptList) {
                DicDateDepts dicDateDepts=new DicDateDepts();
                dicDateDepts.setDidId(entity.getId());
                dicDateDepts.setPermissionId(deptId.toString());
                dicDateDepts.setPermissionsType("1");
                dicDataDeptMappers.insert(dicDateDepts);
            }
        }
        if (vo.getUserIds()!=null){
            List<Long> userList = new ArrayList<>(Arrays.asList(vo.getUserIds()));
            for (Long userId : userList) {
                DicDateDepts dicDateDepts=new DicDateDepts();
                dicDateDepts.setDidId(entity.getId());
                dicDateDepts.setPermissionId(userId.toString());
                dicDateDepts.setPermissionsType("2");
                dicDataDeptMappers.insert(dicDateDepts);
            }
        }
        return result;
    }

    @Override
    public Integer deleteLFFormCode(Integer id) {
        DictData dictData = dicDataMapper.selectById(id);
        LambdaQueryWrapper<BpmnConf> bpmnConfWrapper = new LambdaQueryWrapper<>();
        bpmnConfWrapper.eq(BpmnConf::getFormCode, dictData.getValue());
        bpmnConfWrapper.eq(BpmnConf::getEffectiveStatus,1);
        BpmnConf bpmnConf= bpmnConfMapper.selectOne(bpmnConfWrapper);
        if (bpmnConf==null){
            dictData.setIsDel(1);
            dicDataMapper.updateById(dictData);
            return 1;
        }else{
            return 0;
        }
    }

    /** 私有方法 */
    private List<DictData> getDictItemsByType(String dictType){
        LambdaQueryWrapper<DictData> qryByDictType = Wrappers.<DictData>lambdaQuery()
                .eq(DictData::getDictType, dictType);
        List<DictData> dictData = dicDataMapper.selectList(qryByDictType);
        dictData.sort(Comparator.comparing(DictData::getCreateTime).reversed());
        return dictData;
    }

    public static Long[] convertStringToLongArray(String input) {
        if (input == null || input.isEmpty()) {
            return new Long[0];
        }

        // 去除方括号
        String content = input.replaceAll("[\\[\\]]", "");
        if (content.isEmpty()) {
            return new Long[0];
        }

        // 分割字符串
        String[] elements = content.split(",\\s*");
        List<Long> result = new ArrayList<>();

        for (String element : elements) {
            try {
                // 转换并处理可能的空格
                Long num = Long.valueOf(element.trim());
                result.add(num);
            } catch (NumberFormatException e) {
                // 处理无效元素（例如非数字字符串）
                System.err.println("无法转换的字符串: " + element);
            }
        }

        return result.toArray(new Long[0]);
    }
    /** 私有方法 */
    private ResultAndPage<BaseKeyValueStruVo> handleLFFormCodePageList(Page page, List<DictData> dictlist) {
        if (dictlist ==null) {
            return PageUtils.getResultAndPage(page);
        }
        List<BaseKeyValueStruVo> results=new ArrayList<>();
        for (DictData item : dictlist) {
            //获取可见部门ID
            DicDateDepts dicDateDepts=new DicDateDepts();
            QueryWrapper<DicDateDepts> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("dic_id", item.getId());
            List<DicDateDepts> dicList=dicDataDeptMappers.selectList(queryWrapper);
            List<String> deptIds=new ArrayList<>();
            List<String> userIds=new ArrayList<>();
            for(DicDateDepts dicDateDept:dicList){
                if(dicDateDept.getPermissionsType().equals("1")){
                    deptIds.add(dicDateDept.getPermissionId());
                }if(dicDateDept.getPermissionsType().equals("2")){
                    userIds.add(dicDateDept.getPermissionId());
                }
            }
            results.add(
                    BaseKeyValueStruVo
                            .builder()
                            .id(item.getId())
                            .key(item.getValue())
                            .value(item.getLabel())
                            .deptIds(deptIds != null ?  convertStringToLongArray(String.join(",", deptIds)) : null)
                            .userIds(userIds != null ? convertStringToLongArray(String.join(",", userIds)) : null)
                            .createTime(item.getCreateTime())
                            .type("LF")
                            .remark(item.getRemark())
                            .build()
            );
        }
        List<String> formCodes = results.stream().map(BaseKeyValueStruVo::getKey).collect(Collectors.toList());
        LambdaQueryWrapper<BpmnConf> queryWrapper = Wrappers.<BpmnConf>lambdaQuery()
                .select(BpmnConf::getFormCode, BpmnConf::getExtraFlags)

                .eq(BpmnConf::getEffectiveStatus, 1)
                .isNotNull(BpmnConf::getExtraFlags);
        if (formCodes !=null &&  !formCodes.isEmpty()) {
            queryWrapper.in(BpmnConf::getFormCode, formCodes);
        }
        List<BpmnConf> bpmnConfs = bpmnConfService.list(queryWrapper);
        if(!CollectionUtils.isEmpty(bpmnConfs)){
            Map<String, Integer> formCode2Flags = bpmnConfs.stream().collect(Collectors.toMap(BpmnConf::getFormCode, BpmnConf::getExtraFlags, (v1, v2) -> v1));
            for (BaseKeyValueStruVo lfDto : results) {
                Integer flags = formCode2Flags.get(lfDto.getKey());
                if(flags!=null){
                    boolean hasStartUserChooseModules = BpmnConfFlagsEnum.hasFlag(flags, BpmnConfFlagsEnum.HAS_STARTUSER_CHOOSE_MODULES);
                    lfDto.setHasStarUserChooseModule(hasStartUserChooseModules);
                }
            }
        }
        page.setRecords(results);
        return PageUtils.getResultAndPage(page);
    }
}
