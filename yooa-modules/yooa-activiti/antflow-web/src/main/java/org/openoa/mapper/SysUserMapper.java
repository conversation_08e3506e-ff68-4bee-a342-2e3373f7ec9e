package org.openoa.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.system.api.domain.SysUser;
import com.yooa.system.api.domain.vo.SysUserVo;
import org.apache.ibatis.annotations.Mapper;
import org.openoa.base.vo.BaseIdTranStruVo;
import org.openoa.entity.Student;

import java.util.List;

@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {

}
