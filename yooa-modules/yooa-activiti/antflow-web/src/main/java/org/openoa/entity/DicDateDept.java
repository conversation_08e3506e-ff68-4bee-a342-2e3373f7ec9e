package org.openoa.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
@TableName("t_dict_data_dept")
public class DicDateDept extends BaseEntity {
    @TableId(value = "id", type = IdType.AUTO)
    private Long Id;
    @TableField("dic_id")
    private  Long DidId;
    @TableField("dept_ids")
    private  String DeptIds;
    @TableField("user_ids")
    private  String UserIds;

}

