package com.yooa.cmf.service.impl;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.cmf.api.domain.CmfUsers;
import com.yooa.cmf.api.domain.CmfUsersCharge;
import com.yooa.cmf.api.domain.vo.ConsumerUpVo;
import com.yooa.cmf.api.domain.vo.ConvertAnchorVo;
import com.yooa.cmf.api.domain.vo.ConvertCustomerVo;
import com.yooa.cmf.mapper.CmfUsersChargeMapper;
import com.yooa.cmf.mapper.CmfUsersMapper;
import com.yooa.cmf.service.CmfUsersService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@AllArgsConstructor
@Service
public class CmfUsersServiceImpl extends ServiceImpl<CmfUsersMapper, CmfUsers> implements CmfUsersService {

    private final CmfUsersMapper cmfUsersMapper;
    private final CmfUsersChargeMapper cmfUsersChargeMapper;

    @Override
    public List<ConvertCustomerVo> customersAfterId(Long id) {
        return baseMapper.selectCustomersAfterId(id);
    }

    @Override
    public List<ConvertCustomerVo> customersAfterLastLoginTime(String lastLoginTime) {
        return baseMapper.selectCustomersAfterLastLoginTime(lastLoginTime);
    }

    @Override
    public List<ConvertAnchorVo> anchorListByGtId(Long id) {
        return baseMapper.selectAnchorListByGtId(id);
    }


    @Override
    public List<ConsumerUpVo> totalUp(List<Long> ids) {
        return cmfUsersMapper.totalUp(ids);
    }

    @Override
    public List<ConsumerUpVo> consumerUpChoose(List<Long> ids, String beginTime, String endTime) {
        return cmfUsersMapper.consumerUpChoose(ids, beginTime, endTime);
    }


    @Override
    public List<CmfUsersCharge> selCmfUsersChargeList(String date) {
        List<CmfUsersCharge> list = new ArrayList<>();
        LocalDateTime localDateTime = LocalDateTime.parse(date);
        long timestamp = LocalDateTimeUtil.toEpochMilli(localDateTime) / 1000;        // 十位时间戳
        if (localDateTime.compareTo(LocalDateTime.of(2024, 1, 1, 0, 0)) == 0) {
            // 第一次去拿的时候拿订单创建时间
            list = cmfUsersChargeMapper.selectList(new LambdaQueryWrapper<CmfUsersCharge>()
                    .eq(CmfUsersCharge::getStatus, 1)            // 只拿已完成的(初始化获取数据)
                    .ge(CmfUsersCharge::getAddtime,          // 订单创建时间(大于等于)
                            timestamp));
        }
        else {
            list = cmfUsersChargeMapper.selectList(new LambdaQueryWrapper<CmfUsersCharge>()
                    .eq(CmfUsersCharge::getStatus, 1)            // 只拿已完成的(根据完成时间获取后续订单)
                    .gt(CmfUsersCharge::getTradeDateTime,            // 订单完成时间(大于)
                            timestamp));
        }

        return list;
    }
}
