package com.yooa.cmf.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yooa.cmf.api.domain.CmfAgentAdminAndUser;
import com.yooa.cmf.service.CmfAdminAndUserService;
import com.yooa.common.core.domain.R;
import com.yooa.common.core.utils.LocalDateUtil;
import com.yooa.common.security.annotation.InnerAuth;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/cmf/admin/user")
public class CmfAdminAndUserController {

    private final CmfAdminAndUserService cmfAdminAndUserService;

    /**
     * 定时任务 - 查询推广和用户的绑定记录
     *
     * @param date 完成时间
     */
    @InnerAuth
    @GetMapping("/synchronizationSignBind")
    public R<List<CmfAgentAdminAndUser>> synchronizationSignBind(@RequestParam("date") String date) {
        List<CmfAgentAdminAndUser> list = cmfAdminAndUserService.list(new LambdaQueryWrapper<CmfAgentAdminAndUser>()
                .gt(CmfAgentAdminAndUser::getAddTime, LocalDateUtil.toEpochSecond(LocalDateTimeUtil.parse(date)))
                .in(CmfAgentAdminAndUser::getType, CollUtil.newArrayList(0, 1))
                .eq(CmfAgentAdminAndUser::getServeId, 0));
        return R.ok(list);
    }

    /**
     * 查询客户最新的绑定记录
     *
     * @param customerId 客户ID
     */
    @InnerAuth
    @GetMapping("/{customerId}")
    public R<CmfAgentAdminAndUser> getByCustomerId(@PathVariable Long customerId) {
        CmfAgentAdminAndUser cmfAgentAdminAndUser = cmfAdminAndUserService.getOne(
                Wrappers.<CmfAgentAdminAndUser>lambdaQuery()
                        .eq(CmfAgentAdminAndUser::getUserId, customerId.intValue())
                        .last("limit 1"));
        return R.ok(cmfAgentAdminAndUser);
    }
}
