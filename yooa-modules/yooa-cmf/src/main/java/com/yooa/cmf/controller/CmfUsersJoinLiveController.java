package com.yooa.cmf.controller;

import com.yooa.cmf.api.domain.vo.ConvertCustomerJoinAnchorVo;
import com.yooa.cmf.service.CmfUsersJoinLiveService;
import com.yooa.common.core.domain.R;
import com.yooa.common.security.annotation.InnerAuth;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/cmf/user/live")
public class CmfUsersJoinLiveController {

    private final CmfUsersJoinLiveService cmfUsersJoinLiveService;

    @InnerAuth
    @GetMapping("/list/gt/{id}")
    public R<List<ConvertCustomerJoinAnchorVo>> listByGtId(@PathVariable Long id) {
        return R.ok(cmfUsersJoinLiveService.listByGtId(id));
    }

    @InnerAuth
    @GetMapping("/list/status-change/{ids}")
    public R<List<ConvertCustomerJoinAnchorVo>> listByStatusChange(@PathVariable List<Long> ids) {
        return R.ok(cmfUsersJoinLiveService.listByStatusChange(ids));
    }


}
