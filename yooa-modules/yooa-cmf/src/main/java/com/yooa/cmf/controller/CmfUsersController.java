package com.yooa.cmf.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.yooa.cmf.api.domain.CmfUsers;
import com.yooa.cmf.api.domain.CmfUsersCharge;
import com.yooa.cmf.api.domain.query.CmfUserQuery;
import com.yooa.cmf.api.domain.vo.ConsumerUpVo;
import com.yooa.cmf.api.domain.vo.ConvertAnchorVo;
import com.yooa.cmf.api.domain.vo.ConvertCustomerVo;
import com.yooa.cmf.service.CmfUsersService;
import com.yooa.common.core.domain.R;
import com.yooa.common.security.annotation.InnerAuth;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/cmf/user")
public class CmfUsersController {

    private final CmfUsersService usersService;

    @InnerAuth
    @GetMapping("/list")
    public R<List<CmfUsers>> list(CmfUserQuery query) {
        LambdaQueryWrapper<CmfUsers> wrapper = Wrappers.<CmfUsers>lambdaQuery()
                .in(ObjUtil.isNotEmpty(query.getId()), CmfUsers::getId, query.getId())
                .in(CollUtil.isNotEmpty(query.getIds()), CmfUsers::getId, query.getIds())
                .eq(StrUtil.isNotBlank(query.getType()), CmfUsers::getType, query.getType());
        return R.ok(usersService.list(wrapper));
    }

    /**
     * 获取pd平台消费者充值记录(总充值 ( 数据量很大))
     */
    @InnerAuth
    @PostMapping("/totalUp")
    public R<List<ConsumerUpVo>> totalUp(@RequestBody List<Long> ids) {
        return R.ok(usersService.totalUp(ids));
    }

    @InnerAuth
    @PostMapping("/consumerUpChoose")
    public R<?> consumerUpChoose(@RequestBody List<Long> ids, @RequestParam("beginTime") String beginTime, @RequestParam("endTime") String endTime) {
        return R.ok(usersService.consumerUpChoose(ids, beginTime, endTime));
    }

    /**
     * 获取大于自增ID后的客户数据
     */
    @InnerAuth
    @GetMapping("/customers/after/id")
    public R<List<ConvertCustomerVo>> customersAfterId(@RequestParam("id") Long id) {
        return R.ok(usersService.customersAfterId(id));
    }

    /**
     * 获取大于最后登录时间后的客户数据
     */
    @InnerAuth
    @GetMapping("/customers/after/last-login-Time")
    public R<List<ConvertCustomerVo>> customersAfterLastLoginTime(@RequestParam("lastLoginTime") String lastLoginTime) {
        return R.ok(usersService.customersAfterLastLoginTime(lastLoginTime));
    }

    /**
     * 获取大于自增ID后的主播数据
     */
    @InnerAuth
    @GetMapping("/anchor/list/gt/{id}")
    public R<List<ConvertAnchorVo>> anchorListByGtId(@PathVariable("id") Long id) {
        return R.ok(usersService.anchorListByGtId(id));
    }

    /**
     * 定时任务 - 获取充值记录列表
     *
     * @param date 完成时间
     */
    @InnerAuth
    @GetMapping("/selCmfUsersChargeList")
    public R<List<CmfUsersCharge>> selCmfUsersChargeList(@RequestParam("date") String date) {
        return R.ok(usersService.selCmfUsersChargeList(date));
    }
    
    /**
     * 获取客户信息
     */
    @InnerAuth
    @GetMapping("/customer/{id}")
    public R<CmfUsers> getCustomerById(@PathVariable("id") Long id) {
        return R.ok(usersService.getOne(Wrappers.<CmfUsers>lambdaQuery().eq(CmfUsers::getId, id).eq(CmfUsers::getType, "A")));
    }
}
