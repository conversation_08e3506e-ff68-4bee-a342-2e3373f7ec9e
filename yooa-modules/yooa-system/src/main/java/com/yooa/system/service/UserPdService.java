package com.yooa.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.system.api.domain.SysUserPd;
import com.yooa.system.api.domain.vo.UserPdVo;

import java.util.List;
import java.util.Map;

public interface UserPdService extends IService<SysUserPd> {

    /**
     * 根据用户id查询绑定的PD账号并按类别分组
     */
    Map<String, List<UserPdVo>> mapByUserId(Long userId);

    /**
     * 根据花名册id查询绑定的PD账号并按类别分组
     */
    Map<String, List<UserPdVo>> mapByRosterId(Long rosterId);

    /**
     * 获取绑定关系
     */
    SysUserPd getByPyId(Long pyId);

    /**
     * 保存OA账号与PY账号的绑定关系
     *
     * @param userId OA账号id
     * @param pyId   PY账号id
     * @param type   绑定关系类型
     */
    boolean save(Long userId, Long pyId, String type);

    /**
     * 删除OA账号与PY账号的绑定关系
     *
     * @param userId OA账号id
     * @param pyId   PY账号id
     */
    boolean remove(Long userId, Long pyId);

}
