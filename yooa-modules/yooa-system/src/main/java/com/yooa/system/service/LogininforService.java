package com.yooa.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.system.api.domain.SysLogininfor;
import com.yooa.system.api.domain.query.LogininforQuery;

import java.util.List;

/**
 * 系统访问日志情况信息 服务层
 *
 * <AUTHOR>
 */
public interface LogininforService extends IService<SysLogininfor> {
    /**
     * 查询系统登录日志集合
     *
     * @param query 访问日志对象
     * @return 登录记录集合
     */
    public List<SysLogininfor> selectLogininforList(Page<SysLogininfor> page, LogininforQuery query);


    /**
     * 清空系统登录日志
     */
    public void cleanLogininfor();
}
