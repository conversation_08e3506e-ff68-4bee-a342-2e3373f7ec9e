package com.yooa.system.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
/**
 * 排班表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysYooaAttendGroup extends BaseEntity {

    /**
     * 用户id
     */
    @TableId(value = "group_id", type = IdType.AUTO)
    private Long groupId;
    /**
     * 考勤组名称
     */
    private String groupName;
    /**
     * 考勤组类型
     */
    private String groupType;
    /**
     * 班次id
     */
    private String classId;
    /**
     * 考勤组负责人
     */
    private String ownerUserId;
    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
