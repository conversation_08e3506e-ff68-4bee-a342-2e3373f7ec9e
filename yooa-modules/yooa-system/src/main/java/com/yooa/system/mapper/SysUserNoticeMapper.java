package com.yooa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yooa.system.api.domain.SysUserNotice;
import com.yooa.system.api.domain.vo.SysUserNoticeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户与通知关联表 数据层
 */
public interface SysUserNoticeMapper extends BaseMapper<SysUserNotice> {

    /**
     * 根据通知id获取相关联集合
     */
    public List<SysUserNoticeVo> selectUserNoticeListByNoticeId(@Param("noticeId") Long noticeId);

    /**
     * 根据用户id获取相关联集合
     */
    public List<SysUserNoticeVo> selectUserNoticeListByUserId(@Param("userId") Long userId, @Param("status") String status,@Param("noticeType") String noticeType);

    /**
     * 获取所有用户都已读的通知id集合
     */
    public List<Long> selectListIsReadByNoticeIds(@Param("noticeIds") List<Long> noticeIds);

    /**
     * 批量插入用户与通知关联数据
     */
    public int batchInsert(@Param("userNoticeList") List<SysUserNotice> userNoticeList);



}




