package com.yooa.system.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.system.api.domain.SysRosterUser;
import com.yooa.system.mapper.SysRosterUserMapper;
import com.yooa.system.service.RosterUserService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 花名册与用户关联 - 服务实现层
 *
 * <AUTHOR>
 */
@Service
public class RosterUserServiceImpl extends ServiceImpl<SysRosterUserMapper, SysRosterUser> implements RosterUserService {

    @Override
    public List<SysRosterUser> listByRosterId(Long rosterId) {
        return baseMapper.selectList(
                Wrappers.<SysRosterUser>lambdaQuery()
                        .eq(SysRosterUser::getRosterId, rosterId)
        );
    }

    @Override
    public SysRosterUser getByUserId(Long userId) {
        return baseMapper.selectOne(
                Wrappers.<SysRosterUser>lambdaQuery()
                        .eq(SysRosterUser::getUserId, userId)
        );
    }

    @Override
    public boolean save(Long rosterId, Long userId) {
        return save(
                SysRosterUser.builder()
                        .rosterId(rosterId)
                        .userId(userId)
                        .build()
        );
    }
}
