package com.yooa.system.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.common.core.domain.R;
import com.yooa.common.core.text.Convert;
import com.yooa.common.core.utils.poi.ExcelUtil;
import com.yooa.common.core.web.controller.BaseController;
import com.yooa.common.core.web.domain.AjaxResult;
import com.yooa.common.log.annotation.Log;
import com.yooa.common.log.enums.BusinessType;
import com.yooa.common.security.annotation.RequiresPermissions;
import com.yooa.common.security.utils.SecurityUtils;
import com.yooa.system.api.domain.*;
import com.yooa.system.api.domain.dto.*;
import com.yooa.system.api.domain.query.PublicUserQuery;
import com.yooa.system.api.domain.query.RosterQuery;
import com.yooa.system.api.domain.vo.PublicUserVo;
import com.yooa.system.api.domain.vo.RosterSensitiveVo;
import com.yooa.system.api.domain.vo.SysRecruitVo;
import com.yooa.system.mapper.SysDeptMapper;
import com.yooa.system.mapper.SysRecruitCountMapper;
import com.yooa.system.mapper.SysRecruitMapper;
import com.yooa.system.service.*;
import lombok.AllArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 招聘计划 - 控制层
 */
@Validated
@AllArgsConstructor
@RestController
@RequestMapping("/recruit")
public class RecruitController extends BaseController {

    private final RecruitService recruitService;
    private final SysRecruitCountMapper sysRecruitCountMapper;
    private final SysRecruitMapper sysRecruitMapper;
    private final SysDeptMapper sysDeptMapper;
    /**
     * 分页查询招聘计划
     */
    @GetMapping("/getRecruitList")
    public AjaxResult getRecruitList(Page<SysRecruitVo> page,SysRecruitVo vo) {
        return success(page.setRecords(recruitService.selectRecruitList(page,vo)));
    }

    /**
     * 查询招聘计划 属于同一个流程的一起返回
     */
    @PostMapping("/getAllRecruitList")
    public AjaxResult getAllRecruitList(@Validated @RequestBody SysRecruitVo vo) {
        return success(recruitService.getAllRecruitList(vo));
    }


    /**
     * 导入招聘计划
     * @param file
     * @return
     * @throws Exception
     */
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file,  @NotBlank(message = "招聘年月不能为空") String month) throws Exception {
        String message = recruitService.importRecruit(file,month);
        return success(message);
    }

    /**
     * 招聘计划模板下载
     */
    @PostMapping("/import-template")
    public void importTemplate(HttpServletResponse response, RosterQuery query) {
        ExcelUtil<SysRecruitVo> util = new ExcelUtil<SysRecruitVo>(SysRecruitVo.class);
        util.importTemplateExcel(response, "导入招聘计划模板");
    }


    /**
     * 模板下载
     * @return
     * @throws IOException
     */
    @PostMapping("/download-template")
    public ResponseEntity<byte[]> downloadExcelTemplate() throws IOException {
        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("导入招聘计划模板");

        // 创建样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"需求部门", "专员数量", "管理数量"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 创建数据验证助手
        DataValidationHelper validationHelper = sheet.getDataValidationHelper();

        // 2. 部门下拉列表（使用隐藏工作表）
        Sheet deptSheet = workbook.createSheet("部门列表");
        workbook.setSheetHidden(workbook.getSheetIndex(deptSheet), true);
        LambdaQueryWrapper<SysDept> wrapperDept = new LambdaQueryWrapper<>();
        wrapperDept.eq(SysDept::getDeptLevel, 3);
        List<SysDept> deptList=sysDeptMapper.selectList(wrapperDept);
        List<String> deptNameList=new ArrayList<>();
        for(SysDept dept:deptList){
            deptNameList.add(dept.getDeptName());
        }
        String[] departments = deptNameList.toArray(new String[deptNameList.size()]);
        for (int i = 0; i < departments.length; i++) {
            Row row = deptSheet.createRow(i);
            row.createCell(0).setCellValue(departments[i]);
        }
        Name deptNamedRange = workbook.createName();
        deptNamedRange.setNameName("Departments");
        deptNamedRange.setRefersToFormula("部门列表!$A$1:$A$" + departments.length);
        DataValidationConstraint deptConstraint = validationHelper.createFormulaListConstraint("Departments");
        CellRangeAddressList deptAddressList = new CellRangeAddressList(0, 1000, 0, 0); // C2到C1001
        DataValidation deptValidation = validationHelper.createValidation(deptConstraint, deptAddressList);
        deptValidation.createPromptBox("提示", "请选择部门");
        sheet.addValidationData(deptValidation);

        // 设置列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(50);
        }

        // 将工作簿写入字节数组输出流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        workbook.write(outputStream);
        workbook.close();

        // 设置响应头
        HttpHeaders responseHeaders = new HttpHeaders();
        responseHeaders.setContentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
        responseHeaders.setContentDispositionFormData("attachment", "employee_template.xlsx");

        return ResponseEntity.ok()
                .headers(responseHeaders)
                .body(outputStream.toByteArray());
    }


    /**
     * 批量修改招聘计划
     */
    @PutMapping("edit")
    public AjaxResult edit(@Validated @RequestBody SysRecruitVo vo) throws Exception {
        return AjaxResult.success(recruitService.updateRecruit(vo));
    }

    /**
     * 修改招聘计划
     */
    @PostMapping("updateRecruit")
    public AjaxResult updateRecruit(@Validated @RequestBody SysRecruitVo vo) throws Exception {
        return AjaxResult.success(recruitService.updateRecruitOne(vo));
    }

    /**
     * 批量取消招聘计划需求
     */
    @PutMapping("editBatch")
    public AjaxResult editBatch(@Validated @RequestBody SysRecruitVo vo) throws Exception {
        return AjaxResult.success(recruitService.editBatch(vo));
    }

    /**
     * 退回招聘计划
     */
    @PutMapping("remove")
    public AjaxResult remove(@Validated @RequestBody SysRecruitVo sysRecruit){
        return AjaxResult.success(recruitService.deleteRecruitByIds(sysRecruit));
    }

    /**
     * 通过
     */
    @PutMapping("passRecruit")
    public AjaxResult passRecruit(@Validated @RequestBody SysRecruitVo vo){
        return AjaxResult.success(recruitService.passRecruit(vo));
    }

    /**
     * 追回
     */
    @PutMapping("recoverRecruitByIds")
    public AjaxResult recoverRecruitByIds(@Validated @RequestBody SysRecruitVo vo){
        return AjaxResult.success(recruitService.recoverRecruitByIds(vo));
    }


    /**
     * 根据招聘计划ID查询计划详情
     */
    @GetMapping("/getRecruit")
    public AjaxResult getRecruit( @NotNull(message = "招聘计划ID不能为空") Long recruitId){
        return AjaxResult.success(sysRecruitMapper.selectById(recruitId));
    }

    /**
     * 根据月份统计我的招聘计划统计
     */
    @PostMapping("/getRecruitByMouthCount")
    public AjaxResult getRecruitByMouthCount(@Validated @RequestBody SysRecruitVo vo) {
        return AjaxResult.success(recruitService.getRecruitByMouthCount(vo));
    }
}
