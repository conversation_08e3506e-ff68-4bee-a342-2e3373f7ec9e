package com.yooa.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.system.api.domain.SysUser;
import com.yooa.system.api.domain.dto.UserEditDto;
import com.yooa.system.api.domain.dto.UserSaveDto;
import com.yooa.system.api.domain.query.PublicUserQuery;
import com.yooa.system.api.domain.query.UserQuery;
import com.yooa.system.api.domain.vo.PublicUserVo;
import com.yooa.system.api.domain.vo.RosterSensitiveVo;
import com.yooa.system.api.domain.vo.SysUserNoticeVo;
import com.yooa.system.api.domain.vo.SysUserVo;

import java.util.List;

/**
 * 用户 业务层
 *
 * <AUTHOR>
 */
public interface UserService extends IService<SysUser> {
    /**
     * 根据条件分页查询用户列表
     *
     * @param page  分页对象 - 不分页传null或size小于0
     * @param query 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUserVo> selectUserList(Page<SysUserVo> page, UserQuery query);

    /**
     * 根据条件分页查询用户列表(不走权限)
     *
     * @param page  分页对象 - 不分页传null或size小于0
     * @param query 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUserVo> selectUserListSpecial(Page<SysUserVo> page, UserQuery query);

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param query 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUserVo> selectAllocatedList(Page<SysUserVo> page, UserQuery query);

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param query 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUserVo> selectUnallocatedList(Page<SysUserVo> page, UserQuery query);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public SysUserVo selectUserByUserName(String userName);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public SysUserVo selectUserById(Long userId);

    /**
     * 根据用户ID查询用户所属角色组
     *
     * @param nickName 用户名
     * @return 结果
     */
    public String selectUserRoleGroup(String nickName);

    /**
     * 根据用户ID查询用户所属岗位组
     *
     * @param nickName 用户名
     * @return 结果
     */
    public String selectUserPostGroup(String nickName);

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean checkUserNameUnique(SysUser user);

    /**
     * 校验用户是否允许操作
     *
     * @param userId 用户id
     */
    public void checkUserAllowed(Long userId);

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    public void checkUserDataScope(Long userId);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int insertUser(UserSaveDto user);

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public boolean registerUser(SysUser user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUser(UserEditDto user);

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    public void insertUserAuth(Long userId, Long[] roleIds);

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteUserById(Long userId);

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    public int deleteUserByIds(List<Long> userIds);

    /**
     * 导入用户数据
     *
     * @param userList        用户数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @return 结果
     */
    public String importUser(List<SysUser> userList, Boolean isUpdateSupport);

    /**
     * 查询用户的通知列表
     */
    public List<SysUserNoticeVo> getMyNoticeList(String status,String noticeType);

    /**
     * 查询用户未读通知数量
     */
    public Long getMyUnreadNoticeNumber();

    public List<PublicUserVo> getPublicUserList(PublicUserQuery query);

    public List<PublicUserVo> getPublicPageUserList(Page<PublicUserVo> page,PublicUserQuery query);

}
