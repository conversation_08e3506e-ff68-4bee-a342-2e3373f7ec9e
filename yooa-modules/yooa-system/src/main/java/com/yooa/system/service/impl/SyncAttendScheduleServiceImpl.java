package com.yooa.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.utils.DateUtil;
import com.yooa.external.api.RemoteDingTalkAttendanceService;
import com.yooa.external.api.request.DingTalkAttendScheduleRequest;
import com.yooa.external.api.response.DingTalkAttendScheduleRes;
import com.yooa.system.api.domain.SysAttendSchedule;
import com.yooa.system.api.domain.vo.AttendUserVo;
import com.yooa.system.mapper.SysAttendScheduleMapper;
import com.yooa.system.mapper.SysUserMapper;
import com.yooa.system.service.SyncAttendScheduleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
public class SyncAttendScheduleServiceImpl extends ServiceImpl<SysAttendScheduleMapper, SysAttendSchedule> implements SyncAttendScheduleService {


    private static final ThreadPoolExecutor executor = new ThreadPoolExecutor(5, 20, 60, TimeUnit.MINUTES, new ArrayBlockingQueue(200), new ThreadPoolExecutor.CallerRunsPolicy());
    @Autowired
    private RemoteDingTalkAttendanceService remoteDingTalkAttendanceService;
    @Autowired
    private SysUserMapper sysUserMapper;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void syncAttendSchedule() {
        // 获取系统所有用户
        List<AttendUserVo> attendUserVoList = sysUserMapper.queryDingUser();
        if (attendUserVoList == null || attendUserVoList.size() == 0) {
            return;
        }
        // 获取当月天数
        Date date = new Date();
        Integer dayNum = DateUtil.getDays(date);

        // 删除处理时间范围内数据
        baseMapper.deleteByTime(DateUtil.getMonthFirst(date), DateUtil.getMonthFinally(date));

        // 获取查询的时间范围
        Date endDate = DateUtil.getAfterDays(DateUtil.getMonthFirst(date), 7);
        Date startDate = DateUtil.getMonthFirst(date);
        dayNum = dayNum - 7;
        do {

            Long startTime = startDate.getTime();
            Long endTime = endDate.getTime();
            // 循环处理获取数据
            List<List<AttendUserVo>> attendUserList = ListUtil.partition(attendUserVoList, 50);
            for (List<AttendUserVo> attendUserVoList1 : attendUserList) {
                CompletableFuture.runAsync(() ->
                        detailData(attendUserVoList1, startTime, endTime), executor);
            }
            if (dayNum == 0) {
                return;
            }
            startDate = DateUtil.getAfterDays(endDate, 1);
            ;
            if (dayNum >= 7) {
                endDate = DateUtil.getAfterDays(endDate, 7);
                dayNum = dayNum - 6;
            }
            else {
                endDate = DateUtil.getAfterDays(endDate, dayNum - 1);
                dayNum = 0;
            }

        } while (dayNum >= 0);

    }


    private void detailData(List<AttendUserVo> attendUserVoList, Long startTime, Long endTime) {
        String userList = attendUserVoList.stream().map(AttendUserVo::getDingUserId).collect(Collectors.joining(","));
        DingTalkAttendScheduleRequest request = new DingTalkAttendScheduleRequest();
        request.setUserIds(userList);
        request.setStartTime(startTime);
        request.setEndTime(endTime);
        List<DingTalkAttendScheduleRes> dingTalkAttendScheduleResList = remoteDingTalkAttendanceService.syncgAttendSchedule(request, SecurityConstants.INNER).getData();

        for (DingTalkAttendScheduleRes dingTalkAttendScheduleRes : dingTalkAttendScheduleResList) {
            SysAttendSchedule sysAttendSchedule = new SysAttendSchedule();
            BeanUtil.copyProperties(dingTalkAttendScheduleRes, sysAttendSchedule);
            baseMapper.insert(sysAttendSchedule);
        }
    }
}
