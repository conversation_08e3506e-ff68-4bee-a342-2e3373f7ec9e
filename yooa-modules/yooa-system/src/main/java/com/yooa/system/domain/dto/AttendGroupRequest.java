package com.yooa.system.domain.dto;

import com.yooa.common.mybatis.base.QueryEntity;
import com.yooa.system.domain.vo.RelationVo;
import lombok.*;

import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendGroupRequest extends QueryEntity {
    /**
     * 考勤组名称
     */
    private String groupName;
    /**
     * 班次ID id,id,id
     */
    private String classIds;
    /**
     * 考勤组id 新增的时候不传
     */
    private Long groupId;
    /**
     * 组织信息
     */
    private List<RelationVo> relationVoList;
}
