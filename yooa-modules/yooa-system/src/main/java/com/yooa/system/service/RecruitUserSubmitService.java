package com.yooa.system.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yooa.system.api.domain.SysRecruitUserSubmit;
import com.yooa.system.api.domain.vo.SysRecruitUserSubmitVo;
import com.yooa.system.api.domain.vo.SysRecruitVo;
import org.apache.ibatis.annotations.Param;


import java.util.List;

/**
 * 招聘计划业务层
 *
 * <AUTHOR>
 */
public interface RecruitUserSubmitService extends IService<SysRecruitUserSubmit> {
    /**
     * 分页查询
     * @return 招聘计划发布列表
     */
    public List<SysRecruitUserSubmitVo> selectSubmitList(Page<SysRecruitUserSubmitVo> page, @Param("query") SysRecruitUserSubmitVo vo);

}
