package com.yooa.system.domain.vo;

import com.yooa.common.mybatis.base.QueryEntity;
import com.yooa.system.api.domain.vo.SysRosterVo;
import com.yooa.system.domain.SysYooaAttendClass;
import lombok.*;

import java.util.List;


@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendGroupRelationVo extends QueryEntity {
    /**
     * 关联名称集合
     */
    private List<String> relationNameList;
    /**
     * 班次ID集合
     */
    private List<SysYooaAttendClass> sysYooaAttendClassList;

    /**
     * 排班人员信息
     */
    private List<SysRosterVo> sysRosterVoList;

}
