package com.yooa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yooa.system.api.domain.SysPersonnelChangeRecord;
import com.yooa.system.api.domain.query.PersonnelChangeRecordQuery;
import com.yooa.system.api.domain.vo.PersonnelChangeRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 人员变动记录 - 数据层
 */
public interface SysPersonnelChangeRecordMapper extends BaseMapper<SysPersonnelChangeRecord> {

    List<PersonnelChangeRecordVo> selectPersonnelChangeRecordList(@Param("query") PersonnelChangeRecordQuery query);

}




