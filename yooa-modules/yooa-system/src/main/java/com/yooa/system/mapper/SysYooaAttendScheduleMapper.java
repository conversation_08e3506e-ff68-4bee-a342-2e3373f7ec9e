package com.yooa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yooa.system.domain.SysYooaAttendSchedule;
import com.yooa.system.domain.vo.AttendClassInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;


/**
 * 考勤组
 */
public interface SysYooaAttendScheduleMapper extends BaseMapper<SysYooaAttendSchedule> {


    /**
     * 查询用户排班
     */
    public List<AttendClassInfoVo> queryScheduleByUserAndDate(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("userIdList") List<Long> userIdList, @Param("groupId") Long groupId);

    /**
     * 删除用户对应排班日期的排班
     * @param planDate
     * @param userId
     */
    public void deleteScheduleByDateAndUserId(@Param("planDate") Date planDate, @Param("userId") Long userId);
}




