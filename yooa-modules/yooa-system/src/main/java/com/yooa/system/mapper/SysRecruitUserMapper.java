package com.yooa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yooa.system.api.domain.SysRecruit;
import com.yooa.system.api.domain.SysRecruitUser;
import com.yooa.system.api.domain.vo.SysRecruitUserVo;
import com.yooa.system.api.domain.vo.SysRecruitVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 招聘计划 数据层
 *
 * <AUTHOR>
 */
public interface SysRecruitUserMapper extends BaseMapper<SysRecruitUser> {

    /**
     * @return 招聘计划数据集合
     */
    public List<SysRecruitUserVo> getNotPagetUserList(@Param("query") SysRecruitUserVo vp);

}
