<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.system.mapper.SysPostMapper">

    <resultMap type="com.yooa.system.api.domain.SysPost" id="SysPostResult">
        <id property="postId" column="post_id"/>
        <result property="postCode" column="post_code"/>
        <result property="postName" column="post_name"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectPostVo">
        SELECT post_id,
               post_code,
               post_name,
               sort,
               status,
               create_by,
               create_time,
               remark
        FROM sys_post
    </sql>

    <select id="selectPostList" resultMap="SysPostResult">
        <include refid="selectPostVo"/>
        <where>
            <if test="query.postCode != null and query.postCode != ''">
                AND post_code like concat('%', #{query.postCode}, '%')
            </if>
            <if test="query.status != null and query.status != ''">
                AND status = #{query.status}
            </if>
            <if test="query.postName != null and query.postName != ''">
                AND post_name like concat('%', #{query.postName}, '%')
            </if>
        </where>
    </select>

    <select id="selectPostListByUserId" parameterType="Long" resultType="Long">
        SELECT p.post_id
        FROM sys_post p
                 LEFT JOIN sys_user_post up ON up.post_id = p.post_id
                 LEFT JOIN sys_user u ON u.user_id = up.user_id
        WHERE u.user_id = #{userId}
    </select>

    <select id="selectPostsByNickName" parameterType="String" resultMap="SysPostResult">
        SELECT p.post_id, p.post_name, p.post_code
        FROM sys_post p
                 LEFT JOIN sys_user_post up ON up.post_id = p.post_id
                 LEFT JOIN sys_user u ON u.user_id = up.user_id
        WHERE u.nick_name = #{nickName}
    </select>

    <select id="checkPostNameUnique" parameterType="String" resultMap="SysPostResult">
        <include refid="selectPostVo"/>
        WHERE post_name=#{postName} LIMIT 1
    </select>

    <select id="checkPostCodeUnique" parameterType="String" resultMap="SysPostResult">
        <include refid="selectPostVo"/>
        WHERE post_code=#{postCode} LIMIT 1
    </select>

</mapper> 