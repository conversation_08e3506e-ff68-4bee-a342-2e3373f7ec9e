<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.system.mapper.SysRosterMapper">

    <select id="selectRosterList" resultType="com.yooa.system.api.domain.vo.RosterSensitiveVo">
        SELECT  r.roster_id,
                r.real_name,
                r.employee_role,
                r.position_type,
                r.post_name,
                r.post_rank,
                r.employee_status,
                r.educational_type,
                r.id_card_number,
                r.phone,
                r.mail,
                r.probation_period,
                r.employment_date,
                r.resignation_date,
                r.create_time,
                u.user_id,
                u.user_name,
                up.py_ids pdUserIds,
                d.dept_id AS deptId,
                d.dept_name AS deptName,
                d.ancestors_names AS deptAncestorsNames
        FROM sys_roster AS r
        LEFT JOIN sys_dept AS d ON d.dept_id = r.dept_id
        LEFT JOIN sys_user u on r.oa_user_id = u.user_id
        LEFT JOIN
            (
                SELECT user_id,GROUP_CONCAT(pd_user_id) py_ids FROM sys_user_pd GROUP BY user_id
            ) up on u.user_id = up.user_id
        <where>
            <if test="query.realName != null and query.realName != ''">
                AND r.real_name LIKE concat('%',#{query.realName},'%')
            </if>
            <if test="query.educationalType != null and query.educationalType != ''">
                AND r.educational_type = #{query.educationalType}
            </if>
            <if test="query.positionType != null and query.positionType != ''">
                AND r.position_type = #{query.positionType}
            </if>
            <if test="query.postRank != null and query.postRank != ''">
                AND r.post_rank = #{query.postRank}
            </if>
            <if test="query.employeeType != null and query.employeeType != ''">
                AND r.employee_type = #{query.employeeType}
            </if>
            <if test="query.employeeRole != null and query.employeeRole != ''">
                AND r.employee_role = #{query.employeeRole}
            </if>
            <if test="query.employeeStatus != null and query.employeeStatus != ''">
                <if test="query.employeeStatus == 1">
                    AND r.employee_status in ('0','1')
                </if>
                <if test="query.employeeStatus == 2">
                    AND r.employee_status = '2'
                </if>
            </if>
            <if test="query.deptId != null">
                AND (r.dept_id = #{query.deptId} OR FIND_IN_SET(#{query.deptId},d.ancestors))
            </if>
            <if test="query.deptType != null and query.deptType != ''">
                <if test="query.deptType == 8">
                    AND (d.dept_type = '8' OR d.dept_type = '9' OR d.dept_type = '10')
                </if>
                <if test="query.deptType != 8">
                    AND d.dept_type = #{query.deptType}
                </if>
            </if>
            <!-- 数据范围过滤 -->
            ${query.params.dataScope}
        </where>
        ORDER BY r.create_time DESC
    </select>

    <select id="selectRosterById" resultType="com.yooa.system.api.domain.vo.RosterVo">
        SELECT  r.*,
                d.dept_id AS deptId,
                d.dept_name AS deptName,
                d.ancestors_names AS deptAncestorsNames,
                iu.nick_name AS recommenUserName,
                lu.nick_name AS leaderUserName
        FROM sys_roster AS r
        LEFT JOIN sys_dept AS d ON d.dept_id = r.dept_id
        LEFT JOIN sys_user iu on r.recommen_user_id = iu.user_id
        LEFT JOIN sys_user lu on r.leader_user_id = lu.user_id
        WHERE r.roster_id = #{rosterId}
    </select>

    <select id="selectRosterByDeptIdList">
        SELECT  roster_id
        FROM sys_roster
        where employee_status != '2' and roster_type != '2'
        <if test="realName != null and realName != ''">
            and real_name LIKE concat('%',#{realName},'%')
        </if>
        <if test="deptIdList != null and deptIdList.size > 0">
            AND dept_id IN
            <foreach collection="deptIdList" index="index" item="item" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>


    <select id="selectByRealNameAndId">
        SELECT  *
        FROM sys_roster
        where employee_status != '2'
        and roster_id = #{rosterId}
        <if test="realName != null and realName != ''">
            and real_name LIKE concat('%',#{realName},'%')
        </if>
    </select>
    <select id="getRosterByMouthList"  resultType="com.yooa.system.api.domain.vo.SysRosterVo">
            SELECT
            r.real_name,
            i.invite_user_id
            FROM
            sys_roster AS r
            LEFT JOIN sys_interview i on r.interview_id=i.interview_id
             <where>
                 i.invite_user_id = #{query.inviteUerId}
                 <if test="query.employmentDate != null and query.employmentDate != ''">
                     and r.employment_date LIKE concat('%',#{query.employmentDate},'%')
                 </if>
                 <if test="query.planFormalDate != null and query.planFormalDate != ''">
                     and r.plan_formal_date LIKE concat('%',#{query.planFormalDate},'%')
                 </if>
            </where>

    </select>

    <select id="selectRoster"  resultType="com.yooa.system.api.domain.vo.SysRosterVo">
        SELECT
        r.real_name,
        i.invite_user_id
        FROM
        sys_roster AS r
        LEFT JOIN sys_interview i on r.interview_id=i.interview_id
        <where>
            <if test="query.inviteUerId != null and query.inviteUerId != ''">
                AND i.invite_user_id = #{query.inviteUerId}
            </if>
            <if test="query.employmentDate != null and query.employmentDate != ''">
                and r.employment_date LIKE concat('%',#{query.employmentDate},'%')
            </if>
            <if test="query.resignationDate != null and query.resignationDate != ''">
                and r.resignation_date LIKE concat('%',#{query.resignationDate},'%')
            </if>
            <if test="query.planFormalDate != null and query.planFormalDate != ''">
                and r.plan_formal_date LIKE concat('%',#{query.planFormalDate},'%')
            </if>
        </where>
    </select>


    <select id="selectAllRoster" resultType="com.yooa.system.api.domain.vo.SysRosterVo">
        select  roster_id userId,real_name realName,dept_id deptId from sys_roster where employee_status !='2' and roster_type != '2'
    </select>

    <select id="selectAllRosterByDept" resultType="com.yooa.system.api.domain.vo.SysRosterVo">
        select  roster_id userId,real_name realName from sys_roster
        <where>
            employee_status !='2' and roster_type != '2'
            <if test="deptIdList != null and deptIdList.size > 0">
                AND dept_id IN
                <foreach collection="deptIdList" index="index" item="item" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>

    <select id="selectByRosterIdList" resultType="com.yooa.system.api.domain.vo.SysRosterVo">
        select  roster_id userId,real_name realName from sys_roster
        <where>
            employee_status !='2' and roster_type != '2'
            <if test="idList != null and idList.size > 0">
                AND roster_id IN
                <foreach collection="idList" index="index" item="item" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>

    </select>

    <select id="selectThisMonthList" resultType="com.yooa.system.api.domain.vo.RosterVo">
        SELECT * FROM sys_roster  r
        LEFT JOIN  sys_dept d ON r.dept_id = d.dept_id
        Left join sys_interview i on r.interview_id=i.interview_id
        <where>
            <if test="query.inviteUerId != null and query.inviteUerId != ''">
                and i.invite_user_id = #{query.inviteUerId}
            </if>
            <if test="query.employmentDate != null and query.employmentDate != ''">
                and r.employment_date like concat('%', #{query.employmentDate}, '%')
            </if>
            <if test="query.deptId != null and query.deptId != ''">
                AND FIND_IN_SET(#{query.deptId}, ancestors)
            </if>
        </where>

    </select>
</mapper>
