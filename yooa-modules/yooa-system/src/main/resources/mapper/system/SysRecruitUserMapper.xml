<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yooa.system.mapper.SysRecruitUserMapper">

    <resultMap type="com.yooa.system.api.domain.vo.SysRecruitUserVo" id="SysRecruitUserMap">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="userId" column="user_id"/>
        <result property="processId" column="process_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="hireData" column="hire_data"/>
        <result property="commissioner" column="commissioner"/>
        <result property="manage" column="manage"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="SysRecruitUser">
        select * from sys_recruit_user r
    </sql>
    <select id="getNotPagetUserList" resultMap="SysRecruitUserMap">
        <include refid="SysRecruitUser"/>
    <where>
        <if test="query.processId != null and query.processId != 0">
             r.process_id=#{query.processId}
        </if>
    </where>
        order by  r.create_time desc
    </select>

</mapper>
