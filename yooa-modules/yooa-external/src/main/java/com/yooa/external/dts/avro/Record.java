/**
 * Autogenerated by Avro
 * <p>
 * DO NOT EDIT DIRECTLY
 */
package com.yooa.external.dts.avro;

import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.SchemaStore;
import org.apache.avro.specific.SpecificData;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class Record extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
    public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"Record\",\"namespace\":\"com.alibaba.dts.formats.avro\",\"fields\":[{\"name\":\"version\",\"type\":\"int\",\"doc\":\"version infomation\"},{\"name\":\"id\",\"type\":\"long\",\"doc\":\"unique id of this record in the whole stream\"},{\"name\":\"sourceTimestamp\",\"type\":\"long\",\"doc\":\"record log timestamp\"},{\"name\":\"sourcePosition\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"record source location information\"},{\"name\":\"safeSourcePosition\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"safe record source location information, use to recovery.\",\"default\":\"\"},{\"name\":\"sourceTxid\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"record transation id\",\"default\":\"\"},{\"name\":\"source\",\"type\":{\"type\":\"record\",\"name\":\"Source\",\"fields\":[{\"name\":\"sourceType\",\"type\":{\"type\":\"enum\",\"name\":\"SourceType\",\"symbols\":[\"MySQL\",\"Oracle\",\"SQLServer\",\"PostgreSQL\",\"MongoDB\",\"Redis\",\"DB2\",\"PPAS\",\"DRDS\",\"HBASE\",\"HDFS\",\"FILE\",\"OTHER\"]}},{\"name\":\"version\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"doc\":\"source datasource version information\"}]},\"doc\":\"source dataource\"},{\"name\":\"operation\",\"type\":{\"type\":\"enum\",\"name\":\"Operation\",\"symbols\":[\"INSERT\",\"UPDATE\",\"DELETE\",\"DDL\",\"BEGIN\",\"COMMIT\",\"ROLLBACK\",\"ABORT\",\"HEARTBEAT\",\"CHECKPOINT\",\"COMMAND\",\"FILL\",\"FINISH\",\"CONTROL\",\"RDB\",\"NOOP\",\"INIT\"]},\"namespace\":\"com.alibaba.dts.formats.avro\"},{\"name\":\"objectName\",\"type\":[\"null\",{\"type\":\"string\",\"avro.java.string\":\"String\"}],\"default\":null},{\"name\":\"processTimestamps\",\"type\":[\"null\",{\"type\":\"array\",\"items\":\"long\"}],\"doc\":\"time when this record is processed along the stream dataflow\",\"default\":null},{\"name\":\"tags\",\"type\":{\"type\":\"map\",\"values\":{\"type\":\"string\",\"avro.java.string\":\"String\"},\"avro.java.string\":\"String\"},\"doc\":\"tags to identify properties of this record\",\"default\":{}},{\"name\":\"fields\",\"type\":[\"null\",{\"type\":\"string\",\"avro.java.string\":\"String\"},{\"type\":\"array\",\"items\":{\"type\":\"record\",\"name\":\"Field\",\"fields\":[{\"name\":\"name\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"}},{\"name\":\"dataTypeNumber\",\"type\":\"int\"}]}}],\"default\":null},{\"name\":\"beforeImages\",\"type\":[\"null\",{\"type\":\"string\",\"avro.java.string\":\"String\"},{\"type\":\"array\",\"items\":[\"null\",{\"type\":\"record\",\"name\":\"Integer\",\"fields\":[{\"name\":\"precision\",\"type\":\"int\"},{\"name\":\"value\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"}}]},{\"type\":\"record\",\"name\":\"Character\",\"fields\":[{\"name\":\"charset\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"}},{\"name\":\"value\",\"type\":\"bytes\"}]},{\"type\":\"record\",\"name\":\"Decimal\",\"fields\":[{\"name\":\"value\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"}},{\"name\":\"precision\",\"type\":\"int\"},{\"name\":\"scale\",\"type\":\"int\"}]},{\"type\":\"record\",\"name\":\"Float\",\"fields\":[{\"name\":\"value\",\"type\":\"double\"},{\"name\":\"precision\",\"type\":\"int\"},{\"name\":\"scale\",\"type\":\"int\"}]},{\"type\":\"record\",\"name\":\"Timestamp\",\"fields\":[{\"name\":\"timestamp\",\"type\":\"long\"},{\"name\":\"millis\",\"type\":\"int\"}]},{\"type\":\"record\",\"name\":\"DateTime\",\"fields\":[{\"name\":\"year\",\"type\":[\"null\",\"int\"],\"default\":null},{\"name\":\"month\",\"type\":[\"null\",\"int\"],\"default\":null},{\"name\":\"day\",\"type\":[\"null\",\"int\"],\"default\":null},{\"name\":\"hour\",\"type\":[\"null\",\"int\"],\"default\":null},{\"name\":\"minute\",\"type\":[\"null\",\"int\"],\"default\":null},{\"name\":\"second\",\"type\":[\"null\",\"int\"],\"default\":null},{\"name\":\"millis\",\"type\":[\"null\",\"int\"],\"default\":null}]},{\"type\":\"record\",\"name\":\"TimestampWithTimeZone\",\"fields\":[{\"name\":\"value\",\"type\":\"DateTime\"},{\"name\":\"timezone\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"}}]},{\"type\":\"record\",\"name\":\"BinaryGeometry\",\"fields\":[{\"name\":\"type\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"}},{\"name\":\"value\",\"type\":\"bytes\"}]},{\"type\":\"record\",\"name\":\"TextGeometry\",\"fields\":[{\"name\":\"type\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"}},{\"name\":\"value\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"}}]},{\"type\":\"record\",\"name\":\"BinaryObject\",\"fields\":[{\"name\":\"type\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"}},{\"name\":\"value\",\"type\":\"bytes\"}]},{\"type\":\"record\",\"name\":\"TextObject\",\"fields\":[{\"name\":\"type\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"}},{\"name\":\"value\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"}}]},{\"type\":\"enum\",\"name\":\"EmptyObject\",\"symbols\":[\"NULL\",\"NONE\"]}]}],\"default\":null},{\"name\":\"afterImages\",\"type\":[\"null\",{\"type\":\"string\",\"avro.java.string\":\"String\"},{\"type\":\"array\",\"items\":[\"null\",\"Integer\",\"Character\",\"Decimal\",\"Float\",\"Timestamp\",\"DateTime\",\"TimestampWithTimeZone\",\"BinaryGeometry\",\"TextGeometry\",\"BinaryObject\",\"TextObject\",\"EmptyObject\"]}],\"default\":null}]}");
    private static final long serialVersionUID = -266665134822314238L;
    private static SpecificData MODEL$ = new SpecificData();
    private static final BinaryMessageEncoder<Record> ENCODER =
            new BinaryMessageEncoder<Record>(MODEL$, SCHEMA$);
    private static final BinaryMessageDecoder<Record> DECODER =
            new BinaryMessageDecoder<Record>(MODEL$, SCHEMA$);
    @SuppressWarnings("unchecked")
    private static final org.apache.avro.io.DatumWriter<Record>
            WRITER$ = (org.apache.avro.io.DatumWriter<Record>) MODEL$.createDatumWriter(SCHEMA$);
    @SuppressWarnings("unchecked")
    private static final org.apache.avro.io.DatumReader<Record>
            READER$ = (org.apache.avro.io.DatumReader<Record>) MODEL$.createDatumReader(SCHEMA$);
    /**
     * version infomation
     */
    @Deprecated
    public int version;
    /**
     * unique id of this record in the whole stream
     */
    @Deprecated
    public long id;
    /**
     * record log timestamp
     */
    @Deprecated
    public long sourceTimestamp;
    /**
     * record source location information
     */
    @Deprecated
    public String sourcePosition;
    /**
     * safe record source location information, use to recovery.
     */
    @Deprecated
    public String safeSourcePosition;
    /**
     * record transation id
     */
    @Deprecated
    public String sourceTxid;
    /**
     * source dataource
     */
    @Deprecated
    public Source source;
    @Deprecated
    public Operation operation;
    @Deprecated
    public String objectName;
    /**
     * time when this record is processed along the stream dataflow
     */
    @Deprecated
    public java.util.List<Long> processTimestamps;
    /**
     * tags to identify properties of this record
     */
    @Deprecated
    public java.util.Map<String, String> tags;
    @Deprecated
    public Object fields;
    @Deprecated
    public Object beforeImages;
    @Deprecated
    public Object afterImages;

    /**
     * Default constructor.  Note that this does not initialize fields
     * to their default values from the schema.  If that is desired then
     * one should use <code>newBuilder()</code>.
     */
    public Record() {
    }

    /**
     * All-args constructor.
     *
     * @param version            version infomation
     * @param id                 unique id of this record in the whole stream
     * @param sourceTimestamp    record log timestamp
     * @param sourcePosition     record source location information
     * @param safeSourcePosition safe record source location information, use to recovery.
     * @param sourceTxid         record transation id
     * @param source             source dataource
     * @param operation          The new value for operation
     * @param objectName         The new value for objectName
     * @param processTimestamps  time when this record is processed along the stream dataflow
     * @param tags               tags to identify properties of this record
     * @param fields             The new value for fields
     * @param beforeImages       The new value for beforeImages
     * @param afterImages        The new value for afterImages
     */
    public Record(java.lang.Integer version, Long id, Long sourceTimestamp, String sourcePosition, String safeSourcePosition, String sourceTxid, Source source, Operation operation, String objectName, java.util.List<Long> processTimestamps, java.util.Map<String, String> tags, Object fields, Object beforeImages, Object afterImages) {
        this.version = version;
        this.id = id;
        this.sourceTimestamp = sourceTimestamp;
        this.sourcePosition = sourcePosition;
        this.safeSourcePosition = safeSourcePosition;
        this.sourceTxid = sourceTxid;
        this.source = source;
        this.operation = operation;
        this.objectName = objectName;
        this.processTimestamps = processTimestamps;
        this.tags = tags;
        this.fields = fields;
        this.beforeImages = beforeImages;
        this.afterImages = afterImages;
    }

    public static org.apache.avro.Schema getClassSchema() {
        return SCHEMA$;
    }

    /**
     * Return the BinaryMessageDecoder instance used by this class.
     */
    public static BinaryMessageDecoder<Record> getDecoder() {
        return DECODER;
    }

    /**
     * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
     *
     * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
     */
    public static BinaryMessageDecoder<Record> createDecoder(SchemaStore resolver) {
        return new BinaryMessageDecoder<Record>(MODEL$, SCHEMA$, resolver);
    }

    /**
     * Deserializes a Record from a ByteBuffer.
     */
    public static Record fromByteBuffer(
            java.nio.ByteBuffer b) throws java.io.IOException {
        return DECODER.decode(b);
    }

    /**
     * Creates a new Record RecordBuilder.
     *
     * @return A new Record RecordBuilder
     */
    public static Record.Builder newBuilder() {
        return new Record.Builder();
    }

    /**
     * Creates a new Record RecordBuilder by copying an existing Builder.
     *
     * @param other The existing builder to copy.
     * @return A new Record RecordBuilder
     */
    public static Record.Builder newBuilder(Record.Builder other) {
        return new Record.Builder(other);
    }

    /**
     * Creates a new Record RecordBuilder by copying an existing Record instance.
     *
     * @param other The existing instance to copy.
     * @return A new Record RecordBuilder
     */
    public static Record.Builder newBuilder(Record other) {
        return new Record.Builder(other);
    }

    /**
     * Serializes this Record to a ByteBuffer.
     */
    public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
        return ENCODER.encode(this);
    }

    public org.apache.avro.Schema getSchema() {
        return SCHEMA$;
    }

    // Used by DatumWriter.  Applications should not call.
    public Object get(int field$) {
        switch (field$) {
            case 0:
                return version;
            case 1:
                return id;
            case 2:
                return sourceTimestamp;
            case 3:
                return sourcePosition;
            case 4:
                return safeSourcePosition;
            case 5:
                return sourceTxid;
            case 6:
                return source;
            case 7:
                return operation;
            case 8:
                return objectName;
            case 9:
                return processTimestamps;
            case 10:
                return tags;
            case 11:
                return fields;
            case 12:
                return beforeImages;
            case 13:
                return afterImages;
            default:
                throw new org.apache.avro.AvroRuntimeException("Bad index");
        }
    }

    // Used by DatumReader.  Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int field$, Object value$) {
        switch (field$) {
            case 0:
                version = (java.lang.Integer) value$;
                break;
            case 1:
                id = (Long) value$;
                break;
            case 2:
                sourceTimestamp = (Long) value$;
                break;
            case 3:
                sourcePosition = (String) value$;
                break;
            case 4:
                safeSourcePosition = (String) value$;
                break;
            case 5:
                sourceTxid = (String) value$;
                break;
            case 6:
                source = (Source) value$;
                break;
            case 7:
                operation = (Operation) value$;
                break;
            case 8:
                objectName = (String) value$;
                break;
            case 9:
                processTimestamps = (java.util.List<Long>) value$;
                break;
            case 10:
                tags = (java.util.Map<String, String>) value$;
                break;
            case 11:
                fields = (Object) value$;
                break;
            case 12:
                beforeImages = (Object) value$;
                break;
            case 13:
                afterImages = (Object) value$;
                break;
            default:
                throw new org.apache.avro.AvroRuntimeException("Bad index");
        }
    }

    /**
     * Gets the value of the 'version' field.
     *
     * @return version infomation
     */
    public java.lang.Integer getVersion() {
        return version;
    }

    /**
     * Sets the value of the 'version' field.
     * version infomation
     *
     * @param value the value to set.
     */
    public void setVersion(java.lang.Integer value) {
        this.version = value;
    }

    /**
     * Gets the value of the 'id' field.
     *
     * @return unique id of this record in the whole stream
     */
    public Long getId() {
        return id;
    }

    /**
     * Sets the value of the 'id' field.
     * unique id of this record in the whole stream
     *
     * @param value the value to set.
     */
    public void setId(Long value) {
        this.id = value;
    }

    /**
     * Gets the value of the 'sourceTimestamp' field.
     *
     * @return record log timestamp
     */
    public Long getSourceTimestamp() {
        return sourceTimestamp;
    }

    /**
     * Sets the value of the 'sourceTimestamp' field.
     * record log timestamp
     *
     * @param value the value to set.
     */
    public void setSourceTimestamp(Long value) {
        this.sourceTimestamp = value;
    }

    /**
     * Gets the value of the 'sourcePosition' field.
     *
     * @return record source location information
     */
    public String getSourcePosition() {
        return sourcePosition;
    }

    /**
     * Sets the value of the 'sourcePosition' field.
     * record source location information
     *
     * @param value the value to set.
     */
    public void setSourcePosition(String value) {
        this.sourcePosition = value;
    }

    /**
     * Gets the value of the 'safeSourcePosition' field.
     *
     * @return safe record source location information, use to recovery.
     */
    public String getSafeSourcePosition() {
        return safeSourcePosition;
    }

    /**
     * Sets the value of the 'safeSourcePosition' field.
     * safe record source location information, use to recovery.
     *
     * @param value the value to set.
     */
    public void setSafeSourcePosition(String value) {
        this.safeSourcePosition = value;
    }

    /**
     * Gets the value of the 'sourceTxid' field.
     *
     * @return record transation id
     */
    public String getSourceTxid() {
        return sourceTxid;
    }

    /**
     * Sets the value of the 'sourceTxid' field.
     * record transation id
     *
     * @param value the value to set.
     */
    public void setSourceTxid(String value) {
        this.sourceTxid = value;
    }

    /**
     * Gets the value of the 'source' field.
     *
     * @return source dataource
     */
    public Source getSource() {
        return source;
    }

    /**
     * Sets the value of the 'source' field.
     * source dataource
     *
     * @param value the value to set.
     */
    public void setSource(Source value) {
        this.source = value;
    }

    /**
     * Gets the value of the 'operation' field.
     *
     * @return The value of the 'operation' field.
     */
    public Operation getOperation() {
        return operation;
    }

    /**
     * Sets the value of the 'operation' field.
     *
     * @param value the value to set.
     */
    public void setOperation(Operation value) {
        this.operation = value;
    }

    /**
     * Gets the value of the 'objectName' field.
     *
     * @return The value of the 'objectName' field.
     */
    public String getObjectName() {
        return objectName;
    }

    /**
     * Sets the value of the 'objectName' field.
     *
     * @param value the value to set.
     */
    public void setObjectName(String value) {
        this.objectName = value;
    }

    /**
     * Gets the value of the 'processTimestamps' field.
     *
     * @return time when this record is processed along the stream dataflow
     */
    public java.util.List<Long> getProcessTimestamps() {
        return processTimestamps;
    }

    /**
     * Sets the value of the 'processTimestamps' field.
     * time when this record is processed along the stream dataflow
     *
     * @param value the value to set.
     */
    public void setProcessTimestamps(java.util.List<Long> value) {
        this.processTimestamps = value;
    }

    /**
     * Gets the value of the 'tags' field.
     *
     * @return tags to identify properties of this record
     */
    public java.util.Map<String, String> getTags() {
        return tags;
    }

    /**
     * Sets the value of the 'tags' field.
     * tags to identify properties of this record
     *
     * @param value the value to set.
     */
    public void setTags(java.util.Map<String, String> value) {
        this.tags = value;
    }

    /**
     * Gets the value of the 'fields' field.
     *
     * @return The value of the 'fields' field.
     */
    public Object getFields() {
        return fields;
    }

    /**
     * Sets the value of the 'fields' field.
     *
     * @param value the value to set.
     */
    public void setFields(Object value) {
        this.fields = value;
    }

    /**
     * Gets the value of the 'beforeImages' field.
     *
     * @return The value of the 'beforeImages' field.
     */
    public Object getBeforeImages() {
        return beforeImages;
    }

    /**
     * Sets the value of the 'beforeImages' field.
     *
     * @param value the value to set.
     */
    public void setBeforeImages(Object value) {
        this.beforeImages = value;
    }

    /**
     * Gets the value of the 'afterImages' field.
     *
     * @return The value of the 'afterImages' field.
     */
    public Object getAfterImages() {
        return afterImages;
    }

    /**
     * Sets the value of the 'afterImages' field.
     *
     * @param value the value to set.
     */
    public void setAfterImages(Object value) {
        this.afterImages = value;
    }

    @Override
    public void writeExternal(java.io.ObjectOutput out)
            throws java.io.IOException {
        WRITER$.write(this, SpecificData.getEncoder(out));
    }

    @Override
    public void readExternal(java.io.ObjectInput in)
            throws java.io.IOException {
        READER$.read(this, SpecificData.getDecoder(in));
    }

    /**
     * RecordBuilder for Record instances.
     */
    public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<Record>
            implements org.apache.avro.data.RecordBuilder<Record> {

        /**
         * version infomation
         */
        private int version;
        /**
         * unique id of this record in the whole stream
         */
        private long id;
        /**
         * record log timestamp
         */
        private long sourceTimestamp;
        /**
         * record source location information
         */
        private String sourcePosition;
        /**
         * safe record source location information, use to recovery.
         */
        private String safeSourcePosition;
        /**
         * record transation id
         */
        private String sourceTxid;
        /**
         * source dataource
         */
        private Source source;
        private Source.Builder sourceBuilder;
        private Operation operation;
        private String objectName;
        /**
         * time when this record is processed along the stream dataflow
         */
        private java.util.List<Long> processTimestamps;
        /**
         * tags to identify properties of this record
         */
        private java.util.Map<String, String> tags;
        private Object fields;
        private Object beforeImages;
        private Object afterImages;

        /**
         * Creates a new Builder
         */
        private Builder() {
            super(SCHEMA$);
        }

        /**
         * Creates a Builder by copying an existing Builder.
         *
         * @param other The existing Builder to copy.
         */
        private Builder(Record.Builder other) {
            super(other);
            if (isValidValue(fields()[0], other.version)) {
                this.version = data().deepCopy(fields()[0].schema(), other.version);
                fieldSetFlags()[0] = true;
            }
            if (isValidValue(fields()[1], other.id)) {
                this.id = data().deepCopy(fields()[1].schema(), other.id);
                fieldSetFlags()[1] = true;
            }
            if (isValidValue(fields()[2], other.sourceTimestamp)) {
                this.sourceTimestamp = data().deepCopy(fields()[2].schema(), other.sourceTimestamp);
                fieldSetFlags()[2] = true;
            }
            if (isValidValue(fields()[3], other.sourcePosition)) {
                this.sourcePosition = data().deepCopy(fields()[3].schema(), other.sourcePosition);
                fieldSetFlags()[3] = true;
            }
            if (isValidValue(fields()[4], other.safeSourcePosition)) {
                this.safeSourcePosition = data().deepCopy(fields()[4].schema(), other.safeSourcePosition);
                fieldSetFlags()[4] = true;
            }
            if (isValidValue(fields()[5], other.sourceTxid)) {
                this.sourceTxid = data().deepCopy(fields()[5].schema(), other.sourceTxid);
                fieldSetFlags()[5] = true;
            }
            if (isValidValue(fields()[6], other.source)) {
                this.source = data().deepCopy(fields()[6].schema(), other.source);
                fieldSetFlags()[6] = true;
            }
            if (other.hasSourceBuilder()) {
                this.sourceBuilder = Source.newBuilder(other.getSourceBuilder());
            }
            if (isValidValue(fields()[7], other.operation)) {
                this.operation = data().deepCopy(fields()[7].schema(), other.operation);
                fieldSetFlags()[7] = true;
            }
            if (isValidValue(fields()[8], other.objectName)) {
                this.objectName = data().deepCopy(fields()[8].schema(), other.objectName);
                fieldSetFlags()[8] = true;
            }
            if (isValidValue(fields()[9], other.processTimestamps)) {
                this.processTimestamps = data().deepCopy(fields()[9].schema(), other.processTimestamps);
                fieldSetFlags()[9] = true;
            }
            if (isValidValue(fields()[10], other.tags)) {
                this.tags = data().deepCopy(fields()[10].schema(), other.tags);
                fieldSetFlags()[10] = true;
            }
            if (isValidValue(fields()[11], other.fields)) {
                this.fields = data().deepCopy(fields()[11].schema(), other.fields);
                fieldSetFlags()[11] = true;
            }
            if (isValidValue(fields()[12], other.beforeImages)) {
                this.beforeImages = data().deepCopy(fields()[12].schema(), other.beforeImages);
                fieldSetFlags()[12] = true;
            }
            if (isValidValue(fields()[13], other.afterImages)) {
                this.afterImages = data().deepCopy(fields()[13].schema(), other.afterImages);
                fieldSetFlags()[13] = true;
            }
        }

        /**
         * Creates a Builder by copying an existing Record instance
         *
         * @param other The existing instance to copy.
         */
        private Builder(Record other) {
            super(SCHEMA$);
            if (isValidValue(fields()[0], other.version)) {
                this.version = data().deepCopy(fields()[0].schema(), other.version);
                fieldSetFlags()[0] = true;
            }
            if (isValidValue(fields()[1], other.id)) {
                this.id = data().deepCopy(fields()[1].schema(), other.id);
                fieldSetFlags()[1] = true;
            }
            if (isValidValue(fields()[2], other.sourceTimestamp)) {
                this.sourceTimestamp = data().deepCopy(fields()[2].schema(), other.sourceTimestamp);
                fieldSetFlags()[2] = true;
            }
            if (isValidValue(fields()[3], other.sourcePosition)) {
                this.sourcePosition = data().deepCopy(fields()[3].schema(), other.sourcePosition);
                fieldSetFlags()[3] = true;
            }
            if (isValidValue(fields()[4], other.safeSourcePosition)) {
                this.safeSourcePosition = data().deepCopy(fields()[4].schema(), other.safeSourcePosition);
                fieldSetFlags()[4] = true;
            }
            if (isValidValue(fields()[5], other.sourceTxid)) {
                this.sourceTxid = data().deepCopy(fields()[5].schema(), other.sourceTxid);
                fieldSetFlags()[5] = true;
            }
            if (isValidValue(fields()[6], other.source)) {
                this.source = data().deepCopy(fields()[6].schema(), other.source);
                fieldSetFlags()[6] = true;
            }
            this.sourceBuilder = null;
            if (isValidValue(fields()[7], other.operation)) {
                this.operation = data().deepCopy(fields()[7].schema(), other.operation);
                fieldSetFlags()[7] = true;
            }
            if (isValidValue(fields()[8], other.objectName)) {
                this.objectName = data().deepCopy(fields()[8].schema(), other.objectName);
                fieldSetFlags()[8] = true;
            }
            if (isValidValue(fields()[9], other.processTimestamps)) {
                this.processTimestamps = data().deepCopy(fields()[9].schema(), other.processTimestamps);
                fieldSetFlags()[9] = true;
            }
            if (isValidValue(fields()[10], other.tags)) {
                this.tags = data().deepCopy(fields()[10].schema(), other.tags);
                fieldSetFlags()[10] = true;
            }
            if (isValidValue(fields()[11], other.fields)) {
                this.fields = data().deepCopy(fields()[11].schema(), other.fields);
                fieldSetFlags()[11] = true;
            }
            if (isValidValue(fields()[12], other.beforeImages)) {
                this.beforeImages = data().deepCopy(fields()[12].schema(), other.beforeImages);
                fieldSetFlags()[12] = true;
            }
            if (isValidValue(fields()[13], other.afterImages)) {
                this.afterImages = data().deepCopy(fields()[13].schema(), other.afterImages);
                fieldSetFlags()[13] = true;
            }
        }

        /**
         * Gets the value of the 'version' field.
         * version infomation
         *
         * @return The value.
         */
        public java.lang.Integer getVersion() {
            return version;
        }

        /**
         * Sets the value of the 'version' field.
         * version infomation
         *
         * @param value The value of 'version'.
         * @return This builder.
         */
        public Record.Builder setVersion(int value) {
            validate(fields()[0], value);
            this.version = value;
            fieldSetFlags()[0] = true;
            return this;
        }

        /**
         * Checks whether the 'version' field has been set.
         * version infomation
         *
         * @return True if the 'version' field has been set, false otherwise.
         */
        public boolean hasVersion() {
            return fieldSetFlags()[0];
        }


        /**
         * Clears the value of the 'version' field.
         * version infomation
         *
         * @return This builder.
         */
        public Record.Builder clearVersion() {
            fieldSetFlags()[0] = false;
            return this;
        }

        /**
         * Gets the value of the 'id' field.
         * unique id of this record in the whole stream
         *
         * @return The value.
         */
        public Long getId() {
            return id;
        }

        /**
         * Sets the value of the 'id' field.
         * unique id of this record in the whole stream
         *
         * @param value The value of 'id'.
         * @return This builder.
         */
        public Record.Builder setId(long value) {
            validate(fields()[1], value);
            this.id = value;
            fieldSetFlags()[1] = true;
            return this;
        }

        /**
         * Checks whether the 'id' field has been set.
         * unique id of this record in the whole stream
         *
         * @return True if the 'id' field has been set, false otherwise.
         */
        public boolean hasId() {
            return fieldSetFlags()[1];
        }


        /**
         * Clears the value of the 'id' field.
         * unique id of this record in the whole stream
         *
         * @return This builder.
         */
        public Record.Builder clearId() {
            fieldSetFlags()[1] = false;
            return this;
        }

        /**
         * Gets the value of the 'sourceTimestamp' field.
         * record log timestamp
         *
         * @return The value.
         */
        public Long getSourceTimestamp() {
            return sourceTimestamp;
        }

        /**
         * Sets the value of the 'sourceTimestamp' field.
         * record log timestamp
         *
         * @param value The value of 'sourceTimestamp'.
         * @return This builder.
         */
        public Record.Builder setSourceTimestamp(long value) {
            validate(fields()[2], value);
            this.sourceTimestamp = value;
            fieldSetFlags()[2] = true;
            return this;
        }

        /**
         * Checks whether the 'sourceTimestamp' field has been set.
         * record log timestamp
         *
         * @return True if the 'sourceTimestamp' field has been set, false otherwise.
         */
        public boolean hasSourceTimestamp() {
            return fieldSetFlags()[2];
        }


        /**
         * Clears the value of the 'sourceTimestamp' field.
         * record log timestamp
         *
         * @return This builder.
         */
        public Record.Builder clearSourceTimestamp() {
            fieldSetFlags()[2] = false;
            return this;
        }

        /**
         * Gets the value of the 'sourcePosition' field.
         * record source location information
         *
         * @return The value.
         */
        public String getSourcePosition() {
            return sourcePosition;
        }

        /**
         * Sets the value of the 'sourcePosition' field.
         * record source location information
         *
         * @param value The value of 'sourcePosition'.
         * @return This builder.
         */
        public Record.Builder setSourcePosition(String value) {
            validate(fields()[3], value);
            this.sourcePosition = value;
            fieldSetFlags()[3] = true;
            return this;
        }

        /**
         * Checks whether the 'sourcePosition' field has been set.
         * record source location information
         *
         * @return True if the 'sourcePosition' field has been set, false otherwise.
         */
        public boolean hasSourcePosition() {
            return fieldSetFlags()[3];
        }


        /**
         * Clears the value of the 'sourcePosition' field.
         * record source location information
         *
         * @return This builder.
         */
        public Record.Builder clearSourcePosition() {
            sourcePosition = null;
            fieldSetFlags()[3] = false;
            return this;
        }

        /**
         * Gets the value of the 'safeSourcePosition' field.
         * safe record source location information, use to recovery.
         *
         * @return The value.
         */
        public String getSafeSourcePosition() {
            return safeSourcePosition;
        }

        /**
         * Sets the value of the 'safeSourcePosition' field.
         * safe record source location information, use to recovery.
         *
         * @param value The value of 'safeSourcePosition'.
         * @return This builder.
         */
        public Record.Builder setSafeSourcePosition(String value) {
            validate(fields()[4], value);
            this.safeSourcePosition = value;
            fieldSetFlags()[4] = true;
            return this;
        }

        /**
         * Checks whether the 'safeSourcePosition' field has been set.
         * safe record source location information, use to recovery.
         *
         * @return True if the 'safeSourcePosition' field has been set, false otherwise.
         */
        public boolean hasSafeSourcePosition() {
            return fieldSetFlags()[4];
        }


        /**
         * Clears the value of the 'safeSourcePosition' field.
         * safe record source location information, use to recovery.
         *
         * @return This builder.
         */
        public Record.Builder clearSafeSourcePosition() {
            safeSourcePosition = null;
            fieldSetFlags()[4] = false;
            return this;
        }

        /**
         * Gets the value of the 'sourceTxid' field.
         * record transation id
         *
         * @return The value.
         */
        public String getSourceTxid() {
            return sourceTxid;
        }

        /**
         * Sets the value of the 'sourceTxid' field.
         * record transation id
         *
         * @param value The value of 'sourceTxid'.
         * @return This builder.
         */
        public Record.Builder setSourceTxid(String value) {
            validate(fields()[5], value);
            this.sourceTxid = value;
            fieldSetFlags()[5] = true;
            return this;
        }

        /**
         * Checks whether the 'sourceTxid' field has been set.
         * record transation id
         *
         * @return True if the 'sourceTxid' field has been set, false otherwise.
         */
        public boolean hasSourceTxid() {
            return fieldSetFlags()[5];
        }


        /**
         * Clears the value of the 'sourceTxid' field.
         * record transation id
         *
         * @return This builder.
         */
        public Record.Builder clearSourceTxid() {
            sourceTxid = null;
            fieldSetFlags()[5] = false;
            return this;
        }

        /**
         * Gets the value of the 'source' field.
         * source dataource
         *
         * @return The value.
         */
        public Source getSource() {
            return source;
        }

        /**
         * Sets the value of the 'source' field.
         * source dataource
         *
         * @param value The value of 'source'.
         * @return This builder.
         */
        public Record.Builder setSource(Source value) {
            validate(fields()[6], value);
            this.sourceBuilder = null;
            this.source = value;
            fieldSetFlags()[6] = true;
            return this;
        }

        /**
         * Checks whether the 'source' field has been set.
         * source dataource
         *
         * @return True if the 'source' field has been set, false otherwise.
         */
        public boolean hasSource() {
            return fieldSetFlags()[6];
        }

        /**
         * Gets the Builder instance for the 'source' field and creates one if it doesn't exist yet.
         * source dataource
         *
         * @return This builder.
         */
        public Source.Builder getSourceBuilder() {
            if (sourceBuilder == null) {
                if (hasSource()) {
                    setSourceBuilder(Source.newBuilder(source));
                }
                else {
                    setSourceBuilder(Source.newBuilder());
                }
            }
            return sourceBuilder;
        }

        /**
         * Sets the Builder instance for the 'source' field
         * source dataource
         *
         * @param value The builder instance that must be set.
         * @return This builder.
         */
        public Record.Builder setSourceBuilder(Source.Builder value) {
            clearSource();
            sourceBuilder = value;
            return this;
        }

        /**
         * Checks whether the 'source' field has an active Builder instance
         * source dataource
         *
         * @return True if the 'source' field has an active Builder instance
         */
        public boolean hasSourceBuilder() {
            return sourceBuilder != null;
        }

        /**
         * Clears the value of the 'source' field.
         * source dataource
         *
         * @return This builder.
         */
        public Record.Builder clearSource() {
            source = null;
            sourceBuilder = null;
            fieldSetFlags()[6] = false;
            return this;
        }

        /**
         * Gets the value of the 'operation' field.
         *
         * @return The value.
         */
        public Operation getOperation() {
            return operation;
        }

        /**
         * Sets the value of the 'operation' field.
         *
         * @param value The value of 'operation'.
         * @return This builder.
         */
        public Record.Builder setOperation(Operation value) {
            validate(fields()[7], value);
            this.operation = value;
            fieldSetFlags()[7] = true;
            return this;
        }

        /**
         * Checks whether the 'operation' field has been set.
         *
         * @return True if the 'operation' field has been set, false otherwise.
         */
        public boolean hasOperation() {
            return fieldSetFlags()[7];
        }


        /**
         * Clears the value of the 'operation' field.
         *
         * @return This builder.
         */
        public Record.Builder clearOperation() {
            operation = null;
            fieldSetFlags()[7] = false;
            return this;
        }

        /**
         * Gets the value of the 'objectName' field.
         *
         * @return The value.
         */
        public String getObjectName() {
            return objectName;
        }

        /**
         * Sets the value of the 'objectName' field.
         *
         * @param value The value of 'objectName'.
         * @return This builder.
         */
        public Record.Builder setObjectName(String value) {
            validate(fields()[8], value);
            this.objectName = value;
            fieldSetFlags()[8] = true;
            return this;
        }

        /**
         * Checks whether the 'objectName' field has been set.
         *
         * @return True if the 'objectName' field has been set, false otherwise.
         */
        public boolean hasObjectName() {
            return fieldSetFlags()[8];
        }


        /**
         * Clears the value of the 'objectName' field.
         *
         * @return This builder.
         */
        public Record.Builder clearObjectName() {
            objectName = null;
            fieldSetFlags()[8] = false;
            return this;
        }

        /**
         * Gets the value of the 'processTimestamps' field.
         * time when this record is processed along the stream dataflow
         *
         * @return The value.
         */
        public java.util.List<Long> getProcessTimestamps() {
            return processTimestamps;
        }

        /**
         * Sets the value of the 'processTimestamps' field.
         * time when this record is processed along the stream dataflow
         *
         * @param value The value of 'processTimestamps'.
         * @return This builder.
         */
        public Record.Builder setProcessTimestamps(java.util.List<Long> value) {
            validate(fields()[9], value);
            this.processTimestamps = value;
            fieldSetFlags()[9] = true;
            return this;
        }

        /**
         * Checks whether the 'processTimestamps' field has been set.
         * time when this record is processed along the stream dataflow
         *
         * @return True if the 'processTimestamps' field has been set, false otherwise.
         */
        public boolean hasProcessTimestamps() {
            return fieldSetFlags()[9];
        }


        /**
         * Clears the value of the 'processTimestamps' field.
         * time when this record is processed along the stream dataflow
         *
         * @return This builder.
         */
        public Record.Builder clearProcessTimestamps() {
            processTimestamps = null;
            fieldSetFlags()[9] = false;
            return this;
        }

        /**
         * Gets the value of the 'tags' field.
         * tags to identify properties of this record
         *
         * @return The value.
         */
        public java.util.Map<String, String> getTags() {
            return tags;
        }

        /**
         * Sets the value of the 'tags' field.
         * tags to identify properties of this record
         *
         * @param value The value of 'tags'.
         * @return This builder.
         */
        public Record.Builder setTags(java.util.Map<String, String> value) {
            validate(fields()[10], value);
            this.tags = value;
            fieldSetFlags()[10] = true;
            return this;
        }

        /**
         * Checks whether the 'tags' field has been set.
         * tags to identify properties of this record
         *
         * @return True if the 'tags' field has been set, false otherwise.
         */
        public boolean hasTags() {
            return fieldSetFlags()[10];
        }


        /**
         * Clears the value of the 'tags' field.
         * tags to identify properties of this record
         *
         * @return This builder.
         */
        public Record.Builder clearTags() {
            tags = null;
            fieldSetFlags()[10] = false;
            return this;
        }

        /**
         * Gets the value of the 'fields' field.
         *
         * @return The value.
         */
        public Object getFields() {
            return fields;
        }

        /**
         * Sets the value of the 'fields' field.
         *
         * @param value The value of 'fields'.
         * @return This builder.
         */
        public Record.Builder setFields(Object value) {
            validate(fields()[11], value);
            this.fields = value;
            fieldSetFlags()[11] = true;
            return this;
        }

        /**
         * Checks whether the 'fields' field has been set.
         *
         * @return True if the 'fields' field has been set, false otherwise.
         */
        public boolean hasFields() {
            return fieldSetFlags()[11];
        }


        /**
         * Clears the value of the 'fields' field.
         *
         * @return This builder.
         */
        public Record.Builder clearFields() {
            fields = null;
            fieldSetFlags()[11] = false;
            return this;
        }

        /**
         * Gets the value of the 'beforeImages' field.
         *
         * @return The value.
         */
        public Object getBeforeImages() {
            return beforeImages;
        }

        /**
         * Sets the value of the 'beforeImages' field.
         *
         * @param value The value of 'beforeImages'.
         * @return This builder.
         */
        public Record.Builder setBeforeImages(Object value) {
            validate(fields()[12], value);
            this.beforeImages = value;
            fieldSetFlags()[12] = true;
            return this;
        }

        /**
         * Checks whether the 'beforeImages' field has been set.
         *
         * @return True if the 'beforeImages' field has been set, false otherwise.
         */
        public boolean hasBeforeImages() {
            return fieldSetFlags()[12];
        }


        /**
         * Clears the value of the 'beforeImages' field.
         *
         * @return This builder.
         */
        public Record.Builder clearBeforeImages() {
            beforeImages = null;
            fieldSetFlags()[12] = false;
            return this;
        }

        /**
         * Gets the value of the 'afterImages' field.
         *
         * @return The value.
         */
        public Object getAfterImages() {
            return afterImages;
        }

        /**
         * Sets the value of the 'afterImages' field.
         *
         * @param value The value of 'afterImages'.
         * @return This builder.
         */
        public Record.Builder setAfterImages(Object value) {
            validate(fields()[13], value);
            this.afterImages = value;
            fieldSetFlags()[13] = true;
            return this;
        }

        /**
         * Checks whether the 'afterImages' field has been set.
         *
         * @return True if the 'afterImages' field has been set, false otherwise.
         */
        public boolean hasAfterImages() {
            return fieldSetFlags()[13];
        }


        /**
         * Clears the value of the 'afterImages' field.
         *
         * @return This builder.
         */
        public Record.Builder clearAfterImages() {
            afterImages = null;
            fieldSetFlags()[13] = false;
            return this;
        }

        @Override
        @SuppressWarnings("unchecked")
        public Record build() {
            try {
                Record record = new Record();
                record.version = fieldSetFlags()[0] ? this.version : (java.lang.Integer) defaultValue(fields()[0]);
                record.id = fieldSetFlags()[1] ? this.id : (Long) defaultValue(fields()[1]);
                record.sourceTimestamp = fieldSetFlags()[2] ? this.sourceTimestamp : (Long) defaultValue(fields()[2]);
                record.sourcePosition = fieldSetFlags()[3] ? this.sourcePosition : (String) defaultValue(fields()[3]);
                record.safeSourcePosition = fieldSetFlags()[4] ? this.safeSourcePosition : (String) defaultValue(fields()[4]);
                record.sourceTxid = fieldSetFlags()[5] ? this.sourceTxid : (String) defaultValue(fields()[5]);
                if (sourceBuilder != null) {
                    record.source = this.sourceBuilder.build();
                }
                else {
                    record.source = fieldSetFlags()[6] ? this.source : (Source) defaultValue(fields()[6]);
                }
                record.operation = fieldSetFlags()[7] ? this.operation : (Operation) defaultValue(fields()[7]);
                record.objectName = fieldSetFlags()[8] ? this.objectName : (String) defaultValue(fields()[8]);
                record.processTimestamps = fieldSetFlags()[9] ? this.processTimestamps : (java.util.List<Long>) defaultValue(fields()[9]);
                record.tags = fieldSetFlags()[10] ? this.tags : (java.util.Map<String, String>) defaultValue(fields()[10]);
                record.fields = fieldSetFlags()[11] ? this.fields : (Object) defaultValue(fields()[11]);
                record.beforeImages = fieldSetFlags()[12] ? this.beforeImages : (Object) defaultValue(fields()[12]);
                record.afterImages = fieldSetFlags()[13] ? this.afterImages : (Object) defaultValue(fields()[13]);
                return record;
            }
            catch (Exception e) {
                throw new org.apache.avro.AvroRuntimeException(e);
            }
        }
    }

}
