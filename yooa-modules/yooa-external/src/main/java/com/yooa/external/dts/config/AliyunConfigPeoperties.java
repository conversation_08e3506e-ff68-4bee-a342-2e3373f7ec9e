package com.yooa.external.dts.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties("aliyun.subscribe.config")
public class AliyunConfigPeoperties {

    /**
     * 消费组的账号
     **/
    private String user_name;

    /**
     * 该账号的密码
     **/
    private String password;

    /**
     * 消费组ID
     **/
    private String sid_name;

    /**
     * 消费组名称，需保持和消费组ID相同（即本参数也填入消费组ID）
     **/
    private String group_name;

    /**
     * 数据订阅通道的订阅Topic
     **/
    private String kafka_topic;

    /**
     * 数据订阅通道的网络地址信息
     **/
    private String kafka_broker_url_name;

    /**
     * 默认取值为true，即强制使用指定的数据时间点来消费数据，避免丢失已接收到的但未处理的数据
     **/
    private String use_config_checkpoint_name;

    /**
     * 一个消费组下支持同时启动两个及以上Kafka客户端，如需实现该功能，请将所有客户端该参数的值设置为subscribe。
     * 默认值为assign，即不使用该功能，建议只部署一个客户端
     **/
    private String subscribe_mode_name;

    /**
     * 消费的数据时间点，格式为Unix时间戳，例如1592269238。
     **/
    private String initial_checkpoint_name;


}
