/**
 * Autogenerated by Avro
 * <p>
 * DO NOT EDIT DIRECTLY
 */
package com.yooa.external.dts.avro;

import org.apache.avro.message.BinaryMessageDecoder;
import org.apache.avro.message.BinaryMessageEncoder;
import org.apache.avro.message.SchemaStore;
import org.apache.avro.specific.SpecificData;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public class Character extends org.apache.avro.specific.SpecificRecordBase implements org.apache.avro.specific.SpecificRecord {
    public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"record\",\"name\":\"Character\",\"namespace\":\"com.alibaba.dts.formats.avro\",\"fields\":[{\"name\":\"charset\",\"type\":{\"type\":\"string\",\"avro.java.string\":\"String\"}},{\"name\":\"value\",\"type\":\"bytes\"}]}");
    private static final long serialVersionUID = -1775377444003683903L;
    private static SpecificData MODEL$ = new SpecificData();
    private static final BinaryMessageEncoder<Character> ENCODER =
            new BinaryMessageEncoder<Character>(MODEL$, SCHEMA$);
    private static final BinaryMessageDecoder<Character> DECODER =
            new BinaryMessageDecoder<Character>(MODEL$, SCHEMA$);
    @SuppressWarnings("unchecked")
    private static final org.apache.avro.io.DatumWriter<Character>
            WRITER$ = (org.apache.avro.io.DatumWriter<Character>) MODEL$.createDatumWriter(SCHEMA$);
    @SuppressWarnings("unchecked")
    private static final org.apache.avro.io.DatumReader<Character>
            READER$ = (org.apache.avro.io.DatumReader<Character>) MODEL$.createDatumReader(SCHEMA$);
    @Deprecated
    public String charset;
    @Deprecated
    public java.nio.ByteBuffer value;

    /**
     * Default constructor.  Note that this does not initialize fields
     * to their default values from the schema.  If that is desired then
     * one should use <code>newBuilder()</code>.
     */
    public Character() {
    }

    /**
     * All-args constructor.
     *
     * @param charset The new value for charset
     * @param value   The new value for value
     */
    public Character(String charset, java.nio.ByteBuffer value) {
        this.charset = charset;
        this.value = value;
    }

    public static org.apache.avro.Schema getClassSchema() {
        return SCHEMA$;
    }

    /**
     * Return the BinaryMessageDecoder instance used by this class.
     */
    public static BinaryMessageDecoder<Character> getDecoder() {
        return DECODER;
    }

    /**
     * Create a new BinaryMessageDecoder instance for this class that uses the specified {@link SchemaStore}.
     *
     * @param resolver a {@link SchemaStore} used to find schemas by fingerprint
     */
    public static BinaryMessageDecoder<Character> createDecoder(SchemaStore resolver) {
        return new BinaryMessageDecoder<Character>(MODEL$, SCHEMA$, resolver);
    }

    /**
     * Deserializes a Character from a ByteBuffer.
     */
    public static Character fromByteBuffer(
            java.nio.ByteBuffer b) throws java.io.IOException {
        return DECODER.decode(b);
    }

    /**
     * Creates a new Character RecordBuilder.
     *
     * @return A new Character RecordBuilder
     */
    public static Character.Builder newBuilder() {
        return new Character.Builder();
    }

    /**
     * Creates a new Character RecordBuilder by copying an existing Builder.
     *
     * @param other The existing builder to copy.
     * @return A new Character RecordBuilder
     */
    public static Character.Builder newBuilder(Character.Builder other) {
        return new Character.Builder(other);
    }

    /**
     * Creates a new Character RecordBuilder by copying an existing Character instance.
     *
     * @param other The existing instance to copy.
     * @return A new Character RecordBuilder
     */
    public static Character.Builder newBuilder(Character other) {
        return new Character.Builder(other);
    }

    /**
     * Serializes this Character to a ByteBuffer.
     */
    public java.nio.ByteBuffer toByteBuffer() throws java.io.IOException {
        return ENCODER.encode(this);
    }

    public org.apache.avro.Schema getSchema() {
        return SCHEMA$;
    }

    // Used by DatumWriter.  Applications should not call.
    public Object get(int field$) {
        switch (field$) {
            case 0:
                return charset;
            case 1:
                return value;
            default:
                throw new org.apache.avro.AvroRuntimeException("Bad index");
        }
    }

    // Used by DatumReader.  Applications should not call.
    @SuppressWarnings(value = "unchecked")
    public void put(int field$, Object value$) {
        switch (field$) {
            case 0:
                charset = (String) value$;
                break;
            case 1:
                value = (java.nio.ByteBuffer) value$;
                break;
            default:
                throw new org.apache.avro.AvroRuntimeException("Bad index");
        }
    }

    /**
     * Gets the value of the 'charset' field.
     *
     * @return The value of the 'charset' field.
     */
    public String getCharset() {
        return charset;
    }

    /**
     * Sets the value of the 'charset' field.
     *
     * @param value the value to set.
     */
    public void setCharset(String value) {
        this.charset = value;
    }

    /**
     * Gets the value of the 'value' field.
     *
     * @return The value of the 'value' field.
     */
    public java.nio.ByteBuffer getValue() {
        return value;
    }

    /**
     * Sets the value of the 'value' field.
     *
     * @param value the value to set.
     */
    public void setValue(java.nio.ByteBuffer value) {
        this.value = value;
    }

    @Override
    public void writeExternal(java.io.ObjectOutput out)
            throws java.io.IOException {
        WRITER$.write(this, SpecificData.getEncoder(out));
    }

    @Override
    public void readExternal(java.io.ObjectInput in)
            throws java.io.IOException {
        READER$.read(this, SpecificData.getDecoder(in));
    }

    /**
     * RecordBuilder for Character instances.
     */
    public static class Builder extends org.apache.avro.specific.SpecificRecordBuilderBase<Character>
            implements org.apache.avro.data.RecordBuilder<Character> {

        private String charset;
        private java.nio.ByteBuffer value;

        /**
         * Creates a new Builder
         */
        private Builder() {
            super(SCHEMA$);
        }

        /**
         * Creates a Builder by copying an existing Builder.
         *
         * @param other The existing Builder to copy.
         */
        private Builder(Character.Builder other) {
            super(other);
            if (isValidValue(fields()[0], other.charset)) {
                this.charset = data().deepCopy(fields()[0].schema(), other.charset);
                fieldSetFlags()[0] = true;
            }
            if (isValidValue(fields()[1], other.value)) {
                this.value = data().deepCopy(fields()[1].schema(), other.value);
                fieldSetFlags()[1] = true;
            }
        }

        /**
         * Creates a Builder by copying an existing Character instance
         *
         * @param other The existing instance to copy.
         */
        private Builder(Character other) {
            super(SCHEMA$);
            if (isValidValue(fields()[0], other.charset)) {
                this.charset = data().deepCopy(fields()[0].schema(), other.charset);
                fieldSetFlags()[0] = true;
            }
            if (isValidValue(fields()[1], other.value)) {
                this.value = data().deepCopy(fields()[1].schema(), other.value);
                fieldSetFlags()[1] = true;
            }
        }

        /**
         * Gets the value of the 'charset' field.
         *
         * @return The value.
         */
        public String getCharset() {
            return charset;
        }

        /**
         * Sets the value of the 'charset' field.
         *
         * @param value The value of 'charset'.
         * @return This builder.
         */
        public Character.Builder setCharset(String value) {
            validate(fields()[0], value);
            this.charset = value;
            fieldSetFlags()[0] = true;
            return this;
        }

        /**
         * Checks whether the 'charset' field has been set.
         *
         * @return True if the 'charset' field has been set, false otherwise.
         */
        public boolean hasCharset() {
            return fieldSetFlags()[0];
        }


        /**
         * Clears the value of the 'charset' field.
         *
         * @return This builder.
         */
        public Character.Builder clearCharset() {
            charset = null;
            fieldSetFlags()[0] = false;
            return this;
        }

        /**
         * Gets the value of the 'value' field.
         *
         * @return The value.
         */
        public java.nio.ByteBuffer getValue() {
            return value;
        }

        /**
         * Sets the value of the 'value' field.
         *
         * @param value The value of 'value'.
         * @return This builder.
         */
        public Character.Builder setValue(java.nio.ByteBuffer value) {
            validate(fields()[1], value);
            this.value = value;
            fieldSetFlags()[1] = true;
            return this;
        }

        /**
         * Checks whether the 'value' field has been set.
         *
         * @return True if the 'value' field has been set, false otherwise.
         */
        public boolean hasValue() {
            return fieldSetFlags()[1];
        }


        /**
         * Clears the value of the 'value' field.
         *
         * @return This builder.
         */
        public Character.Builder clearValue() {
            value = null;
            fieldSetFlags()[1] = false;
            return this;
        }

        @Override
        @SuppressWarnings("unchecked")
        public Character build() {
            try {
                Character record = new Character();
                record.charset = fieldSetFlags()[0] ? this.charset : (String) defaultValue(fields()[0]);
                record.value = fieldSetFlags()[1] ? this.value : (java.nio.ByteBuffer) defaultValue(fields()[1]);
                return record;
            }
            catch (Exception e) {
                throw new org.apache.avro.AvroRuntimeException(e);
            }
        }
    }

}
