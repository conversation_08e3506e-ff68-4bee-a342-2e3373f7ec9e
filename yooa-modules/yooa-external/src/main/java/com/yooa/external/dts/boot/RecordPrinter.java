package com.yooa.external.dts.boot;

import avro.shaded.com.google.common.collect.Lists;
import com.yooa.common.core.domain.SubscribeConvertDbData;
import com.yooa.external.dts.avro.Field;
import com.yooa.external.dts.avro.OperatorTypeEnum;
import com.yooa.external.dts.avro.Record;
import com.yooa.external.dts.common.FieldEntryHolder;
import com.yooa.external.dts.common.Util;
import com.yooa.external.dts.recordprocessor.FieldConverter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Iterator;
import java.util.List;


/**
 * this class show how to process record attribute and column
 */
public class RecordPrinter {
    private static final Logger log = LoggerFactory.getLogger(RecordPrinter.class);
    private FieldConverter fieldConverter;

    public RecordPrinter(String dbType) {
        fieldConverter = FieldConverter.getConverter(dbType, null);
    }


    public SubscribeConvertDbData recordToListenConvert(Record record) {
        SubscribeConvertDbData subscribeConvertDbData = new SubscribeConvertDbData();
        StringBuilder stringBuilder = new StringBuilder(256);
        switch (record.getOperation()) {
            case DDL: {
                appendRecordGeneralInfo(record, stringBuilder, subscribeConvertDbData);
                String ddl = (String) record.getAfterImages();
                stringBuilder.append("DDL [").append(ddl).append("]");
                break;
            }
            default: {
                List<Field> fields = (List<Field>) record.getFields();
                FieldEntryHolder[] fieldArray = getFieldEntryHolder(record);
                appendRecordGeneralInfo(record, stringBuilder, subscribeConvertDbData);
                appendFields(fields, fieldArray[0], fieldArray[1], stringBuilder, subscribeConvertDbData);
                break;
            }
        }

        //   log.info("res :[{}]",stringBuilder);
        return subscribeConvertDbData;
    }


    private FieldEntryHolder[] getFieldEntryHolder(Record record) {
        // this is a simple impl, may exist unhandled situation
        FieldEntryHolder[] fieldArray = new FieldEntryHolder[2];

        fieldArray[0] = new FieldEntryHolder((List<Object>) record.getBeforeImages());
        fieldArray[1] = new FieldEntryHolder((List<Object>) record.getAfterImages());

        return fieldArray;
    }

    private void appendFields(List<Field> fields, FieldEntryHolder before, FieldEntryHolder after, StringBuilder stringBuilder, SubscribeConvertDbData subscribeConvertDbData) {
        List<SubscribeConvertDbData.Field> fieldList = Lists.newArrayList();
        if (null != fields) {
            Iterator<Field> fieldIterator = fields.iterator();
            while (fieldIterator.hasNext() && before.hasNext() && after.hasNext()) {
                Field field = fieldIterator.next();
                Object toPrintBefore = before.take();
                Object toPrintAfter = after.take();
                appendField(field, toPrintBefore, toPrintAfter, stringBuilder, subscribeConvertDbData, fieldList);
            }
        }
        subscribeConvertDbData.setFieldList(fieldList);
    }


    private void appendField(Field field, Object beforeImage, Object afterImage, StringBuilder stringBuilder, SubscribeConvertDbData subscribeConvertDbData, List<SubscribeConvertDbData.Field> fieldList) {
        stringBuilder.append("Field [").append(field.getName()).append("]");
        SubscribeConvertDbData.Field fieldUpdate = new SubscribeConvertDbData.Field();
        fieldUpdate.setFieldName(field.getName());
        if (null != beforeImage) {
            fieldUpdate.setBeforeValue(fieldConverter.convert(field, beforeImage).toString());
            stringBuilder.append("Before [").append(fieldConverter.convert(field, beforeImage).toString()).append("]");
        }
        if (null != afterImage) {
            stringBuilder.append("After [").append(fieldConverter.convert(field, afterImage).toString()).append("]");
            fieldUpdate.setAfterValue(fieldConverter.convert(field, afterImage).toString());
        }
        fieldList.add(fieldUpdate);
        stringBuilder.append("\n");
    }

    private void appendRecordGeneralInfo(Record record, StringBuilder stringBuilder, SubscribeConvertDbData subscribeConvertDbData) {
        String dbName = null;
        String tableName = null;
        // here we get db and table friendName
        String[] dbPair = Util.uncompressionObjectName(record.getObjectName());
        if (null != dbPair) {
            if (dbPair.length == 2) {
                dbName = dbPair[0];
                tableName = dbPair[1];
            }
            else if (dbPair.length == 3) {
                dbName = dbPair[0];
                tableName = dbPair[2];
            }
            else if (dbPair.length == 1) {
                dbName = dbPair[0];
                tableName = "";
            }
            else {
                throw new RuntimeException("invalid db and table name pair for record [" + record + "]");
            }
        }
        subscribeConvertDbData.setDbName(dbName);
        subscribeConvertDbData.setDbTableName(tableName);
        subscribeConvertDbData.setOperatorType(OperatorTypeEnum.match(record.getOperation()));
        stringBuilder.
                // record id can not be used as unique identifier
                        append("recordID [").append(record.getId()).append("]")
                // source info contains which source this record came from
                .append("source [").append(record.getSource()).append("]")
                // db and table friendName
                .append("dbTable [").append(dbName).append(".").append(tableName).append("]")
                // record type
                .append("recordType [").append(record.getOperation()).append("]")
                // record generate timestamp in source log
                .append("recordTimestamp [").append(record.getSourceTimestamp()).append("]")
                // record extra tag
                .append("extra tags [").append(StringUtils.join(record.getTags(), ",")).append("]");
        stringBuilder.append("\n");

    }

}
