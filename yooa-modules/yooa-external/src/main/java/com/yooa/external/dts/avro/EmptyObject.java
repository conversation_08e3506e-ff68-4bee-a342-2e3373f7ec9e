/**
 * Autogenerated by Avro
 * <p>
 * DO NOT EDIT DIRECTLY
 */
package com.yooa.external.dts.avro;

@SuppressWarnings("all")
@org.apache.avro.specific.AvroGenerated
public enum EmptyObject {
    NULL, NONE;
    public static final org.apache.avro.Schema SCHEMA$ = new org.apache.avro.Schema.Parser().parse("{\"type\":\"enum\",\"name\":\"EmptyObject\",\"namespace\":\"com.alibaba.dts.formats.avro\",\"symbols\":[\"NULL\",\"NONE\"]}");

    public static org.apache.avro.Schema getClassSchema() {
        return SCHEMA$;
    }
}
