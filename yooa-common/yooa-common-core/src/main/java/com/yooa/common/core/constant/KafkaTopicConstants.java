package com.yooa.common.core.constant;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 *
 */
public class KafkaTopicConstants {

    public static final String GROUP_ID = "subscribe-group";

    /**
     * cmf_users_charge 充值表topic
     **/
    public static final String CMF_USERS_CHARGE = "cmf_users_charge";

    /**
     * cmf_users 客户表topic
     **/
    public static final String CMF_USERS = "cmf_users";

    /**
     * cmf_users_coinrecord 打赏表topic
     **/
    public static final String CMF_USERS_COINRECORD = "cmf_users_coinrecord";

    /**
     * cmf_agent_admin_and_user 客户推广绑定表topic
     **/
    public static final String CMF_AGENT_ADMIN_AND_USER = "cmf_agent_admin_and_user";

    /**
     * cmf_agent_admin_and_user_log 客户推广绑定记录表topic
     **/
    public static final String CMF_AGENT_ADMIN_AND_USER_LOG = "cmf_agent_admin_and_user_log";

    /**
     * cmf_agent_admin_join_serve 交接客服表topic
     **/
    public static final String CMF_AGENT_ADMIN_JOIN_SERVE = "cmf_agent_admin_join_serve";

    /**
     * cmf_users_join_live 交接主播表topic
     **/
    public static final String CMF_USERS_JOIN_LIVE = "cmf_users_join_live";

    /**
     * cmf_users_variation_log  客服操作改绑记录表topic
     **/
    public static final String CMF_USERS_VARIATION_LOG = "cdc_cmf_users_variation_log";

    /**
     * cmf_record_back 退款表topic
     **/
    public static final String CMF_RECORD_BACK = "cmf_record_back";

    /**
     * cmf_users_liverecord 直播记录表topic
     **/
    public static final String CMF_USERS_LIVERECORD = "cmf_users_liverecord";

    /**
     * cmf_three_charge_back 充值退款表topic
     **/
    public static final String CMF_THREE_CHARGE_BACK = "cmf_three_charge_back";

    /**
     * cmf_agent_admin_and_live 运营和主播关联表topic
     **/
    public static final String CMF_AGENT_ADMIN_AND_LIVE = "cmf_agent_admin_and_live";

    /**
     * cmf_users_livetime 用户直播间时间统计表topic
     **/
    public static final String CMF_USERS_NEW_LIVETIME = "cmf_users_new_livetime";

    /**
     * cmf_users_auth 主播认证-- 主播列表topic
     **/
    public static final String CMF_USERS_AUTH = "cmf_users_auth";

    /**
     * cmf_gift 礼物表
     **/
    public static final String CMF_GIFT = "cmf_gift";

    // 静态 List 用于存储所有常量值
    public static final List<String> TOPIC_LIST;

    static {
        List<String> list = new ArrayList<>();
        Field[] fields = KafkaTopicConstants.class.getDeclaredFields();
        for (Field field : fields) {
            // 检查字段是否为 public static final 的 String 类型
            if (isPublicStaticFinalString(field)) {
                try {
                    list.add((String) field.get(null)); // 获取字段值
                }
                catch (IllegalAccessException e) {
                    // 异常处理（理论上不会发生）
                    throw new RuntimeException("field error: " + field.getName(), e);
                }
            }
        }
        TOPIC_LIST = Collections.unmodifiableList(list); // 转为不可变列表
    }

    // 辅助方法：判断字段是否为 public static final 的 String 类型
    private static boolean isPublicStaticFinalString(Field field) {
        int modifiers = field.getModifiers();
        return Modifier.isPublic(modifiers)
                && Modifier.isStatic(modifiers)
                && Modifier.isFinal(modifiers)
                && field.getType() == String.class;
    }


}
