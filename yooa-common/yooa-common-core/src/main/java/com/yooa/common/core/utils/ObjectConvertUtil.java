package com.yooa.common.core.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;


/**
 * <AUTHOR> xh
 * @Date: 2025/3/11 17:12
 * @Description: 类型转换器
 */
public class ObjectConvertUtil {
    private static final Map<Class<?>, Function<String, ?>> CONVERTERS = new ConcurrentHashMap<>();
    private static final Map<Class<?>, Object> DEFAULT_VALUES = new ConcurrentHashMap<>();
    private static final DateTimeFormatter DEFAULT_DATE_FORMAT = DateTimeFormatter.ISO_LOCAL_DATE_TIME;

    static {
        // 注册基本类型转换器
        registerDefaults();
    }

    private static void registerDefaults() {
        // 基本类型转换
        registerConverter(String.class, s -> s);
        registerConverter(Long.class, Long::parseLong);
        registerConverter(long.class, Long::parseLong);
        registerConverter(Integer.class, Integer::parseInt);
        registerConverter(int.class, Integer::parseInt);
        registerConverter(Double.class, Double::parseDouble);
        registerConverter(double.class, Double::parseDouble);
        registerConverter(Boolean.class, Boolean::parseBoolean);
        registerConverter(boolean.class, Boolean::parseBoolean);
        registerConverter(Float.class, Float::parseFloat);
        registerConverter(float.class, Float::parseFloat);
        registerConverter(Short.class, Short::parseShort);
        registerConverter(short.class, Short::parseShort);
        registerConverter(Byte.class, Byte::parseByte);
        registerConverter(byte.class, Byte::parseByte);

        // 日期时间类型
        registerConverter(LocalDateTime.class, s -> LocalDateTime.parse(s, DEFAULT_DATE_FORMAT));
        registerConverter(LocalDate.class, s -> LocalDate.parse(s, DateTimeFormatter.ISO_LOCAL_DATE));
        registerConverter(LocalTime.class, s -> LocalTime.parse(s, DateTimeFormatter.ISO_LOCAL_TIME));
        registerConverter(ZonedDateTime.class, s -> ZonedDateTime.parse(s, DateTimeFormatter.ISO_ZONED_DATE_TIME));

        // 默认值配置
        DEFAULT_VALUES.put(long.class, 0L);
        DEFAULT_VALUES.put(int.class, 0);
        DEFAULT_VALUES.put(double.class, 0.0);
        DEFAULT_VALUES.put(float.class, 0f);
        DEFAULT_VALUES.put(short.class, (short) 0);
        DEFAULT_VALUES.put(byte.class, (byte) 0);
        DEFAULT_VALUES.put(boolean.class, false);
    }

    /**
     * 注册自定义转换器
     * @param targetType 目标类型
     * @param converter  转换函数
     */
    public static <T> void registerConverter(Class<T> targetType, Function<String, T> converter) {
        CONVERTERS.put(targetType, converter);
    }

    /**
     * 通用转换方法
     * @param value        输入字符串
     * @param targetType   目标类型
     * @param defaultValue 转换失败时的默认值
     */
    public static <T> T convertTo(String value, Class<T> targetType, T defaultValue) {
        if (StrUtil.isBlank(value)) {
            return defaultValue != null ? defaultValue : getPrimitiveDefault(targetType);
        }

        try {
            Function<String, ?> converter = CONVERTERS.get(targetType);
            if (converter != null) {
                return targetType.cast(converter.apply(value));
            }

            // 处理valueOf方法
            try {
                Method valueOf = targetType.getMethod("valueOf", String.class);
                return targetType.cast(valueOf.invoke(null, value));
            } catch (NoSuchMethodException e) {
                // 尝试使用构造函数
                try {
                    return targetType.getConstructor(String.class).newInstance(value);
                } catch (NoSuchMethodException ex) {
                    throw new IllegalArgumentException("Unsupported conversion type: " + targetType.getName());
                }
            }
        } catch (Exception e) {
            return defaultValue != null ? defaultValue : getPrimitiveDefault(targetType);
        }
    }

    public static <T> T convertTo(String value, Class<T> targetType) {
        return convertTo(value, targetType, null);
    }

    /**
     * 目前Long类型使用较多  不填默认Long类型
     */
    public static Long convertToLong(String value) {
        return convertTo(value, Long.class);
    }

    @SuppressWarnings("unchecked")
    private static <T> T getPrimitiveDefault(Class<T> targetType) {
        return targetType.isPrimitive() ? (T) DEFAULT_VALUES.get(targetType) : null;
    }

}
