<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.yooa</groupId>
        <artifactId>yooa</artifactId>
        <version>3.6.4</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <modules>
        <module>yooa-common-log</module>
        <module>yooa-common-core</module>
        <module>yooa-common-redis</module>
        <module>yooa-common-seata</module>
        <module>yooa-common-security</module>
        <module>yooa-common-sensitive</module>
        <module>yooa-common-datascope</module>
        <module>yooa-common-datasource</module>
        <module>yooa-common-mybatis</module>
        <module>yooa-common-kafka</module>
        <module>yooa-common-idempotent</module>
    </modules>

    <artifactId>yooa-common</artifactId>
    <packaging>pom</packaging>

    <description>
        yooa-common通用模块
    </description>

</project>
