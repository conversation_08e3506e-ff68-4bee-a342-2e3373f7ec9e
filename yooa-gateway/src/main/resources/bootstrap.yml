server:
  port: 18080

spring:
  application:
    name: @artifactId@
  profiles:
    active: @profiles.active@
  cloud:
    nacos:
      username: @nacos.username@
      password: @nacos.password@
      discovery:
        server-addr: @nacos.server-addr@
        namespace: @nacos.namespace@
      config:
        server-addr: @nacos.server-addr@
        namespace: @nacos.namespace@
        file-extension: @nacos.file-extension@
        shared-configs:
          - <EMAIL>@.@nacos.file-extension@

    sentinel:
      eager: true
      transport:
        dashboard: 127.0.0.1:8718
      datasource:
        ds1:
          nacos:
            server-addr: @nacos.server-addr@
            namespace: @nacos.namespace@
            dataId: sentinel-yooa-gateway
            groupId: DEFAULT_GROUP
            data-type: json
            rule-type: gw-flow
