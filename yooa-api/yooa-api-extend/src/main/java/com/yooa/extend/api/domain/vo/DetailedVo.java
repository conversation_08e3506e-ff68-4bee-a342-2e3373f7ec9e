package com.yooa.extend.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 *
 */
@Data
public class DetailedVo {

    /**
     * 用户ID/部门ID
     */
    private Long id;

    /**
     * 用户昵称/部门名
     */
    @Excel(name = "名称")
    private String name;

    /**
     * 好友
     */
    @Excel(name = "好友")
    private Long friend;

    /**
     * 注册
     */
    @Excel(name = "注册")
    private Long register;

    /**
     * 优质
     */
    @Excel(name = "优质")
    private Long qualityUsers;

    /**
     * 接收数
     */
    @Excel(name = "一交")
    private Long receive;

    /**
     * 接收数vip/接粉数
     */
    @Excel(name = "二交")
    private Long receiveVip;

    /**
     * 新增业绩
     */
    @Excel(name = "新增业绩")
    private BigDecimal fortyFiveDays;

    /**
     * 总业绩
     */
    @Excel(name = "总业绩")
    private BigDecimal realProfit;

    /**
     * 充值用户数量
     */
    @Excel(name = "充值用户数量")
    private Integer chargeUser;

    /**
     * 首充
     */
    @Excel(name = "首充")
    private Long firstCharge;

    /**
     * 200粉
     */
    @Excel(name = "200粉")
    private Integer fans2h;

    /**
     * 500粉
     */
    private Integer fans5h;

    /**
     * 5k粉
     */
    @Excel(name = "5k粉")
    private Integer fans5k;

    /**
     * 5k新粉
     */
    @Excel(name = "5k新粉")
    private Integer fans5kNew;

    /**
     * 5w粉
     */
    @Excel(name = "5w粉")
    private Integer fans5w;

    /**
     * 部门
     */
    @Excel(name = "部门")
    private String teamName1;

    /**
     * 团队
     */
    @Excel(name = "团队")
    private String teamName2;

    /**
     * 小组
     */
    @Excel(name = "小组")
    private String teamName3;

    /**
     * 领导
     */
    @Excel(name = "直系领导")
    private String leaderName;

    /**
     * 祖级列表部门名拼接
     */
    private String ancestorsNames;

    /**
     * 领导ID
     */
    private Long leaderId;

    /**
     * PD用户ID集
     */
    private String pdUserIds;

    /**
     * 下级部门ID集
     */
    private String juniorIds;

    /**
     * 粉丝登记字符串
     */
    private String fansInfo;

    /**
     * 充值订单字符串
     */
    private String orderInfo;
}
