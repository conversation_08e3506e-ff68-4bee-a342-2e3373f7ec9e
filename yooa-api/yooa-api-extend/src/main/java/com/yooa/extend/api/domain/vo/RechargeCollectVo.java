package com.yooa.extend.api.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> xh
 * @Date: 2025/7/8 13:10
 * @Description:
 */
@Data
public class RechargeCollectVo {

    /**
     * 当月充值
     */
    private BigDecimal monthRecharge;

    /**
     * 今年充值
     */
    private BigDecimal yearRecharge;

    /**
     * 去年充值
     */
    private BigDecimal lastYearRecharge;

    /**
     * 上个月充值
     */
    private BigDecimal lastMonthRecharge;

    /**
     * 本月平均充值
     */
    private BigDecimal monthAvgRecharge;

    /**
     * 今年平均充值
     */
    private BigDecimal yearAvgRecharge;
}
