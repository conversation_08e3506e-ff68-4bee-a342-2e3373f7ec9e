package com.yooa.extend.api.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 *
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeptRankingVo {

    // 完成字段名
    private String fieldsName;

    // 部门负责人
    private Long userId;

    // 部门负责人
    private String nickName;

    // 字段属性的值
    private BigDecimal value;

    // oa部门ID
    private Long deptId;

    // 部门名
    private String deptName;

    // 部门拼接名
    private String deptSpliceName;

    // 提升比例
    private String upScale;

    // 提升值
    private BigDecimal upValue;

    // 排名
    private Integer ranking;
}
