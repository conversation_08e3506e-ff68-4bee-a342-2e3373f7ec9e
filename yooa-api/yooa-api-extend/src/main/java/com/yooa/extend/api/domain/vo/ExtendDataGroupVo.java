package com.yooa.extend.api.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description 用户或部门返回类
 * <AUTHOR>
 * @Date 2024/9/13 上午10:39
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExtendDataGroupVo {

    /**
     * 用户或部门id
     */
    private Long id;

    /**
     * 用户或部门名
     */
    private String name;

    // 字段属性的值
    private BigDecimal value;

    // 排名
    private Integer ranking;

    // 占比
    private BigDecimal scale;

    // 提升比例
    private BigDecimal upScale;

}
