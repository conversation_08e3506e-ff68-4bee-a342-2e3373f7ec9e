package com.yooa.extend.api.domain;

import com.yooa.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 运营充值达成_粉丝登记列表
 *
 * @TableName operate_vermicelli
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OperateVermicelli implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键id
     */
    private Long id;
    /**
     * 打赏粉丝类型(0:运营英文100粉、1:200粉、2:500粉、3:5k粉、4:5w粉、5:10w粉、6:首次打赏粉、7:首充时间)
     */
    @Excel(name = "粉丝类型", sort = 5, readConverterExp = "0=运营英文100粉,1=200粉,2=500粉,3=5k粉,4=5w粉,5=10w粉,6=首次打赏粉,7=首充时间")
    private Integer fansType;
    /**
     * 好友id
     */
    private Long friendId;
    /**
     * 好友下的客户ID集
     */
    @Excel(name = "粉丝ID", sort = 1)
    private String customerIds;
    /**
     * 达成日期
     */
    @Excel(name = "达成日期", sort = 3, dateFormat = "yyyy-MM-dd")
    private LocalDate recordDate;
    /**
     * 主播id
     */
    private Long anchorId;
    /**
     * py运营id
     */
    private Long pyOperateId;
    /**
     * 运营id
     */
    private Long operateId;
    /**
     * 运营部门id
     */
    private Long operateDeptId;
    /**
     * 状态（0新粉 1老粉）
     */
    @Excel(name = "状态", sort = 4, readConverterExp = "0=新粉,1=老粉")
    private String status;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 修改人
     */
    private Long updateBy;
    /**
     * 备注
     */
    private String remark;
}