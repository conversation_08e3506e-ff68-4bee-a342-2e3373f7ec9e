package com.yooa.extend.api.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 *
 */
@Data
public class IndexUserGroupAllVo {

    // 用户id
    private Long userId;

    // 用户名
    private String nickName;

    /**
     * 好友
     */
    private Long friend;

    /**
     * 注册
     */
    private Long register;

    /**
     * 优质
     */
    private Integer qualityUsers;

    /**
     * 接收数
     */
    private Integer receive;

    /**
     * 二交
     */
    private Integer receiveVip;

    /**
     * 新增业绩
     */
    private BigDecimal fortyFiveDays;

    /**
     * 汇总业绩
     */
    private BigDecimal realProfit;

    /**
     * 首充
     */
    private Integer firstCharge;

    /**
     * 200粉
     */
    private Integer fans2h;

    /**
     * 500粉
     */
    private Integer fans5h;

    /**
     * 5k粉
     */
    private Integer fans5k;

    /**
     * 5k新粉
     */
    private Integer fans5kNew;

    /**
     * 5w粉
     */
    private Integer fans5w;

    /**
     * 首充率/付费率
     */
    private BigDecimal firstChargeRate;

    /**
     * 注册率
     */
    private BigDecimal registerRate;

    /**
     * 交接率
     */
    private BigDecimal receiveRate;

    /**
     * 二交率
     */
    private BigDecimal receiveVipRate;


}
