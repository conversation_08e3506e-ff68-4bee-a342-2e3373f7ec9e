package com.yooa.extend.api.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/9/7 下午1:37
 */
@Data
public class ExtendDataAllVo {

    /**
     * 好友
     */
    private Integer friend;

    /**
     * 注册
     */
    private Integer register;

    /**
     * 优质
     */
    private Integer qualityUsers;

    /**
     * 一交
     */
    private Integer receive;

    /**
     * 二交
     */
    private Integer receiveVip;

    /**
     * 总业绩
     */
    private BigDecimal realProfit;

    /**
     * 新增业绩
     */
    private BigDecimal fortyFiveDays;

    /**
     * 充值用户数量
     */
    private Integer chargeUser;

    /**
     * 首充
     */
    private Integer firstCharge;

    /**
     * 200粉
     */
    private Integer fans2h;

    /**
     * 500粉
     */
    private Integer fans5h;

    /**
     * 5k粉
     */
    private Integer fans5k;

    /**
     * 5w粉
     */
    private Integer fans5w;

    /**
     * 10w粉
     */
    private Integer fans10w;

    /**
     * 40w粉
     */
    private Integer fans40w;

    /**
     * 100w粉
     */
    private Integer fans100w;

    // 人数
    private Long peopleNumber;
}
