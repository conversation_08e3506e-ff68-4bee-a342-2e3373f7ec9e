package com.yooa.extend.api.domain.vo;

import lombok.Data;

@Data
public class AnchorOperateVo {

    /**
     * 运营id
     */
    private Long userId;

    /**
     * 运营名称
     */
    private String userName;

    /**
     * 运营昵称
     */
    private String nickName;

    /**
     * 运营部门id
     */
    private Long deptId;

    /**
     * 运营部门
     */
    private String deptName;

    /**
     * 运营父部门
     */
    private String ancestorsNames;

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 主播名称
     */
    private String anchorName;

    /**
     * 客户ID
     */
    private Long customerId;
}
