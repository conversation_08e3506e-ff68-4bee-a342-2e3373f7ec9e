package com.yooa.extend.api.domain.vo;

import com.yooa.extend.api.domain.TargetPlanChangeRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 目标计划变动记录
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TargetPlanChangeRecordVo extends TargetPlanChangeRecord {

    /**
     * 创建人昵称
     */
    private String createNickName;

    /**
     * 修改人昵称
     */
    private String updateNickName;

}
