package com.yooa.extend.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 推广缓存改绑处理表
 */
@TableName(value = "extend_processed")
@Data
public class ExtendProcessed implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 表ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 改绑前ID(被改绑人ID)
     */
    @TableField(value = "beforeID")
    private Integer beforeid;
    /**
     * 改绑后ID(改绑人ID)
     */
    @TableField(value = "afterID")
    private Integer afterid;
    /**
     * 改绑时间
     */
    @TableField(value = "time")
    private Date time;
    /**
     * 0:订单、1:用户
     */
    @TableField(value = "type")
    private Integer type;
    /**
     * 0:未处理、1:已处理
     */
    @TableField(value = "read")
    private Integer read;
}