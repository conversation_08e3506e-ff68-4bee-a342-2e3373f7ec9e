package com.yooa.extend.api.factory;

import com.yooa.common.core.domain.R;
import com.yooa.extend.api.RemoteVermicelliService;
import com.yooa.extend.api.domain.ExtendVermicelli;
import com.yooa.extend.api.domain.OperateVermicelli;
import com.yooa.extend.api.domain.VipVermicelli;
import com.yooa.extend.api.domain.vo.IndexUserGroupAllVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

/**
 * 粉丝登记服务降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteVermicelliFallbackFactory implements FallbackFactory<RemoteVermicelliService> {
    @Override
    public RemoteVermicelliService create(Throwable throwable) {

        log.error("粉丝登记服务调用失败:{}", throwable.getMessage());

        return new RemoteVermicelliService() {

            @Override
            public R<Integer> manufactureVermicelli(List<Long> pdExtendIds, Long extendId, Long extendDeptId, Long serveId, Long serveDeptId, List<Long> ids, Long friendId, String source) {
                return R.fail("生产推广粉丝登记列表失败:" + throwable.getMessage());
            }

            @Override
            public R<Integer> manufactureVermicelliCopy(List<Long> pdExtendIds, Long extendId, Long extendDeptId, Long serveId, Long serveDeptId, List<Long> ids, Long friendId, BigDecimal oneDayUp, BigDecimal monthUp, String source) {
                return R.fail("生产推广粉丝登记列表失败:" + throwable.getMessage());
            }

            @Override
            public R<IndexUserGroupAllVo> selExtendVermicelliNumber(List<Long> userIds, String beginTime, String endTime, String source) {
                return R.fail("查询推广粉丝登记汇总数失败:" + throwable.getMessage());
            }

            @Override
            public R<List<ExtendVermicelli>> selExtendVermicelliList(List<Long> userIds, Integer fieldType, List<Long> friendIds, String beginTime, String endTime, String source) {
                return R.fail("查询推广粉丝登记列表失败:" + throwable.getMessage());
            }

            @Override
            public R<List<ExtendVermicelli>> selOrderExtendVermicelliList(Long userId, Long customerId, String orderTime, String source) {
                return R.fail("查询推广粉丝登记列表失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> addExtendVermicelli(ExtendVermicelli vermicelli, String source) {
                return R.fail("新增推广粉丝登记失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> updExtendVermicelliList(List<ExtendVermicelli> vermicelliList, String source) {
                return R.fail("批量修改推广粉丝登记列表失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> removeExtendVermicelliByIds(List<Long> ids, String source) {
                return R.fail("批量删除推广粉丝登记列表失败:" + throwable.getMessage());
            }

            @Override
            public R<List<VipVermicelli>> selVipVermicelliList(List<Long> userIds, Integer fieldType, List<Long> friendIds, String beginTime, String endTime, String source) {
                return R.fail("查询VIP粉丝登记列表失败:" + throwable.getMessage());
            }

            @Override
            public R<List<VipVermicelli>> selOrderVipVermicelliList(Long userId, Long customerId, String orderTime, String source) {
                return R.fail("查询VIP粉丝登记列表:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> addVipVermicelli(VipVermicelli vermicelli, String source) {
                return R.fail("新增VIP粉丝登记失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> updVipVermicelliList(List<VipVermicelli> vermicelliList, String source) {
                return R.fail("批量修改VIP粉丝登记列表失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> removeVipVermicelliByIds(List<Long> ids, String source) {
                return R.fail("批量删除VIP粉丝登记列表失败:" + throwable.getMessage());
            }

            @Override
            public R<List<OperateVermicelli>> selOperateVermicelliList(List<Long> userIds, Integer fieldType, Long customerId, List<Long> friendIds, String beginTime, String endTime, String source) {
                return R.fail("查询运营粉丝登记列表失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> addOperateVermicelli(OperateVermicelli vermicelli, String source) {
                return R.fail("新增运营粉丝登记失败:" + throwable.getMessage());
            }

            @Override
            public R<Boolean> updOperateVermicelliList(List<OperateVermicelli> vermicelliList, String source) {
                return R.fail("批量修改运营粉丝登记列表失败:" + throwable.getMessage());
            }
        };
    }
}
