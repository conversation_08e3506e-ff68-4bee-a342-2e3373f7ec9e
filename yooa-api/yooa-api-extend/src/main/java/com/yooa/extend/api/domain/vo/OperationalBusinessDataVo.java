package com.yooa.extend.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR> xh
 * @Date: 2025/7/9 15:48
 * @Description:
 */
@Data
public class OperationalBusinessDataVo {

    private Long id;

    /**
     * 运营名字
     */
    @Excel(name = "运营名字")
    private String operateName;

    /**
     * 开播数
     */
    @Excel(name = "开播数")
    private Long beginToShowNum;

    /**
     * 休播数
     */
    @Excel(name = "休播数")
    private Long offAirBroadcastNum;

    /**
     * 停播数
     */
    @Excel(name = "停播数")
    private Long discontinueBroadcastingNum;

    /**
     * 待停播
     */
    @Excel(name = "待停播")
    private Long toBeDiscontinued;

    /**
     * 交接数
     */
    @Excel(name = "交接数")
    private Long handoverNum;

    /**
     * 总打赏粉
     */
    @Excel(name = "总打赏数")
    private Long totalRewardNum;

    /**
     * 接粉打赏数
     */
    @Excel(name = "接粉打赏数")
    private Long handoverRewardNum;

    /**
     * 接粉打赏率
     */
    @Excel(name = "接粉打赏率")
    private BigDecimal handoverRewardRate;

    /**
     * 首充粉
     */
    @Excel(name = "首充粉")
    private Long firstChargeNum;

    /**
     * 接粉手冲率
     */
    @Excel(name = "接粉手冲率")
    private BigDecimal handoverFirstChargeRate;

    /**
     * 200粉
     */
    @Excel(name = "200粉")
    private Long fans2h;


    /**
     * 5k粉
     */
    @Excel(name = "5k粉")
    private Long fans5k;


    /**
     * 5w粉
     */
    @Excel(name = "5w粉")
    private Long fans5w;

    /**
     * 10w粉
     */
    @Excel(name = "10w粉")
    private Long fans10w;

    /**
     * 打赏业绩
     */
    @Excel(name = "打赏业绩")
    private BigDecimal rewardAmount;

    /**
     * PD用户ID集
     */
    private String pdUserIds;
}
