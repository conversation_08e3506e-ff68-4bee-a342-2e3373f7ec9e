package com.yooa.extend.api.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/18 下午5:14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EmployeeMonthRankingVo {

    // 完成字段名
    private String fieldsName;

    // 字段属性的值
    private BigDecimal value;

    // 排名
    private Integer ranking;

    // 排名人数
    private Integer rankingPeople;
}
