package com.yooa.extend.api.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExtendDataTimeGroupVo {

    // 字段属性的值
    private BigDecimal value;

    // 时间
    private LocalDateTime date;

    // 结束时间
    private LocalDateTime endDate;
}
