package com.yooa.extend.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 打包计划目标和审核
 */
@TableName(value = "goal_extend_pack")
@Data
public class GoalExtendPack implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 表id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 标题
     */
    @TableField(value = "title")
    private String title;
    /**
     * 表类型(0:年计划、1:月计划)
     */
    @TableField(value = "table_type")
    private Integer tableType;
    /**
     * 计划类型(0:个人计划、1:团队计划)
     */
    @TableField(value = "goal_type")
    private Integer goalType;
    /**
     * 制定人id
     */
    @TableField(value = "producer_id")
    private Long producerId;
    /**
     * 制定人名
     */
    @TableField(value = "producer_name")
    private String producerName;
    /**
     * 制定人备注
     */
    @TableField(value = "producer_remark")
    private String producerRemark;
    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDate createTime;
    /**
     * 修改时间
     */
    @TableField(value = "update_time")
    private LocalDate updateTime;
    /**
     * 审核状态(字典)
     */
    @TableField(value = "audit_status")
    private Integer auditStatus;
    /**
     * 审核人id
     */
    @TableField(value = "audit_people_id")
    private Long auditPeopleId;
    /**
     * 审核人名
     */
    @TableField(value = "audit_people_name")
    private String auditPeopleName;
    /**
     * 未通过原因
     */
    @TableField(value = "reason")
    private String reason;
}