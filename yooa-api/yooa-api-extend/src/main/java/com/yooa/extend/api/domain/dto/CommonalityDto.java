package com.yooa.extend.api.domain.dto;

import cn.hutool.core.collection.CollUtil;
import com.yooa.common.core.utils.LocalDateUtil;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据汇总入参类
 */
@Data
public class CommonalityDto {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 部门层级
     */
    private Integer hierarchy;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 父部门id
     */
    private Long parentId;

    /**
     * 包含部门ID
     */
    private Long findInSetDeptId;

    /**
     * 开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 查询类型(部门类型)(注:本来要改为deptType,但前端需要改的地方比较多,所以暂时不改)
     */
    private Integer selType;

    /**
     * 粉丝类型(1中文男粉 2中文女粉 3英文男粉 4英文女粉)
     */
    private Integer fansType;

    /**
     * 字段(0:注册、1:总业绩、2:新增业绩、3:引领、4:好友、5:首充、6:优质、7:交接、8:二交（接粉）、9:2h、10:5h、11:5k、12:5w、13:注册率、14:交接率、15:二交率、16:付费率)
     */
    private Integer fields;

    /**
     * 输出类型(0:月、1周、2:天、3小时)(给柱状图、折线图的分组、...)
     */
    private Integer expType;

    /**
     * 同比环比:(0年、1月、2周、3天、4季)
     */
    private Integer compare;

    /**
     * 查询等级(0:部门、1:个人)
     */
    private Integer lv;

    /**
     * 完成字段name
     */
    private String fieldsName;

    /**
     * 搜索框
     */
    private String searchBox;

    /**
     * 目标字段id
     */
    private Integer fieldsGoalId;

    /**
     * 审核状态(字典)
     */
    private Integer auditStatus;

    /**
     * 打包ID
     */
    private Integer goalId;

    /**
     * 级别
     */
    private Integer rank;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 好友ID集
     */
    private List<Long> friendIds;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 用户ID集
     */
    private List<Long> userIds;

    /**
     * 部门ID集
     */
    private List<Long> deptIds;

    private LocalDateTime compareBeginTime;
    private LocalDateTime compareEndTime;
    /**
     * 推前时间
     */
    private int year = 0;
    private int month = 0;
    private int day = 0;
    /**
     * 环比时间
     */
    private int withYear = 0;
    private int withDay = 0;
    /**
     * 同比时间
     */
    private int ringMonth = 0;
    private int ringDay = 0;

    public void setFields(Integer fields) {
        this.fields = fields;
        switch (fields) {
            case 0:
                this.fieldsName = "register";               // 注册
                this.fieldsGoalId = 2;
                break;
            case 1:
                this.fieldsName = "real_profit";            // 汇总业绩
                this.fieldsGoalId = 8;
                break;
            case 2:
                this.fieldsName = "forty_five_days";        // 新增
                this.fieldsGoalId = 7;
                break;
            case 3:
                this.fieldsName = "";                       // 暂为空
                break;
            case 4:
                this.fieldsName = "friend";                 // 好友
                this.fieldsGoalId = 1;
                break;
            case 5:
                this.fieldsName = "first_charge";           // 首充
                this.fieldsGoalId = 6;
                break;
            case 6:
                this.fieldsName = "quality_users";          // 优质
                this.fieldsGoalId = 3;
                break;
            case 7:
                this.fieldsName = "receive";                // 一交
                this.fieldsGoalId = 4;
                break;
            case 8:
                this.fieldsName = "receive_vip";            // 二交
                this.fieldsGoalId = 5;
                break;
            case 9:
                this.fieldsName = "0";
                this.fieldsGoalId = 9;                      // 推广两百粉
                break;
            case 10:
                this.fieldsName = "1";
                this.fieldsGoalId = 10;                     // 推广五百粉
                break;
            case 11:
                this.fieldsName = "2";
                this.fieldsGoalId = 11;                     // 推广五千粉
                break;
            case 12:
                this.fieldsName = "3";
                this.fieldsGoalId = 12;                     // 推广五万粉
                break;
        }
    }

    public Integer getFieldsName(String fieldsName) {
        switch (fieldsName) {
            case "register":            // 注册
                return 0;
            case "real_profit":         // 总业绩
                return 1;
            case "forty_five_days":     // 新增
                return 2;
            case "friend":              // 好友
                return 4;
            case "first_charge":        // 首充
                return 5;
            case "quality_users":       // 优质
                return 6;
            case "receive":             // 一交
                return 7;
            case "receive_vip":         // 二交
                return 8;
            case "0":                   // 推广两百粉
                return 9;
            case "1":                   // 推广五百粉
                return 10;
            case "2":                   // 推广五千粉
                return 11;
            case "3":                   // 推广五万粉
                return 12;
        }
        return null;
    }

    // 驼峰转换
    public void humpConvert() {
        if (this.fieldsName.equals("friend")) {
            this.fields = 4;
        } else if (this.fieldsName.equals("register")) {
            this.fields = 0;
        } else if (this.fieldsName.equals("receive")) {
            this.fields = 7;
        } else if (this.fieldsName.equals("realProfit")) {
            this.fieldsName = "real_profit";
            this.fields = 1;
        } else if (this.fieldsName.equals("fortyFiveDays")) {
            this.fieldsName = "forty_five_days";
            this.fields = 2;
        } else if (this.fieldsName.equals("firstCharge")) {
            this.fieldsName = "first_charge";
            this.fields = 5;
        } else if (this.fieldsName.equals("qualityUsers")) {
            this.fieldsName = "quality_users";
            this.fields = 6;
        } else if (this.fieldsName.equals("receiveVip")) {
            this.fieldsName = "receive_vip";
            this.fields = 8;
        } else if (this.fieldsName.equals("chargeUser")) {
            this.fieldsName = "charge_user";
        } else if (this.fieldsName.equals("fans2h")) {
            this.fieldsName = "0";
            this.fields = 9;
        } else if (this.fieldsName.equals("fans5h")) {
            this.fieldsName = "1";
            this.fields = 10;
        } else if (this.fieldsName.equals("fans5k")) {
            this.fieldsName = "2";
            this.fields = 11;
        } else if (this.fieldsName.equals("fans5w")) {
            this.fieldsName = "3";
            this.fields = 12;
        }
    }

    //推前时间数字计算
    public void computeForward() {
        if (this.compare == 0) {     // 年
            year = -1;
        } else if (this.compare == 1) {       // 月
            month = -1;
        } else if (this.compare == 2) {       // 周
            day = -7;
        } else if (this.compare == 3) {       // 天
            day = -1;
        } else if (this.compare == 4) {       // 季
            month = -3;
        }
    }

    //推前时间(使用完要调用下面的时间还原方法)
    public void forwardDate() {
        this.compareBeginTime = this.beginTime;
        this.compareEndTime = this.endTime;
        this.beginTime = this.beginTime.plusYears(year).plusMonths(month).plusDays(day);
        this.endTime = this.endTime.plusYears(year).plusMonths(month).plusDays(day);
    }

    //同步环比时间数字计算
    public void compareInt() {
        if (this.compare == 0) {     // 年
            this.withYear = -1;
        } else if (this.compare == 1) {       // 月
            this.withYear = -1;
            this.ringMonth = -1;
        } else if (this.compare == 2) {       // 周
            this.withDay = -7;
            this.ringDay = -28;
        } else if (this.compare == 3) {       // 天
            this.withDay = -1;
            this.ringDay = -7;
        }
    }

    //同比计算时间(使用完要调用下面的时间还原方法)
    public void withDate() {
        this.compareBeginTime = this.beginTime;
        this.compareEndTime = this.endTime;
        this.beginTime = this.beginTime.plusYears(withYear).plusDays(withDay);
        this.endTime = this.endTime.plusYears(withYear).plusDays(withDay);
    }

    //环比计算时间(使用完要调用下面的时间还原方法)
    public void ringDate() {
        this.compareBeginTime = this.beginTime;
        this.compareEndTime = this.endTime;
        this.beginTime = this.beginTime.plusMonths(ringMonth).plusDays(ringDay);
        this.endTime = this.endTime.plusMonths(ringMonth).plusDays(ringDay);
    }

    //时间参数恢复方法
    public void restore() {
        this.beginTime = this.compareBeginTime;
        this.endTime = this.compareEndTime;
    }

    //查询方向(根据字段,决定去查数据汇总,crm,粉丝登记)
    public int selDirection() {
        if (this.getFields() == 0 || this.getFields() == 4) {                               // 查crm好友、注册:1
            return 1;
        } else if (this.getFields() < 9 && this.getFields() != 5) {                         // 查询数据汇总:0
            return 0;
        } else {                                                                            // 查询粉丝登记:2
            return 2;
        }
    }

    //计算两个时间的差集合(根据要返回的类型)
    public List<LocalDateTime> computeDatePoorList() {
        List<LocalDate> dateList = new ArrayList<>();            // 时间分组
        List<LocalDateTime> timeList = new ArrayList<>();

        if (this.getExpType() == 1 || this.getExpType() == 2) {      // 已天为单位
            dateList = LocalDateUtil.getDatesBetween(this.getBeginTime().toLocalDate(), this.getEndTime().toLocalDate());
        } else if (this.getExpType() == 0) {                                   // 已月为单位(默认为月初第一天)
            dateList = LocalDateUtil.getMonthsBetween(this.getBeginTime().toLocalDate(), this.getEndTime().toLocalDate());
        } else if (this.getExpType() == 3) {                                    // 已小时为单位
            timeList = LocalDateUtil.getHoursBetween(this.getBeginTime(), this.getEndTime());
        }

        if (CollUtil.isNotEmpty(dateList)) {
            for (LocalDate localDate : dateList) {
                timeList.add(localDate.atStartOfDay());
            }
        }

        return timeList;
    }
}
