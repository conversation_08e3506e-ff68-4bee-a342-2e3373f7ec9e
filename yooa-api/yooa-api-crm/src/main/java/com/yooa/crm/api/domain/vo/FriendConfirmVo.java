package com.yooa.crm.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import lombok.Data;

import java.time.LocalDate;


@Data
public class FriendConfirmVo {

    private Long confirmId;

    @Excel(name = "日期")
    private LocalDate friendDate;

    private Long extendDeptId;

    @Excel(name = "推广父部门")
    private String extendDeptAncestorName;

    @Excel(name = "推广部门")
    private String extendDeptName;

    private String extendId;

    @Excel(name = "推广")
    private String extendName;

    private Long pitcherDeptId;

    @Excel(name = "投手父部门")
    private String pitcherDeptAncestorName;

    @Excel(name = "投手部门")
    private String pitcherDeptName;

    private Long pitcherId;

    @Excel(name = "投手")
    private String pitcherName;

    private Long mainChannelId;

    @Excel(name = "对公类型", readConverterExp = "1=对公,2=对私")
    private String publicType;

    @Excel(name = "主渠道")
    private String mainChannelName;

    private Long subChannelId;

    @Excel(name = "子渠道")
    private String subChannelName;

    @Excel(name = "性别", readConverterExp = "0=男,1=女")
    private String sex;

    @Excel(name = "语言", readConverterExp = "1=中文,2=英文")
    private String language;

    @Excel(name = "总好友数")
    private Long friendNum;

    @Excel(name = "有效好友数")
    private Long validFriendNum;

    @Excel(name = "状态", readConverterExp = "1=待确认,2=已确认,3=已驳回,4=已撤回")
    private String status;

    // @Excel(name = "备注")
    private String remark;

    @Excel(name = "备注")
    private String confirmRemark;

    @Excel(name = "理由")
    private String reviewRemark;
}
