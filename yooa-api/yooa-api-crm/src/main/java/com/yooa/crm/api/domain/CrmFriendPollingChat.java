package com.yooa.crm.api.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.core.annotation.Excel;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.*;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 好友表
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CrmFriendPollingChat extends BaseEntity {

    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 轮询api客户id
     */
    private String pollingApiUserId;

    /**
     * 消息id
     */
    private Long sequenceId;

    /**
     * 信息文本
     */
    private String text;


    /**
     * 消息类型 0-未定义 1-文本 2-图片 3-模板 4-文件 5-回传 6-视频 7-邮件 8-系统消息
     */
    private String msgType;

    /**
     * 发送人类型 1-用户 2-客服 3-机器人
     */
    private String senderType;

    /**
     * 发送消息时间
     */
    private LocalDateTime sendTime;

    /**
     * 发送人头像
     */
    private String senderAvatar;

    /**
     * 发送消息者id
     */
    private String senderId;

}