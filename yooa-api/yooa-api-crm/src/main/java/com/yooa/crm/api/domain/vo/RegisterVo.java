package com.yooa.crm.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import com.yooa.common.core.annotation.Excels;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegisterVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户id
     */
    @Excel(name = "客户ID")
    private Long customerId;

    /**
     * 好友id
     */
    @Excel(name = "好友ID")
    private Long friendId;

    /**
     * 好友名称
     */
    @Excel(name = "客户名")
    private String friendName;

    /**
     * 好友录入时间
     */
    @Excel(name = "加好友时间")
    private LocalDate recordDate;

    /**
     * 客户流失时间
     */
    @Excel(name = "客户解绑时间")
    private LocalDateTime loseTime;

    /**
     * 一级渠道名称
     */
    @Excel(name = "一级渠道名称")
    private String mainChannelName;

    /**
     * 二级渠道名称
     */
    @Excel(name = "二级渠道名称")
    private String subChannelName;

    /**
     * 渠道名称（主渠道-子渠道）
     */
    @Excel(name = "渠道名称")
    private String channelName;

    /**
     * 渠道负责人/投手 名称
     */
    private String channelUserName;

    /**
     * 渠道负责人/投手 昵称
     */
    @Excel(name = "投手")
    private String channelNickName;

    /**
     * 优质达成时间
     */
    @Excel(name = "优质达成时间")
    private LocalDateTime qualityTime;

    /**
     * 客户注册时间
     */
    @Excel(name = "注册时间")
    private LocalDateTime createTime;

    /**
     * 客户更新时间
     */
    @Excel(name = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 最后登入时间
     */
    @Excel(name = "最后登入时间")
    private LocalDateTime lastLoginTime;

    @Excel(name = "大小号")
    private String accountType;

    /**
     * 推广信息
     */
    @Excels({
            @Excel(name = "推广负责人", targetAttr = "nickName", type = Excel.Type.EXPORT),
            @Excel(name = "推广上级部门", targetAttr = "ancestorsNames", type = Excel.Type.EXPORT),
            @Excel(name = "推广部门", targetAttr = "deptName", type = Excel.Type.EXPORT)
    })
    private ExtendVo extend;

    /**
     * 客服信息
     */
    @Excels({
            @Excel(name = "客服负责人", targetAttr = "nickName", type = Excel.Type.EXPORT),
            @Excel(name = "客服上级部门", targetAttr = "ancestorsNames", type = Excel.Type.EXPORT),
            @Excel(name = "客服部门", targetAttr = "deptName", type = Excel.Type.EXPORT)
    })
    private ServeVo serve;

    /**
     * 运营主播信息
     */
    private List<AnchorOperateVo> anchorOperate;

    /**
     * 导出时用的主播信息json
     */
    @Excel(name = "运营主播信息集")
    private String anchorOperateJSON;
}
