package com.yooa.crm.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 客户跟进记录表
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CrmCustomerFollowRecord extends BaseEntity {

    /**
     * 跟进记录id
     */
    @TableId(value = "follow_id", type = IdType.AUTO)
    private Long followId;

    /**
     * 跟进时间
     */
    private LocalDate followDate;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 文字内容
     */
    private String textContent;

    /**
     * 图片内容（多个以,分割）
     */
    private String imageContent;
}