package com.yooa.crm.api.domain.dto;

import com.yooa.crm.api.domain.CrmAnchorInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;

/**
 * 主播信息表
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class AnchorInfoEditDto extends CrmAnchorInfo {

    @NotNull(message = "主播id不能为空")
    private Long anchorId;

    /**
     * 主播姓名
     */
    @NotBlank(message = "主播姓名不能为空")
    private String anchorName;


    /**
     * 邀约用户ID
     */
    @NotNull(message = "邀约用户不能为空")
    private Long inviterId;

    /**
     * 地区
     */
    // @NotBlank(message = "地区不能为空")
    private String region;

    /**
     * 语言（1中文 2英文）
     */
    // @NotBlank(message = "语言不能为空")
    private String language;

    /**
     * 民族
     */
    @NotBlank(message = "民族不能为空")
    private String nation;

    /**
     * 籍贯
     */
    @NotBlank(message = "籍贯不能为空")
    private String nativePlace;

    /**
     * 婚姻（1未婚 2已婚 3其他）
     */
    @NotBlank(message = "婚姻状态不能为空")
    private String marital;

    /**
     * 身份证号码
     */
    // @NotBlank(message = "身份证号码不能为空")
    private String idCardNumber;

    /**
     * 生日日期
     */
    @NotNull(message = "生日日期不能为空")
    private LocalDate birthdayDate;

    /**
     * 年龄
     */
    @NotNull(message = "年龄不能为空")
    private Integer age;

    /**
     * 性别（0男 1女）
     */
    @NotBlank(message = "性别不能为空")
    private String sex;

    /**
     * 身高
     */
    @NotNull(message = "身高不能为空")
    private Double height;

    /**
     * 体重
     */
    @NotNull(message = "体重不能为空")
    private Double weight;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String phone;

    /**
     * 户籍所在地
     */
    @NotBlank(message = "户籍所在地不能为空")
    private String registeredResidence;

    /**
     * 现住地址
     */
    @NotBlank(message = "现住地址不能为空")
    private String address;

    /**
     * 健康状态（1良好 2一般 3其他）
     */
    @NotBlank(message = "健康状态不能为空")
    private String healthStatus;

    /**
     * 是否有传染病（Y是 N否）
     */
    @NotBlank(message = "是否有遗传病不能为空")
    private String isInfectiousDiseases;

    /**
     * 紧急联系人姓名
     */
    @NotBlank(message = "紧急联系人姓名不能为空")
    private String emergencyContactName;

    /**
     * 紧急联系人类型
     */
    @NotBlank(message = "紧急联系人类型不能为空")
    private String emergencyContactType;

    /**
     * 紧急联系人电话
     */
    @NotBlank(message = "紧急联系人电话不能为空")
    private String emergencyContactPhone;

    /**
     * 是否有亲属在公司（Y是 N否）
     */
    @NotBlank(message = "是否有亲属在公司不能为空")
    private String isRelativesHere;

}