package com.yooa.crm.api.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * 主播信息表
 */
@Data
public class AnchorInfoEditStatusDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull(message = "主播id不能为空")
    private Long anchorId;

    /**
     * 状态（0待面试 1面试中 2初试未过 3待复试 4复试中 5复试未过 6待签约 7已签约 8已解约）
     */
    @NotBlank(message = "主播状态不能为空")
    private String anchorStatus;

    /**
     * 初试人id
     */
    private Long firstRecruiterId;

    /**
     * 复试人id
     */
    private Long finalRecruiterId;

    /**
     * 合作时间/签约时间
     */
    private LocalDate cooperationDate;
    
    /**
     * 备注/通过、未通过理由
     */
    private String remark;


}