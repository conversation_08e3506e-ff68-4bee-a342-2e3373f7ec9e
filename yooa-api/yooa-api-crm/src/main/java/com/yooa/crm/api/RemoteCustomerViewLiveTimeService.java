package com.yooa.crm.api;

import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.constant.ServiceNameConstants;
import com.yooa.common.core.domain.R;
import com.yooa.crm.api.factory.RemoteCustomerViewLiveTimeFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 用户观看直播时间
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteCustomerViewLiveTimeService", value = ServiceNameConstants.CRM_SERVICE, fallbackFactory = RemoteCustomerViewLiveTimeFallbackFactory.class)
public interface RemoteCustomerViewLiveTimeService {

    String ClassPath = "/customer/view/live/time";

    /**
     * 定时刷新客户质量状态
     */
    @PostMapping(ClassPath + "/refreshQuality")
    R<Integer> refreshQuality(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

}
