package com.yooa.crm.api.domain;

import java.time.LocalDateTime;
import java.util.Date;
import java.io.Serializable;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * 客户交接表(CrmCustomerHandover)实体类
 *
 * <AUTHOR>
 * @since 2025-06-09 14:23:42
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("crm_customer_handover")
public class CrmCustomerHandover implements Serializable {
    private static final long serialVersionUID = 467019227330427792L;

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long handoverId;


    /**
     * 客户id
     */
    private Long customerId;

    /**
     * PY推广id
     */
    private Long pyExtendId;

    /**
     * PY主播id
     */
    private Long pyAnchorId;

    /**
     * PY运营id
     */
    private Long pyOperateId;

    /**
     * PY客服id
     */
    private Long pyServeId;

    /**
     * 交接类型（1一交 2二交）
     */
    private String handoverType;

    /**
     * 交接次数（1首次交接 2多次交接）
     */
    private String handoverNum;

    /**
     * 交接状态（0待交接 1已交接 2拒绝交接）
     */
    private String handoverStatus;

    /**
     * 交接信息
     */
    private String handoverInfo;

    /**
     * 交接图片
     */
    private String handoverImg;

    /**
     * 交接时间
     */
    private LocalDateTime handoverTime;

    /**
     * 接收时间
     */
    private LocalDateTime receiveTime;

    /**
     * 拒收信息
     */
    private String rejectInfo;

    /**
     * crm_customer_friend id
     */
    private Long customerFriendId;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改者
     */
    private Long updateBy;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * OA推广id
     */
    private Long extendId;

    /**
     * OA运营id
     */
    private Long operateId;

    /**
     * OA客服id
     */
    private Long serveId;


    /**
     * 脱手时间
     */
    private LocalDateTime loseTime;


}
