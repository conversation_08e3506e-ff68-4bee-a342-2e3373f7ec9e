package com.yooa.crm.api.domain.vo;

import com.yooa.crm.api.domain.CrmCustomerOrder;
import lombok.Data;

import java.util.List;

/**
 * 投放 - 充值记录
 */
@Data
public class RechargeRecordPitcherVo extends CrmCustomerOrder {
    /**
     * 客户昵称
     */
    private String customerName;
    /**
     * 投手昵称
     */
    private String pitcherName;

    /**
     * 客户账号
     */
    private String customerAccount;

    /**
     * 来源（推广名称）
     */
    private String extendName;

    /**
     * VIP客服
     */
    private String vipName;

    /**
     * 运营名称
     */
    private String operateName;
}
