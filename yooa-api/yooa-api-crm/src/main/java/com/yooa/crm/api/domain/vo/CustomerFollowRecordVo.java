package com.yooa.crm.api.domain.vo;

import com.yooa.crm.api.domain.CrmCustomerFollowRecord;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 *
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerFollowRecordVo extends CrmCustomerFollowRecord {

    /**
     * 评论条数
     */
    private Long commentNumber;

    /**
     * 评论详情
     */
    private CustomerFollowCommentVo comment;

    /**
     * 创建者
     */
    private String createName;
}
