package com.yooa.crm.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import com.yooa.crm.api.domain.CrmAnchorInfo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@EqualsAndHashCode(callSuper = true)
@Data
public class AnchorInfoVo extends CrmAnchorInfo {

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 主播名称
     */
    @Excel(name = "主播名称")
    private String anchorName;

    /**
     * 主播性别
     */
    @Excel(name = "性别", readConverterExp = "0=男,1=女")
    private String sex;

    /**
     * 语言
     */
    @Excel(name = "语言", readConverterExp = "1=中文,2=英文")
    private String language;

    /**
     * 面试日期
     */
    @Excel(name = "面试日期")
    private LocalDate interviewDate;

    /**
     * 签约日期
     */
    @Excel(name = "签约日期")
    private LocalDate cooperationDate;

    /**
     * 主播类别
     */
    @Excel(name = "类别", readConverterExp = "1=线上,2=线下,3=兼职")
    private String anchorType;

    /**
     * 主播风格
     */
    @Excel(name = "风格")
    private String anchorStyleName;

    /**
     * 运营部门名称
     */
    @Excel(name = "运营部门")
    private String operateDeptName;

    /**
     * 运营部门父名称
     */
    @Excel(name = "运营父部门名")
    private String operateDeptAncestorsNames;

    /**
     * 运营昵称
     */
    @Excel(name = "运营昵称")
    private String operateNickName;

    /**
     * 主播状态
     */
    @Excel(name = "状态", readConverterExp = "0=待面试,1=面试中,2=初试未过,3=待复试,4=复试中,5=复试未过,6=待签约,7=已签约,8=已解约")
    private String anchorStatus;

    /**
     * 邀约人
     */
    @Excel(name = "邀约人")
    private String inviterName;

    /**
     * 初试官
     */
    @Excel(name = "初试官")
    private String firstRecruiterName;

    /**
     * 复试官
     */
    @Excel(name = "复试官")
    private String finalRecruiterName;
}
