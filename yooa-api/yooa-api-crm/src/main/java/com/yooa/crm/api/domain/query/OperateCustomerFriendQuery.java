package com.yooa.crm.api.domain.query;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;


@Data
public class OperateCustomerFriendQuery extends QueryEntity {

    // 用户ID
    @NotNull(message = "缺少用户ID!")
    private Long userId;

    // 权限范围(0:查询自己、1:查询包含下级)
    @NotNull(message = "请查询范围!")
    private Long dataScope;

    // 查询类型(0:全部、1:推广、2:VIP、3:运营)
    private List<Integer> dataScopeType;

    // 部门ID
    private Long deptId;

    // 搜索类型(0:客户(客户ID、好友名)、1:推广(推广负责人、渠道负责人)、2:客服(客服负责人)、3:运营(运营负责人、主播名))
    private Long searchType;

    // 搜索框内容(searchType)
    private String searchBox;

    // 领取人
    private Long receiveId;

    // 地区
    private Long area;

    // 语言(字典)
    private Long language;

    // 粉丝类型（1男粉 2女粉 3英文粉 4中文粉）
    private Long fansType;

    // 职业
    private Long workType;

    // 情感
    private Long demand;

    // 性别(字典)
    private Long sex;

    // 状态(1首次、2多次)
    private Long status;

    // 绑定状态(0:在绑、1:解绑)
    private Long bindStatus;

    // 类型(0:无充值、1:运营英文100粉、2:200粉、3:500粉、4:5k粉、5:5w粉、6:10w粉、7:首次打赏粉)
    private Long type;

    /**
     * 查询开始金额
     */
    private Long beginMoney;

    /**
     * 查询结束金额
     */
    private Long endMoney;

    /**
     * 查询开始总金额
     */
    private Long beginTotalMoney;

    /**
     * 查询结束总金额
     */
    private Long endTotalMoney;

    /**
     * 开始年龄
     */
    private Long beginAge;

    /**
     * 结束年龄
     */
    private Long endAge;

    /**
     * 主播语言（1中文 2英文）
     */
    private Long anchorLanguage;

    /**
     * 主播性别（0男 1女）
     */
    private Long anchorSex;

    /**
     * 注册开始时间
     */
    private LocalDateTime createBeginTime;

    /**
     * 注册结束时间
     */
    private LocalDateTime createEndTime;

    /**
     * 修改开始时间
     */
    private LocalDateTime updateBeginTime;

    /**
     * 修改结束时间
     */
    private LocalDateTime updateEndTime;

    /**
     * 打赏筛选开始时间
     */
    private LocalDateTime rechargeBeginTime;

    /**
     * 打赏筛选结束时间
     */
    private LocalDateTime rechargeEndTime;

    /**
     * 粉丝达成筛选开始时间
     */
    private LocalDateTime fansBeginTime;

    /**
     * 粉丝达成筛选结束时间
     */
    private LocalDateTime fansEndTime;

    /**
     * 一交接收开始时间
     */
    private LocalDateTime recordBeginTime;

    /**
     * 一交接收结束时间
     */
    private LocalDateTime recordEndTime;

    /**
     * 最后登入开始时间
     */
    private LocalDateTime lastLoginBeginTime;

    /**
     * 最后登入结束时间
     */
    private LocalDateTime lastLoginEndTime;

    /**
     * 二交开始时间
     */
    private LocalDateTime serveBeginTime;

    /**
     * 二交结束时间
     */
    private LocalDateTime serveEndTime;

    /***二次查询的条件***/

    /**
     * 好友ID集
     */
    private List<Long> friendIds;

    /**
     * 运营人员ID集
     */
    private List<Long> operateIds;

    /**
     * 客户ID集
     */
    private List<Long> customerIds;

    /**
     * true:分页、false:不分页
     */
    private boolean sizeBl;
}
