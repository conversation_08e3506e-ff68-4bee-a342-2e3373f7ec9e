package com.yooa.crm.api.factory;

import com.yooa.common.core.domain.R;
import com.yooa.crm.api.RemoteCustomerJoinServeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RemoteCustomerJoinServeFallbackFactory implements FallbackFactory<RemoteCustomerJoinServeService> {

    @Override
    public RemoteCustomerJoinServeService create(Throwable throwable) {

        log.error("RemoteCustomerJoinServeService Error：{}", throwable.getMessage());

        return new RemoteCustomerJoinServeService() {

            @Override
            public R<?> correctionExpireTime(String source) {
                return R.fail("expireTimeCorrection Error：" + throwable.getMessage());
            }
        };
    }
}
