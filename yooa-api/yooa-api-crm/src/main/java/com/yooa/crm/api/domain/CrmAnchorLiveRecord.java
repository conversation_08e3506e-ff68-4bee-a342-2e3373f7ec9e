package com.yooa.crm.api.domain;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * 直播记录表(CrmAnchorLiveRecord)实体类
 *
 * <AUTHOR>
 * @since 2025-03-04 17:31:59
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("crm_anchor_live_record")
public class CrmAnchorLiveRecord implements Serializable {
    private static final long serialVersionUID = 399934973540721607L;


    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 平台 1:poyo 2:潘多拉 3:助手
     */
    private Integer appProject;

    /**
     * 用户ID
     */
    private Long anchorId;

    /**
     * 直播标识
     */
    private Integer showId;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 本次直播收益
     */
    private BigDecimal votes;

    /**
     * 直播分类ID
     */
    private Integer liveClassId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;


}
