package com.yooa.crm.api.domain.dto;

import com.yooa.crm.api.domain.CrmFriendConfirm;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;


@EqualsAndHashCode(callSuper = true)
@Data
public class FriendConfirmEditDto extends CrmFriendConfirm {

    @NotNull(message = "id不能为空")
    private Long confirmId;

    @NotNull(message = "日期不能为空")
    // @Past(message = "日期不能晚于当前日期")
    private LocalDate friendDate;

    @NotNull(message = "投手id不能为空")
    private Long pitcherId;

    @NotNull(message = "总好友数不能为空")
    private Integer friendNum;

    @NotNull(message = "总有效好友数不能为空")
    private Integer validFriendNum;

    @NotBlank(message = "对公类型不能为空")
    private String publicType;

    @NotNull(message = "主渠道不能为空")
    private Long mainChannelId;

    @NotNull(message = "子渠道不能为空")
    private Long subChannelId;

    @NotBlank(message = "粉丝类型不能为空")
    private String sex;

    @NotBlank(message = "语言不能为空")
    private String language;
}