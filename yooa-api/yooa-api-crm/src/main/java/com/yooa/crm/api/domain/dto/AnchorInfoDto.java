package com.yooa.crm.api.domain.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR> xh
 * @Date: 2025/3/31 11:25
 * @Description:
 */
@Data
public class AnchorInfoDto {

    /**
     * 主播姓名
     */
    @Excel(name = "主播姓名")
    private String anchorName;


    /**
     * 地区
     */
    @Excel(name = "地区", orderNum = "1")
    private String region;

    /**
     * 语言（1中文 2英文）
     */
    @Excel(name = "语言", replace = {"中文_1","英文_2"}, orderNum = "2")
    private String language;

    /**
     * 民族
     */
    @Excel(name = "民族", orderNum = "3")
    private String nation;

    /**
     * 籍贯
     */
    @Excel(name = "籍贯", orderNum = "4")
    private String nativePlace;

    /**
     * 身份证号码
     */
    @Excel(name = "身份证号码", orderNum = "5")
    private String idCardNumber;

    /**
     * 生日日期
     */
    @Excel(name = "生日日期", orderNum = "6")
    private LocalDate birthdayDate;

    /**
     * 年龄
     */
    @Excel(name = "年龄", orderNum = "7")
    private Integer age;

    /**
     * 性别（0男 1女）
     */
    @Excel(name = "性别", replace = {"男_0","女_1"}, orderNum = "8")
    private String sex;

    /**
     * 手机号
     */
    @Excel(name = "手机号", orderNum = "9")
    private String phone;



}
