package com.yooa.crm.api.domain.query;

import com.yooa.common.core.annotation.Excel;
import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;


@Data
@EqualsAndHashCode
public class PollingFriendQuery extends QueryEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *  业务类型
     *  pitcher：投放
     *  extend：推广
     */
    private String businessType;

    /**
     * 轮询类型1：有效,2:无效
     */
    private Long pollingType;

    /**
     * 联系方式（api渠道）
     */

    private String pollingApiChannel;

    /**
     * 渠道（子渠道）
     */
    private Long subChannelId;

    /**
     * 加好友时间
     */
    private LocalDate[] recordDate;

    /**
     * 好友名称
     */
    private String friendName;

    /**
     * 搜索框
     */
    private String searchBox;

    /**
     * 会话数开始范围
     */
    private Long chatStart;
    /**
     * 会话数结束范围
     */
    private Long chatEnd;
    /**
     * 有效客户开始范围
     */
    private Long invalidStart;
    /**
     * 有效客户结束范围
     */
    private Long invalidEnd;
    /**
     * 有效率开始范围
     */
    private Double invalidRateStart;
    /**
     * 有效率结束范围
     */
    private Double invalidRateEnd;
    /**
     * 注册数开始范围
     */
    private Long registeredStart;
    /**
     * 注册数结束范围
     */
    private Long registeredEnd;
    /**
     * 注册率开始范围
     */
    private Double registrationRateStart;
    /**
     * 注册率结束范围
     */
    private Double registrationRateEnd;

    /**
     * 推广名称
     */
    private String extendName;

    /**
     * 轮询状态1待审核，2已确认，3已驳回
     */
    private Long state;
}
