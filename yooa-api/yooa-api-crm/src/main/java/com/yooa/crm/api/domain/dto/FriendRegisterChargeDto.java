package com.yooa.crm.api.domain.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/9/13 下午1:47
 */
@Data
public class FriendRegisterChargeDto {

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 父部门id
     */
    private Long parentId;

    /**
     * 部门层级
     */
    private Integer hierarchy;

    /**
     * 部门类型(字典)
     */
    private Integer deptType;

    /**
     * 粉丝类型(1中文男粉 2中文女粉 3英文男粉 4英文女粉)
     */
    private Integer fansType;

    /**
     * 输出类型(0:月、1周、2:天、3小时)(给柱状图、折线图的分组)
     */
    private Integer expType;

    /**
     * 开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 用户id集
     */
    private List<Long> userIds;

    /**
     * 包含部门ID
     */
    private Long findInSetDeptId;


}
