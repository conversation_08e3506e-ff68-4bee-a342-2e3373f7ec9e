package com.yooa.crm.api.factory;

import com.yooa.common.core.domain.R;
import com.yooa.crm.api.RemoteCustomerViewLiveTimeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 用户观看直播时间
 */
@Slf4j
@Component
public class RemoteCustomerViewLiveTimeFallbackFactory implements FallbackFactory<RemoteCustomerViewLiveTimeService> {

    @Override
    public RemoteCustomerViewLiveTimeService create(Throwable throwable) {
        log.error("用户观看直播服务调用失败:{}", throwable.getMessage());
        return new RemoteCustomerViewLiveTimeService() {
            @Override
            public R<Integer> refreshQuality(String source) {
                return R.fail("定时刷新客户质量状态失败:" + throwable.getMessage());
            }
        };
    }

}
