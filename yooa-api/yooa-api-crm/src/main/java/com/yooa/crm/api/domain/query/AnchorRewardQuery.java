package com.yooa.crm.api.domain.query;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR> xh
 * @Date: 2025/3/19 15:34
 * @Description:
 */
@Data
public class AnchorRewardQuery {

    @NotNull(message = "主播id不能为空")
    private Long anchorId;

    /**
     * 最低打赏次数
     */
   // private String minRewardNum;

    /**
     * 最高打赏次数
     */
   // private String maxRewardNum;

    /**
     * 最低打赏金额
     */
    private BigDecimal minRewardAmount;

    /**
     * 最高打赏金额
     */
    private BigDecimal maxRewardAmount;

    /**
     * 打赏开始时间
     */
    private LocalDateTime startTime;

    /**
     * 打赏结束时间
     */
    private LocalDateTime endTime;

    /**
     * 根据客户id 客户姓名查询
     */
    private String queryId;

    /**
     * 根据主播账号id查询
     */
    private Long anchorAccountId;

}
