package com.yooa.crm.api.domain.dto;

import cn.hutool.core.collection.CollUtil;
import com.yooa.common.core.utils.LocalDateUtil;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 数据汇总入参类
 */
@Data
public class ExtendDataDto {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 部门层级
     */
    private Integer deptLevel;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * 父部门id
     */
    private Long parentId;

    /**
     * 包含部门ID
     */
    private Long findInSetDeptId;

    /**
     * 开始时间
     */
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 查询类型(部门类型)(注:本来要改为deptType,但前端需要改的地方比较多,所以暂时不改)
     */
    private Integer deptType;

    /**
     * 粉丝类型(1中文男粉 2中文女粉 3英文男粉 4英文女粉)
     */
    private Integer fansType;

    /**
     * 字段(0:总业绩、1:新增业绩、2:好友、3:注册、4:优质、5:交接、6:二交、7:首充、8:200粉、9:500粉、10:5k粉、11:5w粉、12:10w粉、13:40w粉、14:100w粉、
     * 15:注册率、16:交接率、17:二交率、18:付费率)
     */
    private Integer fields;

    /**
     * 输出类型(0:月、1周、2:天、3小时(先不做))(给柱状图、折线图的分组、...)
     */
    private Integer expType;

    /**
     * 同比环比:(0年、1月、2周、3天、4季)
     */
    private Integer compare;

    /**
     * 查询等级(0:部门、1:个人)
     */
    private Integer lv;

    /**
     * 搜索框
     */
    private String searchBox;

    /**
     * 目标字段id
     */
    private Integer fieldsGoalId;

    /**
     * 审核状态(字典)
     */
    private Integer auditStatus;

    /**
     * 打包ID
     */
    private Integer goalId;

    /**
     * 级别
     */
    private Integer rank;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 好友ID集
     */
    private List<Long> friendIds;

    /**
     * 用户ID集
     */
    private List<Long> userIds;

    /**
     * 部门ID集
     */
    private List<Long> deptIds;

    private LocalDateTime compareBeginTime;
    private LocalDateTime compareEndTime;
    /**
     * 推前时间
     */
    private int year = 0;
    private int month = 0;
    private int day = 0;
    /**
     * 环比时间
     */
    private int withYear = 0;
    private int withDay = 0;
    /**
     * 同比时间
     */
    private int ringMonth = 0;
    private int ringDay = 0;

    public Integer getFieldsName(String fieldsName) {
        switch (fieldsName) {
            case "realProfit":            // 总业绩
                return 0;
            case "fortyFiveDays":         // 新增业绩
                return 1;
            case "friend":                // 好友
                return 2;
            case "register":              // 注册
                return 3;
            case "qualityUsers":          // 优质
                return 4;
            case "receive":               // 交接
                return 5;
            case "receiveVip":            // 二交
                return 6;
            case "firstCharge":           // 首充
                return 7;
            case "fans2h":                // 200粉
                return 8;
            case "fans5h":                // 500粉
                return 9;
            case "fans5k":                // 5k粉
                return 10;
            case "fans5w":                // 5w粉
                return 11;
            case "fans10w":               // 10w粉
                return 12;
            case "fans40w":               // 40w粉
                return 13;
            case "fans100w":              // 100w粉
                return 14;
        }
        return null;
    }

    //推前时间数字计算
    public void computeForward() {
        if (this.compare == 0) {     // 年
            year = -1;
        } else if (this.compare == 1) {       // 月
            month = -1;
        } else if (this.compare == 2) {       // 周
            day = -7;
        } else if (this.compare == 3) {       // 天
            day = -1;
        } else if (this.compare == 4) {       // 季
            month = -3;
        }
    }

    //推前时间(使用完要调用下面的时间还原方法)
    public void forwardDate() {
        this.compareBeginTime = this.beginTime;
        this.compareEndTime = this.endTime;
        this.beginTime = this.beginTime.plusYears(year).plusMonths(month).plusDays(day);
        this.endTime = this.endTime.plusYears(year).plusMonths(month).plusDays(day);
    }

    //同步环比时间数字计算
    public void compareInt() {
        if (this.compare == 0) {     // 年
            this.withYear = -1;
        } else if (this.compare == 1) {       // 月
            this.withYear = -1;
            this.ringMonth = -1;
        } else if (this.compare == 2) {       // 周
            this.withDay = -7;
            this.ringDay = -28;
        } else if (this.compare == 3) {       // 天
            this.withDay = -1;
            this.ringDay = -7;
        }
    }

    //同比计算时间(使用完要调用下面的时间还原方法)
    public void withDate() {
        this.compareBeginTime = this.beginTime;
        this.compareEndTime = this.endTime;
        this.beginTime = this.beginTime.plusYears(withYear).plusDays(withDay);
        this.endTime = this.endTime.plusYears(withYear).plusDays(withDay);
    }

    //环比计算时间(使用完要调用下面的时间还原方法)
    public void ringDate() {
        this.compareBeginTime = this.beginTime;
        this.compareEndTime = this.endTime;
        this.beginTime = this.beginTime.plusMonths(ringMonth).plusDays(ringDay);
        this.endTime = this.endTime.plusMonths(ringMonth).plusDays(ringDay);
    }

    //时间参数恢复方法
    public void restore() {
        this.beginTime = this.compareBeginTime;
        this.endTime = this.compareEndTime;
    }

    //查询方向(根据字段,决定去查数据汇总,crm,粉丝登记)
    public int selDirection() {
        if (this.getFields() == 0 || this.getFields() == 4) {                               // 查crm好友、注册:1
            return 1;
        } else if (this.getFields() < 9 && this.getFields() != 5) {                         // 查询数据汇总:0
            return 0;
        } else {                                                                            // 查询粉丝登记:2
            return 2;
        }
    }

    //计算两个时间的差集合(根据要返回的类型)
    public List<LocalDateTime> computeDatePoorList() {
        List<LocalDate> dateList = new ArrayList<>();            // 时间分组
        List<LocalDateTime> timeList = new ArrayList<>();

        if (this.getExpType() == 1 || this.getExpType() == 2) {      // 已天为单位
            dateList = LocalDateUtil.getDatesBetween(this.getBeginTime().toLocalDate(), this.getEndTime().toLocalDate());
        } else if (this.getExpType() == 0) {                                   // 已月为单位(默认为月初第一天)
            dateList = LocalDateUtil.getMonthsBetween(this.getBeginTime().toLocalDate(), this.getEndTime().toLocalDate());
        } else if (this.getExpType() == 3) {                                    // 已小时为单位
            timeList = LocalDateUtil.getHoursBetween(this.getBeginTime(), this.getEndTime());
        }

        if (CollUtil.isNotEmpty(dateList)) {
            for (LocalDate localDate : dateList) {
                timeList.add(localDate.atStartOfDay());
            }
        }

        return timeList;
    }
}
