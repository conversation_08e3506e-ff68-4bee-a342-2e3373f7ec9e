package com.yooa.crm.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;

/**
 * 贵族信息表
 */
@Data
public class CrmNoble implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 礼物id
     */
    @TableId(value = "noble_id", type = IdType.AUTO)
    private Long nobleId;

    /**
     * 礼物名称
     */
    private String nobleName;
}
