package com.yooa.crm.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 投手注册列表VO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PitcherRegisterVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户名
     */
    @Excel(name = "客户名")
    private String friendName;

    /**
     * 客户ID
     */
    @Excel(name = "客户ID")
    private Long customerId;

    /**
     * 加好友时间
     */
    @Excel(name = "加好友时间")
    private LocalDate recordDate;

    /**
     * 最后登录时间
     */
    @Excel(name = "最后登录时间")
    private LocalDateTime lastLoginTime;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间")
    private LocalDateTime updateTime;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间")
    private LocalDateTime createTime;

    /**
     * 绑出时间
     */
    @Excel(name = "绑出时间")
    private LocalDateTime endTime;

    /**
     * 投手
     */
    @Excel(name = "投手")
    private String pitcherName;

    /**
     * 推广ID
     */
    @Excel(name = "推广ID")
    private Long extendUserId;

    /**
     * 推广
     */
    @Excel(name = "推广")
    private String extendNickName;

    /**
     * 推广部门ID
     */
    @Excel(name = "推广部门ID")
    private Long extendDeptId;

    /**
     * 推广部门
     */
    @Excel(name = "推广部门")
    private String extendDeptName;

    /**
     * 推广父部门
     */
    @Excel(name = "推广父部门")
    private String extendAncestorsNames;

    /**
     * 渠道名称（主渠道-子渠道）
     */
    @Excel(name = "渠道名称")
    private String channelName;

    /**
     * 主渠道名称
     */
    private String mainChannelName;

    /**
     * 子渠道名称
     */
    private String subChannelName;

    /**
     * 好友ID
     */
    private Long friendId;
}
