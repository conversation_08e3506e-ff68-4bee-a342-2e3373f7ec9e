package com.yooa.crm.api.domain.vo;

import lombok.Data;

/**
 * <AUTHOR> xh
 * @Date: 2025/7/8 17:32
 * @Description:
 */
@Data
public class MyAnchorCollectVo {

    /**
     * 在播数
     */
    private Long beginToShow;
    /**
     * 停播数
     */
    private Long discontinueBroadcasting;
    /**
     * 休播数
     */
    private Long offAirBroadcast;
    /**
     * 待面试
     */
    private Long pendingInterview;
    /**
     * 面试中
     */
    private Long interview;
    /**
     * 待复试
     */
    private Long pendingRetest;
    /**
     * 复试中
     */
    private Long inTheSecondInterview;
    /**
     * 待签约
     */
    private Long toCooperate;
    /**
     * 已签约
     */
    private Long collaboratedAlready;
    /**
     * 已解约
     */
    private Long cancelCooperation;
    /**
     * 未通过
     */
    private Long fail;
}
