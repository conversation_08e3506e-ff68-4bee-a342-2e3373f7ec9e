package com.yooa.crm.api.domain.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.core.annotation.Excel;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.*;

import java.time.LocalDate;

/**
 * 轮询无效列表
 */
@Data
public class PollingFriendInvalidVo extends PollingFriendVo {
    /**
     * 状态
     */
    @Excel(name = "状态", readConverterExp = "1=待审核,2=已确认,3=已驳回")
    private Integer state;
}
