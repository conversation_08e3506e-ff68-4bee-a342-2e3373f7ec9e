package com.yooa.crm.api.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

/**
 * @Description crm统计的好友数、注册数、首充数(也可以查询排名、时间分组)
 * <AUTHOR>
 * @Date 2024/8/19 下午3:35
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FriendRegisterChargeVo {

    /**
     * OA用户ID/OA部门ID
     */
    private Long id;

    /**
     * OA用户名/OA部门名
     */
    private String name;

    /**
     * 好友数
     */
    private Long friend;

    /**
     * 注册数
     */
    private Long register;

    /**
     * 首充数
     */
    private Long firstCharge;

    /**
     * 时间
     */
    private LocalDate date;

    /**
     * 排名
     */
    private int ranking;
}
