package com.yooa.crm.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CrmAnchorOperate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * PD运营id
     */
    private Long operateId;

    /**
     * 接手时间
     */
    private LocalDateTime joinTime;

    /**
     * 脱手时间
     */
    private LocalDateTime loseTime;

}
