package com.yooa.crm.api.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class ExtendVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * PD推广id集
     */
    private String extendId;

    /**
     * 推广id
     */
    private Long userId;

    /**
     * 推广名称
     */
    private String userName;

    /**
     * 推广昵称
     */
    private String nickName;

    /**
     * 推广部门id
     */
    private Long deptId;

    /**
     * 推广部门名称
     */
    private String deptName;

    /**
     * 推广父部门名称集
     */
    private String ancestorsNames;

    /**
     * 接手时间
     */
    private LocalDateTime receiveTime;

    /**
     * 脱手时间
     */
    private LocalDateTime loseTime;

    /**
     * 客户ID
     */
    private Long customerId;
}
