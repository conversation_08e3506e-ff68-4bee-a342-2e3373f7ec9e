package com.yooa.crm.api.domain;

import java.time.LocalDateTime;
import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

/**
 * (CrmAnchorChargeRefund)实体类
 *
 * <AUTHOR>
 * @since 2025-03-05 13:28:12
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("crm_customer_charge_refund")
public class CrmCustomerChargeRefund implements Serializable {
    private static final long serialVersionUID = 269698399750536422L;

    /**
     * 订单id
     */
    @TableId
    private Long orderId;


    /**
     * 退款时间
     */
    private LocalDateTime refundTime;

    /**
     * app后台操作的管理员ID
     */
    private Long adminId;

    /**
     * app后台操作的管理员名字
     */
    private String adminName;

    /**
     * 状态 1:生成退款 2:冻结
     */
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;


}
