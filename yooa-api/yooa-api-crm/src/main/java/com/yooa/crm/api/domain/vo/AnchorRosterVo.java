package com.yooa.crm.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import com.yooa.common.core.annotation.ExcelDict;
import com.yooa.common.sensitive.annotation.Sensitive;
import com.yooa.common.sensitive.enums.DesensitizedType;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> xh
 * @Date: 2025/7/10 13:52
 * @Description:
 */
@Data
public class AnchorRosterVo {

    /**
     * 主播id
     */
    private Long anchorId;

    /**
     * 主播名称
     */
    @Excel(name = "主播名称")
    private String anchorName;

    /**
     * 主播花名
     */
    @Excel(name = "主播花名")
    private String anchorNickName;

    /**
     * PD主播名称
     */
    @Excel(name = "PD主播名称")
    private String pdAnchorName;
    /**
     * 手机号
     */
    @Excel(name = "手机号")
    @Sensitive(desensitizedType = DesensitizedType.PHONE)
    private String phone;


    /**
     * 学历
     */
    @Excel(name = "学历")
    private String educationType;

    /**
     * 状态（0待面试 1面试中 2待复试 3复试中 4待合作 5已合作 6取消合作 9未通过）
     */
    @Excel(name = "主播状态")
    @ExcelDict(dictType = "crm_anchor_status")
    private String anchorStatus;

    /**
     * 运营名称
     */
    @Excel(name = "运营名称")
    private String operateName;

    /**
     * 运营团队
     */
    @Excel(name = "运营团队")
    private String operateDeptName;

    /**
     * 父部门拼接
     */
    @Excel(name = "父部门")
    private String operateAncestorsName;


    /**
     * 性别
     */
    @Excel(name = "性别")
    @ExcelDict(dictType = "common_sex_type")
    private String sex;

    /**
     * 主播类型（1线上 2线下 3兼职）
     */
    @Excel(name = "主播类型")
    @ExcelDict(dictType = "crm_anchor_type")
    private String anchorType;

    /**
     * 主播风格
     */
    @Excel(name = "主播风格")
    private String anchorStyle;

    /**
     * 语言
     */
    @ExcelDict(dictType = "crm_anchor_language")
    @Excel(name = "语言")
    private String language;

    /**
     * 身份证
     */
    @Excel(name = "身份证")
    @Sensitive(desensitizedType = DesensitizedType.ID_CARD)
    private String idCardNumber;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间")
    private LocalDateTime createTime;

    /**
     * 签约时间
     */
    @Excel(name = "签约时间")
    private LocalDateTime cooperationDate;

    /**
     * 解约时间
     */
    @Excel(name = "解约时间")
    private LocalDateTime rescindTime;

}
