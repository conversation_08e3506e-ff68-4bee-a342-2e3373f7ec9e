package com.yooa.system.api.domain.vo;

import com.yooa.common.core.annotation.Excel;
import com.yooa.common.sensitive.annotation.Sensitive;
import com.yooa.common.sensitive.enums.DesensitizedType;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;


@Data
public class RosterSensitiveVo {

    /**
     * 花名册id
     */
    private Long rosterId;

    /**
     * 真实姓名
     */
    @Excel(name = "真实姓名")
    private String realName;

    /**
     * 部门父名称
     */
    @Excel(name = "部门父名称")
    private String deptAncestorsNames;

    /**
     * 部门名称
     */
    @Excel(name = "部门名称")
    private String deptName;

    /**
     * 部门id
     */
    private Long deptId;

    /**
     * OA ID
     */
    private String userId;

    /**
     * OA账号
     */
    @Excel(name = "OA账号")
    private String userName;

    /**
     * PY ID
     */
    @Excel(name = "PYID")
    private String pdUserIds;

    /**
     * 员工角色（1会长/总经理 2部长/总监 3厅长/经理 4组长/主管 5员工）
     */
    @Excel(name = "员工角色", readConverterExp = "1=总经理,2=总监,3=经理,4=主管,5=员工")
    private String employeeRole;

    /**
     * 职位类型（1推广 2运营 3财务 4职能 5事业部 6平台）
     */
    @Excel(name = "职位类型", readConverterExp = "1=推广,2=运营,3=财务,4=职能,5=事业部,6=平台")
    private String positionType;

    /**
     * 岗位名称
     */
    @Excel(name = "岗位名称")
    private String postName;

    /**
     * 岗位职级
     */
    @Excel(name = "岗位职级", readConverterExp = "1=P1,2=P2,3=P3,4=P4,5=M1-1,6=M1-2,7=M1-3,8=M1-4,9=M2-1,10=M2-2,11=M2-3,12=M3-1,13=M3-2,14=无")
    private String postRank;

    /**
     * 员工状态（0试用 1在职 2离职）
     */
    @Excel(name = "员工状态", readConverterExp = "0=试用,1=在职,2=离职")
    private String employeeStatus;

    /**
     * 学历类型（9小学 8初中 1高中 2中专 3大专 4本科 5硕士 6博士 7其他）
     */
    @Excel(name = "学历类型", readConverterExp = "9=小学,8=初中,1=高中,2=中专,3=大专,4=本科,5=硕士,6=博士,7=其他")
    private String educationalType;

    /**
     * 脱敏身份证号码
     */
    @Sensitive(desensitizedType = DesensitizedType.ID_CARD)
    @Excel(name = "身份证号码")
    private String idCardNumber;

    /**
     * 脱敏手机号
     */
    @Sensitive(desensitizedType = DesensitizedType.PHONE)
    @Excel(name = "手机号")
    private String phone;

    /**
     * 脱敏邮箱
     */
    @Sensitive(desensitizedType = DesensitizedType.EMAIL)
    @Excel(name = "邮箱")
    private String mail;

    /**
     * 试用期（1无试用期 21个月 32个月 43个月 54个月 65个月 76个月 8其他）
     */
    @Excel(name = "试用期", readConverterExp = "1=无试用期,2=1个月,3=2个月,4=3个月,5=4个月,6=5个月,7=6个月,8=其他")
    private String probationPeriod;

    /**
     * 入职日期
     */
    @Excel(name = "入职日期", dateFormat = "yyyy-MM-dd")
    private LocalDate employmentDate;

    /**
     * 离职日期
     */
    @Excel(name = "离职日期", dateFormat = "yyyy-MM-dd")
    private LocalDate resignationDate;

    /**
     * 部门
     */
    @Excel(name = "部门")
    private String teamName1;

    /**
     * 团队
     */
    @Excel(name = "团队")
    private String teamName2;

    /**
     * 小组
     */
    @Excel(name = "小组")
    private String teamName3;

    /**
     * 创建日期
     */
    private LocalDateTime createTime;
}

