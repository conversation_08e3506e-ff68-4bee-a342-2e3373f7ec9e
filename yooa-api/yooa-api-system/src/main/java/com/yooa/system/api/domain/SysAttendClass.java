package com.yooa.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 排班表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysAttendClass extends BaseEntity {

    /**
     * 用户id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 班次id
     */
    private Integer classId;
    /**
     * 班次名称
     */
    private String className;
    /**
     * 上班时间
     */
    private String onTime;
    /**
     * 下班时间
     */
    private String offTime;
    /**
     * 休息开始时间
     */
    private String restBeginTime;
    /**
     * 休息结束时间
     */
    private String restEndTime;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

}
