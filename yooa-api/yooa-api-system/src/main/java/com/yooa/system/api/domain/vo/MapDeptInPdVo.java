package com.yooa.system.api.domain.vo;

import com.yooa.system.api.domain.SysDept;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 *
 */
@Data
public class MapDeptInPdVo implements Serializable {

    //k:部门、v:部门下的pd用户ID集
    private Map<SysDept, List<Integer>> map;

    //pd用户ID集
    private List<Integer> pdIds;

    //人数(用于计算人均)
    private long peopleNumber;
}
