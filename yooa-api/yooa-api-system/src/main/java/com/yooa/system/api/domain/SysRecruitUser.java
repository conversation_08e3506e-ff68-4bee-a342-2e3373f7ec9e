package com.yooa.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 招聘人员分配表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysRecruitUser extends BaseEntity {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long Id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 流程ID
     */
    private Long processId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 招聘时间
     */
    private String hireData;

    /**
     * 专员数量
     */
    @NotBlank(message = "专员数量不能为空")
    private Long commissioner;

    /**
     * 管理数量
     */
    @NotBlank(message = "管理数量不能为空")
    private Long manage;
}
