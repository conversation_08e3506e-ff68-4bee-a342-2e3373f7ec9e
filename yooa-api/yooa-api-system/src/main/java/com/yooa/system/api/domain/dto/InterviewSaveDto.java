package com.yooa.system.api.domain.dto;

import com.yooa.system.api.domain.SysInterview;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;


@EqualsAndHashCode(callSuper = true)
@Data
public class InterviewSaveDto extends SysInterview {

    /**
     * 渠道类型（1BOSS 258 3内推 4返聘）
     */
    @NotBlank(message = "请选择渠道类型")
    private String channelType;

    /**
     * 内推人 (渠道为内推时字段不为空)
     */
    private Long channelId;

    /**
     * 应聘岗位
     */
    @NotBlank(message = "应聘岗位不能为空")
    private String position;

    /**
     * 期望薪资
     */
    @NotNull(message = "期望薪资不能为空")
    @DecimalMin(value = "1", message = "期望薪资应大于或等于1")
    private BigDecimal salaryExpectation;

    /**
     * 是否住宿（Y是 N否）
     */
    @NotBlank(message = "请选择是否住宿")
    private String isLodging;

    /**
     * 邀约用户ID
     */
    @NotNull(message = "请选择邀约用户")
    private Long inviteUserId;

    /**
     * 面试姓名
     */
    @NotBlank(message = "面试姓名不能为空")
    private String interviewName;

    /**
     * 民族
     */
    @NotBlank(message = "民族不能为空")
    private String nation;

    /**
     * 籍贯
     */
    @NotBlank(message = "籍贯不能为空")
    private String nativePlace;

    /**
     * 婚姻状态
     */
    @NotBlank(message = "请选择婚姻状态")
    private String maritalStatus;

    /**
     * 身份证号码
     */
    @NotBlank(message = "身份证号码不能为空")
    private String idCardNumber;

    /**
     * 年龄
     */
    @NotNull(message = "年龄不能为空")
    private Integer age;

    /**
     * 性别（1男 2女 3其他）
     */
    @NotBlank(message = "请选择性别")
    private String sex;

    /**
     * 学历类型（1初中 2高中 3专科 4本科 5研究生）
     */
    @NotBlank(message = "请选择学历类型")
    private String educationType;

    /**
     * 专业
     */
    @NotBlank(message = "专业不能为空")
    private String major;

    /**
     * 出生日期
     */
    @NotNull(message = "出生日期不能为空")
    private LocalDate birthdayDate;

    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不能为空")
    private String phone;

    /**
     * 邮箱
     */
    @NotBlank(message = "邮箱不能为空")
    private String email;

    /**
     * 户籍所在地
     */
    @NotBlank(message = "户籍所在地不能为空")
    private String registeredResidence;

    /**
     * 现住地址
     */
    @NotBlank(message = "现住地址不能为空")
    private String address;

    /**
     * 健康状态
     */
    @NotBlank(message = "请选择健康状态")
    private String healthStatus;

    /**
     * 是否有疾病（Y是 N否）
     */
    @NotBlank(message = "请选择是否有遗传病")
    private String isInfectiousDiseases;

    /**
     * 紧急联系人姓名
     */
    @NotBlank(message = "紧急联系人姓名不能为空")
    private String emergencyContactName;

    /**
     * 紧急联系人类型
     */
    @NotBlank(message = "请选择紧急联系人关系")
    private String emergencyContactType;

    /**
     * 紧急联系人电话
     */
    @NotBlank(message = "紧急联系人电话不能为空")
    private String emergencyContactPhone;

    /**
     * 是否有竞业协议（Y是 N否）
     */
    @NotBlank(message = "请选择是否有竞业协议")
    private String isNca;

    /**
     * 是否有亲属在公司（Y是 N否）
     */
    @NotBlank(message = "请选择是否有亲属在公司")
    private String isRelativesHere;

    /**
     * 是否有犯罪记录 （Y是 N否）
     */
    @NotBlank(message = "请选择是否有犯罪记录")
    private String isCriminalRecord;

    /**
     * 是否同意背景调查（Y是 N否）
     */
    @NotBlank(message = "请选择是否同意背景调查")
    private String isBackgroundCheck;

    /**
     * 最看重（1高薪 2晋升 3公司发展 4福利 5行业）
     */
    @NotBlank(message = "请选择最看重的类型")
    private String mostConcerned;


    /**
     * 学历扩展信息：
     * enrollment_date 入学日期
     * graduation_date 毕业日期
     * school 学校
     * major 专业
     * education 学历
     * degree 学位
     */
    @NotBlank(message = "请填写学历扩展信息")
    private String educationExtra;

    /**
     * 工作扩展信息：
     * employment_date 入职日期
     * resignation_date 离职日期
     * company 公司
     * position 职位
     * salary 薪资
     * certifier 证明人
     * certifier_phone 证明人电话
     */
    @NotBlank(message = "请填写工作扩展信息")
    private String workExtra;

    /**
     * 家庭扩展信息：
     * member_name 成员姓名
     * relationship 关系
     * contact_phone 联系电话
     * work 工作
     * remark 备注
     */
    @NotBlank(message = "请填写家庭扩展信息")
    private String familyExtra;

    /**
     * 面试角色 从预约面试表中匹配获取
     */
    private String interviewRole;
}
