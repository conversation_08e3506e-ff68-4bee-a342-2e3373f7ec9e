package com.yooa.system.api;

import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.constant.ServiceNameConstants;
import com.yooa.common.core.domain.R;
import com.yooa.system.api.factory.RemoteRosterFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * 花名册 - 远程调用服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteRosterService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteRosterFallbackFactory.class)
public interface RemoteRosterService {


    /**
     * 钉钉入职同步花名册信息
     *
     * @param userIds 钉钉用户ID
     * @param source  请求来源
     */
    @GetMapping("/roster/ding-talk/add/{userIds}")
    R<Integer> DingTalkAddRoster(@PathVariable("userIds") List<String> userIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 钉钉离职同步花名册信息
     *
     * @param userIds 钉钉用户ID
     * @param source  请求来源
     */
    @GetMapping("/roster/ding-talk/leave/{userIds}")
    R<Integer> DingTalkLeaveRoster(@PathVariable("userIds") List<String> userIds, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


}
