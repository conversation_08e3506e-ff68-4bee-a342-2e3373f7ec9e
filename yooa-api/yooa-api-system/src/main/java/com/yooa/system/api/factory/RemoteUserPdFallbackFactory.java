package com.yooa.system.api.factory;

import com.yooa.system.api.RemoteUserPdService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class RemoteUserPdFallbackFactory implements FallbackFactory<RemoteUserPdService> {

    @Override
    public RemoteUserPdService create(Throwable throwable) {

        log.error("用户与PD用户关联服务调用失败:{}", throwable.getMessage());

        return new RemoteUserPdService() {
        };

    }
}
