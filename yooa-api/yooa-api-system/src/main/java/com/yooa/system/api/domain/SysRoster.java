package com.yooa.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

/**
 * 花名册
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SysRoster extends BaseEntity {


    /**
     * 花名册ID
     */
    @TableId(type = IdType.AUTO)
    private Long rosterId;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 花名
     */
    private String flowerName;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 工号
     */
    private String jobNumber;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String mail;

    /**
     * 民族
     */
    private String nation;

    /**
     * 现住地址
     */
    private String address;

    /**
     * 岗位名称
     */
    private String postName;

    /**
     * 岗位职级
     */
    private String postRank;

    /**
     * 职位类型（1推广 2运营 3财务 4职能 5事业部 6平台）
     */
    private String positionType;

    /**
     * 员工角色（1会长/总经理 2部长/总监 3厅长/经理 4组长/主管 5员工）
     */
    private String employeeRole;

    /**
     * 员工类型（1全职 2兼职 3实习 4劳务派遣 5退休返聘 6劳务外包）
     */
    private String employeeType;

    /**
     * 员工状态（0试用 1在职 2离职）
     */
    private String employeeStatus;

    /**
     * 员工性别（0男 1女）
     */
    private String employeeSex;

    /**
     * 入职日期
     */
    private LocalDate employmentDate;

    /**
     * 离职日期
     */
    private LocalDate resignationDate;

    /**
     * 试用期（1无试用期 21个月 32个月 43个月 54个月 65个月 76个月 8其他）
     */
    private String probationPeriod;

    /**
     * 计划转正日期
     */
    private LocalDate planFormalDate;

    /**
     * 实际转正日期
     */
    private LocalDate becomeFormalDate;

    /**
     * 身份证号码
     */
    private String idCardNumber;

    /**
     * 身份证姓名
     */
    private String idCardName;

    /**
     * 身份证地址
     */
    private String idCardAddress;

    /**
     * 身份证有效日期
     */
    private LocalDate idCardValidityDate;

    /**
     * 身份证出生日期
     */
    private LocalDate idCardBirthdayDate;

    /**
     * 公历/农历生日
     */
    private LocalDate birthdayDate;

    /**
     * 生日类型（1公历 2农历）
     */
    private String birthdayType;

    /**
     * 婚姻状态（1未婚 2已婚 3其他）
     */
    private String maritalStatus;

    /**
     * 第一次参加工作时间
     */
    private LocalDate firstWorkingDate;

    /**
     * 户籍所在地类型（1本地城镇 2本地农村 3外地城镇（省内） 4外地农村（省内） 5外地城镇（省外） 6外地农村（省外））
     */
    private String registeredResidenceType;

    /**
     * 政治特征类型（1团员 2党员 3群众 4其他）
     */
    private String politicalFeaturesType;

    /**
     * 学历类型（9小学 8初中 1高中 2中专 3大专 4本科 5硕士 6博士 7其他）
     */
    private String educationalType;

    /**
     * 毕业学校
     */
    private String graduationSchool;

    /**
     * 毕业专业
     */
    private String graduationMajor;

    /**
     * 毕业日期
     */
    private LocalDate graduationDate;

    /**
     * 紧急联系人姓名
     */
    private String emergencyContactName;

    /**
     * 紧急联系人类型（1父母 2配偶 3子女 4亲友 5其他）
     */
    private String emergencyContactType;

    /**
     * 紧急联系人电话
     */
    private String emergencyContactPhone;

    /**
     * 工资发放主体
     */
    private String salaryPaymenSubject;

    /**
     * 银行卡号
     */
    private String bankCardNumber;

    /**
     * 银行开户行
     */
    private String bankOpening;

    /**
     * 支付宝账号
     */
    private String alipayAccount;

    /**
     * 合同归属主体
     */
    private String contractOwnershipSubject;

    /**
     * 合同类型（1固定期限合同 2无固定期限合同 3实习协议 4劳务协议 5劳务派遣合同 6返聘协议 7短期劳动合同 8其他）
     */
    private String contractType;

    /**
     * 首次合同开始日期
     */
    private LocalDate firstContractBeginDate;

    /**
     * 首次合同结束日期
     */
    private LocalDate firstContractEndDate;

    /**
     * 现合同开始日期
     */
    private LocalDate nowContractBeginDate;

    /**
     * 现合同结束日期
     */
    private LocalDate nowContractEndDate;

    /**
     * 续签次数
     */
    private Integer renewalNumber;

    /**
     * OA用户ID
     */
    private Long oaUserId;

    /**
     * OA子用户id 临时用
     */
    private Long subOaUserId;

    /**
     * 招聘/推荐用户ID
     */
    private Long recommenUserId;

    /**
     * 领导用户ID
     */
    private Long leaderUserId;

    /**
     * 钉钉用户ID
     */
    private String dingUserId;

    /**
     * 面试记录ID
     */
    private Long interviewId;

    private String rosterType;

}