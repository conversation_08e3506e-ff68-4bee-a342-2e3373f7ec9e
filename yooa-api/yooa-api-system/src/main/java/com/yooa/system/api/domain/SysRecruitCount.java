package com.yooa.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 招聘计划数量统计表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysRecruitCount extends BaseEntity {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long Id;

    /**
     * 流程id
     */
    private Long processId;

    /**
     * 月份
     */
    private String month;

    /**
     * 总数
     */
    private Long total;

    /**
     * 专员总数
     */
    private Long commissionerTotal;

    /**
     * 管理总数
     */
    private Long manageTotal;

    /**
     * 专员可分配数量
     */
    private Long commissionerRemaining;

    /**
     * 管理可分配数量
     */
    private Long manageRemaining;

}
