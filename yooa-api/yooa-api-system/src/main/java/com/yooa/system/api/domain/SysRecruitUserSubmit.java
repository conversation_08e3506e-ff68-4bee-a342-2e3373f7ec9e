package com.yooa.system.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.yooa.common.mybatis.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 招聘计划发布表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysRecruitUserSubmit extends BaseEntity {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long Id;


    /**
     * 流程ID
     */
    private Long processId;

    /**
     * 上传时间
     */
    private String uploadTime;

    /**
     * 所属团队
     */
    private String demandDepartment;

    /**
     * BP姓名
     */
    private String name;

    /**
     * BP userId
     */
    private Long userId;

    /**
     * 专员数量
     */
    private Long commissioner;

    /**
     * 管理数量
     */
    private Long manage;

    /**
     * 计划归属类型
     */
    private int recruitType;

    /**
     * 计划招聘时间
     */
    private String hireDate;

}
