package com.yooa.system.api.domain.dto;

import com.yooa.system.api.domain.SysRoster;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

@EqualsAndHashCode(callSuper = true)
@Data
public class RosterSaveDto extends SysRoster {

    /**
     * 真实姓名
     */
    @NotBlank(message = "真实姓名不能为空")
    private String realName;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    private String phone;

    /**
     * 身份证号码
     */
    @NotBlank(message = "身份证号码不能为空")
    private String idCardNumber;

}