package com.yooa.system.api.domain.query;

import com.yooa.common.mybatis.base.QueryEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
public class OperLogQuery extends QueryEntity {

    /**
     * 操作模块
     */
    private String title;

    /**
     * 业务类型（0其它 1新增 2修改 3删除）
     */
    private Integer businessType;

    /**
     * 业务类型组
     */
    private Integer[] businessTypes;

    /**
     * 操作人员
     */
    private String operName;

    /**
     * 操作地址
     */
    private String operIp;

    /**
     * 操作状态（0正常 1异常）
     */
    private Integer status;
    
    private LocalDate[] operDate;
}
