package com.yooa.cmf.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 渠道后台---推广员和用户关联表
 */
@TableName(value = "cmf_agent_admin_and_user")
@Data
public class CmfAgentAdminAndUser implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 管理员ID
     */
    @TableField(value = "admin_id")
    private Long adminId;
    /**
     * 与管理员绑定的用户ID
     */
    @TableField(value = "user_id")
    private Long userId;
    /**
     * vip客服id
     */
    @TableField(value = "serve_id")
    private Long serveId;
    /**
     * 绑定奖励领取0=未领取 1=以领取
     */
    @TableField(value = "dress_type")
    private Integer dressType;
    /**
     * 绑定方式：0=链接绑定，1=注册绑定，2=手动绑定
     */
    @TableField(value = "type")
    private Integer type;
    /**
     * 创建时间
     */
    @TableField(value = "add_time")
    private Integer addTime;
    /**
     * 奖励领取时间
     */
    @TableField(value = "receive_time")
    private Integer receiveTime;
    /**
     * 渠道id cms专用
     */
    @TableField(value = "channel_id")
    private Integer channelId;
    /**
     * 绑定类型 2:推广 3:运营
     */
    @TableField(value = "form")
    private Integer form;
}