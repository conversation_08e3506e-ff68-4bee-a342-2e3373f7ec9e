package com.yooa.cmf.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 *
 */
@TableName(value = "cmf_department_binding")
@Data
public class CmfDepartmentBinding implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 推广工会id
     */
    @TableField(value = "expand_department_id")
    private Integer expandDepartmentId;
    /**
     * 主播公会id
     */
    @TableField(value = "operating_department_id")
    private String operatingDepartmentId;
    /**
     * 操作人
     */
    @TableField(value = "admin")
    private String admin;
    /**
     * 添加时间
     */
    @TableField(value = "addtime")
    private Integer addtime;
}