package com.yooa.cmf.api;

import com.yooa.cmf.api.domain.vo.ConvertCustomerJoinAnchorVo;
import com.yooa.cmf.api.factory.RemoteCmfUserJoinLiveFallbackFactory;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.constant.ServiceNameConstants;
import com.yooa.common.core.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;


@FeignClient(contextId = "remoteCmfUserJoinLiveService", value = ServiceNameConstants.CMF_SERVICE, fallbackFactory = RemoteCmfUserJoinLiveFallbackFactory.class)
public interface RemoteCmfUserJoinLiveService {

    String ClassPath = "/cmf/user/live";

    /**
     * 获取客户主播交接记录列表 - 大于自增ID后的数据
     */
    @GetMapping(ClassPath + "/list/gt/{id}")
    R<List<ConvertCustomerJoinAnchorVo>> listByGtId(@PathVariable("id") Long id, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
    
    /**
     * 获取客户主播交接记录列表 - 状态变更的数据
     */
    @GetMapping(ClassPath + "/list/status-change/{ids}")
    R<List<ConvertCustomerJoinAnchorVo>> listByStatusChange(@PathVariable("ids") List<Long> ids, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
