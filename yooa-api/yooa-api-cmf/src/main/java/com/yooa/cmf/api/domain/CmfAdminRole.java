package com.yooa.cmf.api.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 *
 */
@TableName(value = "cmf_admin_role")
@Data
public class CmfAdminRole implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    /**
     * 父组别
     */
    @TableField(value = "pid")
    private Integer pid;
    /**
     * 组名
     */
    @TableField(value = "name")
    private String name;
    /**
     * 规则ID
     */
    @TableField(value = "rules")
    private String rules;
    /**
     * 创建时间
     */
    @TableField(value = "createtime")
    private Integer createtime;
    /**
     * 更新时间
     */
    @TableField(value = "updatetime")
    private Integer updatetime;
    /**
     * 状态
     */
    @TableField(value = "status")
    private String status;
}