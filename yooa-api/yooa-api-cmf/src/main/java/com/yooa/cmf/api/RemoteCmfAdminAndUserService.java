package com.yooa.cmf.api;

import com.yooa.cmf.api.domain.CmfAgentAdminAndUser;
import com.yooa.cmf.api.factory.RemoteCmfAdminAndUserFallbackFactory;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.constant.ServiceNameConstants;
import com.yooa.common.core.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(contextId = "remoteCmfAdminAndUserService", value = ServiceNameConstants.CMF_SERVICE, fallbackFactory = RemoteCmfAdminAndUserFallbackFactory.class)
public interface RemoteCmfAdminAndUserService {

    String ClassPath = "/cmf/admin/user";

    /**
     * 查询推广和用户的绑定记录
     *
     * @param date 完成时间
     */
    @GetMapping(ClassPath + "/synchronizationSignBind")
    R<List<CmfAgentAdminAndUser>> synchronizationSignBind(@RequestParam("date") String date, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 查询客户最新的绑定记录
     *
     * @param customerId 客户ID
     */
    @GetMapping(ClassPath + "/{customerId}")
    R<CmfAgentAdminAndUser> getByCustomerId(@PathVariable("customerId") Long customerId, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
