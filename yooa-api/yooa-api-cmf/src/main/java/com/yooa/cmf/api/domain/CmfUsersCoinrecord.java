package com.yooa.cmf.api.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * 用户消费表
 *
 * @TableName cmf_users_coinrecord
 */
@Data
public class CmfUsersCoinrecord implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    private Long id;
    /**
     * 收支类型 "expend"=>消费,"income"=>收入
     */
    private String type;
    /**
     * 收支行为
     * "sendgift"=>"赠送礼物",
     * "sendbarrage"=>"弹幕",
     * "loginbonus"=>"登录奖励",
     * "buyvip"=>"购买VIP",
     * "buycar"=>"购买坐骑",
     * "buyliang"=>"购买靓号",
     * 'sendred'=>'发送红包',
     * 'robred'=>'抢红包',
     * 'buyguard'=>'开通守护',
     * 'reg_reward'=>'注册奖励',
     * 'luckgift'=>'礼物中奖',
     * 'jackpotwin'=>'奖池中奖',
     * 'uname'=>'修改名字',
     * 'upvideo'=>'上传视频',
     * 'videolikes'=>'上传视频点赞',
     * 'buynoble'=>'开通贵族'，
     * 'buyavatar'=>'购买头像框',
     * 'community'=>'社区动态送礼',
     * 'draw'=>'抽奖',
     * 'prop'=>'购买道具',
     * 'turntable' => '整蛊转盘',
     * 'blind_box' => '盲盒',
     * 'red_packet'=>'人氣紅包',
     * 'fans_exclusive_pack'=>'粉絲團禮包',
     * 'fans_pack_send'=>'粉絲團包裹贈送禮物',
     */
    private String action;
    /**
     * 用户ID
     */
    private Long uid;
    /**
     * 消费对象ID
     */
    private Long touid;
    /**
     * 行为对应ID
     */
    private Long giftid;
    /**
     * 社区动态礼物消费 社区动态的ID
     */
    private Long dynamicId;
    /**
     * 数量
     */
    private Long giftcount;
    /**
     * 总钻石价
     */
    private Integer totalcoin;
    /**
     * 总消费金币
     */
    private Long totalgold;
    /**
     * 总元宝价
     */
    private Integer totalyuanbao;
    /**
     * 直播标识
     */
    private Integer showid;
    /**
     * 添加时间
     */
    private Integer addtime;
    /**
     * 庄家ID
     */
    private Long gameBanker;
    /**
     * 游戏类型
     */
    private Integer gameAction;
    /**
     * 标识，1表示热门礼物，2表示守护礼物
     */
    private Integer mark;
    /**
     * 是否为续费礼物 0否 1是
     */
    private Integer renew;
    /**
     * 主播收益钻石
     */
    private Integer anchorProfitCoin;
    /**
     * 官方收益钻石
     */
    private Integer officialProfitCoin;
    /**
     * 是否来源与心愿单消费礼物 1是0不是
     */
    private Integer isWishList;
    /**
     * 盲盒类型 1.迷你盲盒 2.典藏盲盒 3.PD盲盒
     */
    private Integer blindBoxType;
    /**
     * 是否包裹送出礼物 1.是 0.否
     */
    private Integer isPack;
    /**
     * 运营ID
     */
    private Long adminId;
}