package com.yooa.cmf.api.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 推广每日目标完成统计表      返回类
 */
@Data
public class ExtendTargetProgressVo implements Serializable {
    /**
     *
     */
    private Integer id;

    /**
     * 日期
     */
    private LocalDate date;

    /**
     * 推广id
     */
    private Integer agentAdminId;

    /**
     * 优质(推广需要,客服不需要)
     */
    private Integer qualityUsers = 0;

    /**
     * 接收数
     */
    private Integer receive = 0;

    /**
     * 接收数vip/接粉数
     */
    private Integer receiveVip = 0;

    /**
     * 业绩/新增
     */
    private BigDecimal fortyFiveDays = BigDecimal.ZERO;

    /**
     * 汇总业绩
     */
    private BigDecimal realProfit = BigDecimal.ZERO;

    /**
     * 充值用户数量
     */
    private Integer chargeUser = 0;
}