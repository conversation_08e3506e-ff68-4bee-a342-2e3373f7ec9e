package com.yooa.cmf.api;

import com.yooa.cmf.api.domain.CmfAgentAdmin;
import com.yooa.cmf.api.domain.dto.CmfAgentAdminDto;
import com.yooa.cmf.api.factory.RemoteCmfAdminFallbackFactory;
import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.constant.ServiceNameConstants;
import com.yooa.common.core.domain.R;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@FeignClient(contextId = "remoteCmfAdminService", value = ServiceNameConstants.CMF_SERVICE, fallbackFactory = RemoteCmfAdminFallbackFactory.class)
public interface RemoteCmfAdminService {

    /**
     * 查询PD用户数据
     *
     * @param cmfAgentAdminDto 搜索条件
     * @param source           请求来源
     */
    @PostMapping("/cmf/admin/list")
    R<List<CmfAgentAdmin>> list(@RequestBody CmfAgentAdminDto cmfAgentAdminDto, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取管理员列表
     */
    @GetMapping("/cmf/admin/list/{ids}")
    R<List<CmfAgentAdmin>> listByIds(@PathVariable("ids") List<Long> ids, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取管理员信息
     */
    @GetMapping("/cmf/admin/{id}")
    R<CmfAgentAdmin> getById(@PathVariable("id") Long id, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取推广信息
     */
    @GetMapping("/cmf/admin/extend/{id}")
    R<CmfAgentAdmin> getExtendById(@PathVariable("id") Long id, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取客服信息
     */
    @GetMapping("/cmf/admin/serve/{id}")
    R<CmfAgentAdmin> getServeById(@PathVariable("id") Long id, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取运营信息
     */
    @GetMapping("/cmf/admin/operate/{id}")
    R<CmfAgentAdmin> getOperateById(@PathVariable("id") Long id, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

}
