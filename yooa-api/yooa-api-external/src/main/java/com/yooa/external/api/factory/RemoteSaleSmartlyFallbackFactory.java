package com.yooa.external.api.factory;

import com.alibaba.fastjson2.JSONObject;
import com.yooa.common.core.domain.R;
import com.yooa.external.api.RemoteSaleSmartlyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;


/**
 * 轮询
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteSaleSmartlyFallbackFactory implements FallbackFactory<RemoteSaleSmartlyService> {
    @Override
    public RemoteSaleSmartlyService create(Throwable throwable) {

        log.error("SaleSmartly 服务调用失败：{}", throwable.getMessage());

        return new RemoteSaleSmartlyService() {

            @Override
            public R<JSONObject> getMemberList(String project_id, int page, int page_size, String updated_time, String signature) {
                return R.fail("获取团队管理成员失败：" + throwable.getMessage());
            }

            @Override
            public R<JSONObject> getContactList(String project_id, int page_size, String ids, String updated_time, String signature) {
                return R.fail("获取客户列表失败：" + throwable.getMessage());
            }
        };
    }
}
