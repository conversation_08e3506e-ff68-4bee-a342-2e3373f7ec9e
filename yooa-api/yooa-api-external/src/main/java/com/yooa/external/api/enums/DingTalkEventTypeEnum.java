package com.yooa.external.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 钉钉推送事件类型枚举
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum DingTalkEventTypeEnum {
    /**
     * 通讯录用户新增
     */
    ADD_USER("user_add_org"),
    /**
     * 通讯录用户离职
     */
    LEAVE_USER("user_leave_org"),
    /**
     * 通讯录用户更新
     */
    MODIFY_USER("user_modify_org"),
    ;
    /**
     * 钉钉事件类型
     */
    private final String eventType;

    public static DingTalkEventTypeEnum getByEventType(String eventType) {
        for (DingTalkEventTypeEnum dingTalkEventTypeEnum : DingTalkEventTypeEnum.values()) {
            if (dingTalkEventTypeEnum.getEventType().equals(eventType)) {
                return dingTalkEventTypeEnum;
            }
        }
        return null;
    }
}
