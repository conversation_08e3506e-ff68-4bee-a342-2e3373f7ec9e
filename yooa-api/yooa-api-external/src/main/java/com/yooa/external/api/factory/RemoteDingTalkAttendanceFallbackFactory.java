package com.yooa.external.api.factory;

import com.yooa.common.core.domain.R;
import com.yooa.external.api.RemoteDingTalkAttendanceService;
import com.yooa.external.api.request.DingTalkAttendScheduleRequest;
import com.yooa.external.api.request.DingTalkAttendUserCheckRequest;
import com.yooa.external.api.response.DingTalkAttendClassRes;
import com.yooa.external.api.response.DingTalkAttendGroupRes;
import com.yooa.external.api.response.DingTalkAttendScheduleRes;
import com.yooa.external.api.response.DingTalkAttendUserCheckRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 钉钉用户 - 远程调用服务降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteDingTalkAttendanceFallbackFactory implements FallbackFactory<RemoteDingTalkAttendanceService> {
    @Override
    public RemoteDingTalkAttendanceService create(Throwable throwable) {

        log.error("钉钉服务调用失败：{}", throwable.getMessage());

        return new RemoteDingTalkAttendanceService() {

            @Override
            public R<List<DingTalkAttendGroupRes>> syncgAttendGroup(String source) {
                return R.fail("获取考勤组数据失败：" + throwable.getMessage());
            }

            @Override
            public R<List<DingTalkAttendClassRes>> syncgAttendClass(String source) {
                return R.fail("获取排班数据失败：" + throwable.getMessage());
            }

            @Override
            public R<List<DingTalkAttendScheduleRes>> syncgAttendSchedule(DingTalkAttendScheduleRequest dingTalkAttendScheduleRequest, String source) {
                return R.fail("获取用户排班数据失败：" + throwable.getMessage());
            }

            @Override
            public R<List<DingTalkAttendUserCheckRes>> syncgAttendUserCheck(DingTalkAttendUserCheckRequest dingTalkAttendUserCheckRequest, String source) {
                return R.fail("获取用户打卡数据失败：" + throwable.getMessage());
            }
        };
    }
}
