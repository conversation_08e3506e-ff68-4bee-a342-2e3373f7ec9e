package com.yooa.external.api;

import com.yooa.common.core.constant.SecurityConstants;
import com.yooa.common.core.constant.ServiceNameConstants;
import com.yooa.common.core.domain.R;
import com.yooa.external.api.factory.RemoteDingTalkUserFallbackFactory;
import com.yooa.external.api.request.DingTalkAttendScheduleRequest;
import com.yooa.external.api.request.DingTalkAttendUserCheckRequest;
import com.yooa.external.api.response.DingTalkAttendClassRes;
import com.yooa.external.api.response.DingTalkAttendGroupRes;
import com.yooa.external.api.response.DingTalkAttendScheduleRes;
import com.yooa.external.api.response.DingTalkAttendUserCheckRes;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;

import java.util.List;

/**
 * 钉钉考勤 - 远程调用服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteDingTalkAttendanceService", value = ServiceNameConstants.EXTERNAL_SERVICE, fallbackFactory = RemoteDingTalkUserFallbackFactory.class)
public interface RemoteDingTalkAttendanceService {


    /**
     * 获取钉钉考勤组数据
     */
    @GetMapping("/ding-talk/sync/attendance/group")
    R<List<DingTalkAttendGroupRes>> syncgAttendGroup(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 获取钉钉排班数据
     */
    @GetMapping("/ding-talk/sync/attendance/class")
    R<List<DingTalkAttendClassRes>> syncgAttendClass(@RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 获取钉钉排班数据
     */
    @PostMapping("/ding-talk/sync/attendance/schedule")
    R<List<DingTalkAttendScheduleRes>> syncgAttendSchedule(@RequestBody DingTalkAttendScheduleRequest dingTalkAttendScheduleRequest, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


    /**
     * 获取钉钉排班数据
     */
    @PostMapping("/ding-talk/sync/attendance/checkTime")
    R<List<DingTalkAttendUserCheckRes>> syncgAttendUserCheck(@RequestBody DingTalkAttendUserCheckRequest dingTalkAttendUserCheckRequest, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);


}
