package com.yooa.external.api.factory;

import com.alibaba.fastjson2.JSONObject;
import com.yooa.common.core.domain.R;
import com.yooa.external.api.RemoteSaleSmartlyPoyoService;
import com.yooa.external.api.response.SaleSmartlyPoyoManageRes;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;


/**
 * 轮询 - 聊天ai训练
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteSaleSmartlyPoyoFallbackFactory implements FallbackFactory<RemoteSaleSmartlyPoyoService> {
    @Override
    public RemoteSaleSmartlyPoyoService create(Throwable throwable) {

        log.error("SaleSmartly 聊天ai训练失败：{}", throwable.getMessage());

        return new RemoteSaleSmartlyPoyoService() {
            @Override
            public void saveaidata(SaleSmartlyPoyoManageRes saleSmartlyPoyoManageRes) {

            }
        };
    }
}
