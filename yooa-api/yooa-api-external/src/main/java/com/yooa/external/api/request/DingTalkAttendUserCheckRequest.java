package com.yooa.external.api.request;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户考勤打卡
 *
 * <AUTHOR>
 */
@Data
public class DingTalkAttendUserCheckRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * userId,userId
     */
    private List<String> userIdList;
    /**
     * 考勤组ID
     */
    private String startDate;
    /**
     * 排班时间
     */
    private String endDate;


}
